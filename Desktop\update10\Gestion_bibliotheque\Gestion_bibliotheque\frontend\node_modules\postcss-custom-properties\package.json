{"name": "postcss-custom-properties", "description": "Use Custom Properties Queries in CSS", "version": "12.1.11", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON>"], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist", "index.d.ts"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2"}, "devDependencies": {"postcss-import": "^15.0.0"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && node .tape.cjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-custom-properties#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-custom-properties"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "csswg", "custom", "declarations", "postcss", "postcss-plugin", "properties", "specification", "variables", "vars", "w3c"], "csstools": {"exportName": "postcssCustomProperties", "humanReadableName": "PostCSS Custom Properties"}, "volta": {"extends": "../../package.json"}}