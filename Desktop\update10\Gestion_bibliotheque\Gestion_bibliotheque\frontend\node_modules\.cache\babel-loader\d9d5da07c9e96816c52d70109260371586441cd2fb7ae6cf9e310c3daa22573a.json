{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async token => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const response = await api.get('/api/utilisateurs/users/me/');\n      setCurrentUser(response.data);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n      setAuthError('Impossible de récupérer les informations utilisateur');\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (token) {\n          const success = await fetchUserInfo(token);\n          if (!success) {\n            localStorage.removeItem('token');\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, [fetchUserInfo]);\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n      const {\n        user,\n        profile,\n        token\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n      setCurrentUser({\n        ...user,\n        profile\n      });\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Erreur de connexion:', error);\n      setAuthError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Erreur de connexion');\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Erreur de connexion'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const {\n        token,\n        user\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response5, _error$response5$data;\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && _error$response4.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n      return {\n        success: false,\n        message: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || 'Erreur d\\'inscription'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data, _error$response7, _error$response7$data;\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"MmdmBtoNyEXLJ8ErwOEOz7sUNs4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useCallback", "axios", "api", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "authError", "setAuthError", "fetchUserInfo", "token", "response", "get", "data", "error", "console", "checkAuth", "localStorage", "getItem", "success", "removeItem", "login", "username", "password", "post", "user", "profile", "setItem", "defaults", "headers", "common", "message", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "register", "userData", "autoLogin", "_error$response3", "_error$response3$data", "_error$response4", "_error$response5", "_error$response5$data", "errorMessages", "field", "Array", "isArray", "push", "join", "length", "logout", "updateProfile", "put", "_error$response6", "_error$response6$data", "_error$response7", "_error$response7$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async (token) => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const response = await api.get('/api/utilisateurs/users/me/');\n\n      setCurrentUser(response.data);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n      setAuthError('Impossible de récupérer les informations utilisateur');\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (token) {\n          const success = await fetchUserInfo(token);\n\n          if (!success) {\n            localStorage.removeItem('token');\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [fetchUserInfo]);\n\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n\n      const { user, profile, token } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n\n      setCurrentUser({ ...user, profile });\n      setIsAuthenticated(true);\n\n      return { success: true };\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur de connexion');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur de connexion'\n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const { token, user } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if (error.response?.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur d\\'inscription'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n\n      return { success: true, data: response.data };\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMU,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMT,UAAU,CAACO,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMsB,aAAa,GAAGnB,WAAW,CAAC,MAAOoB,KAAK,IAAK;IACjD,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMnB,GAAG,CAACoB,GAAG,CAAC,6BAA6B,CAAC;MAE7DV,cAAc,CAACS,QAAQ,CAACE,IAAI,CAAC;MAC7BT,kBAAkB,CAAC,IAAI,CAAC;MACxBI,YAAY,CAAC,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpFN,YAAY,CAAC,sDAAsD,CAAC;MACpE,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFV,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,KAAK,GAAGO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAIR,KAAK,EAAE;UACT,MAAMS,OAAO,GAAG,MAAMV,aAAa,CAACC,KAAK,CAAC;UAE1C,IAAI,CAACS,OAAO,EAAE;YACZF,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;YAChChB,kBAAkB,CAAC,KAAK,CAAC;YACzBF,cAAc,CAAC,IAAI,CAAC;UACtB;QACF;MACF,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDG,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;QAChChB,kBAAkB,CAAC,KAAK,CAAC;QACzBF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDU,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;EAEnB,MAAMY,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACFf,YAAY,CAAC,IAAI,CAAC;MAElB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,IAAI,CAAC,0BAA0B,EAAE;QAC1DF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEE,IAAI;QAAEC,OAAO;QAAEhB;MAAM,CAAC,GAAGC,QAAQ,CAACE,IAAI;MAE9C,IAAIH,KAAK,EAAE;QACTO,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;QACpCnB,KAAK,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;QACjElB,GAAG,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;MACjE,CAAC,MAAM;QACLK,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAC;QAC7CN,YAAY,CAAC,+CAA+C,CAAC;QAC7D,OAAO;UACLW,OAAO,EAAE,KAAK;UACdY,OAAO,EAAE;QACX,CAAC;MACH;MAEA7B,cAAc,CAAC;QAAE,GAAGuB,IAAI;QAAEC;MAAQ,CAAC,CAAC;MACpCtB,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEe,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAkB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdpB,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CN,YAAY,CAAC,EAAAwB,eAAA,GAAAlB,KAAK,CAACH,QAAQ,cAAAqB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBnB,IAAI,cAAAoB,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,qBAAqB,CAAC;MACnE,OAAO;QACLjB,OAAO,EAAE,KAAK;QACdY,OAAO,EAAE,EAAAG,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAACgC,IAAI,CAAC,6BAA6B,EAAEc,QAAQ,CAAC;;MAExE;MACA,MAAM;QAAE5B,KAAK;QAAEe;MAAK,CAAC,GAAGd,QAAQ,CAACE,IAAI;MAErC,IAAIH,KAAK,EAAE;QACTO,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;QACpCnB,KAAK,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;QACjElB,GAAG,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;QAE/DR,cAAc,CAACuB,IAAI,CAAC;QACpBrB,kBAAkB,CAAC,IAAI,CAAC;MAC1B;MAEA,OAAO;QACLe,OAAO,EAAE,IAAI;QACbY,OAAO,EAAEpB,QAAQ,CAACE,IAAI,CAACuB,MAAM;QAC7BG,SAAS,EAAE,CAAC,CAAC7B,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CN,YAAY,CAAC,EAAAgC,gBAAA,GAAA1B,KAAK,CAACH,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3B,IAAI,cAAA4B,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,uBAAuB,CAAC;;MAErE;MACA,IAAI,CAAAM,gBAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,eAAdA,gBAAA,CAAgB7B,IAAI,IAAI,OAAOC,KAAK,CAACH,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QACnE,MAAMgC,aAAa,GAAG,EAAE;QACxB,KAAK,MAAMC,KAAK,IAAIhC,KAAK,CAACH,QAAQ,CAACE,IAAI,EAAE;UACvC,IAAIkC,KAAK,CAACC,OAAO,CAAClC,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACiC,KAAK,CAAC,CAAC,EAAE;YAC7CD,aAAa,CAACI,IAAI,CAAC,GAAGH,KAAK,KAAKhC,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACiC,KAAK,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UAC1E;QACF;QAEA,IAAIL,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;UAC5B,OAAO;YACLhC,OAAO,EAAE,KAAK;YACdY,OAAO,EAAEc,aAAa,CAACK,IAAI,CAAC,IAAI;UAClC,CAAC;QACH;MACF;MAEA,OAAO;QACL/B,OAAO,EAAE,KAAK;QACdY,OAAO,EAAE,EAAAY,gBAAA,GAAA7B,KAAK,CAACH,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsBR,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMgB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM5D,GAAG,CAACgC,IAAI,CAAC,2BAA2B,CAAC;IAC7C,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRZ,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,KAAK,CAAC;MACzBI,YAAY,CAAC,IAAI,CAAC;MAClBS,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO7B,KAAK,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACrD,OAAOtC,GAAG,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMuB,aAAa,GAAG,MAAOf,QAAQ,IAAK;IACxC,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAAC8D,GAAG,CAAC,4BAA4B,EAAEhB,QAAQ,CAAC;;MAEtE;MACA,MAAM7B,aAAa,CAACQ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;MAElD,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEN,IAAI,EAAEF,QAAQ,CAACE;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAyC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd3C,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEN,YAAY,CAAC,EAAA+C,gBAAA,GAAAzC,KAAK,CAACH,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBpB,MAAM,KAAI,yCAAyC,CAAC;MACvF,OAAO;QACLjB,OAAO,EAAE,KAAK;QACdY,OAAO,EAAE,EAAA0B,gBAAA,GAAA3C,KAAK,CAACH,QAAQ,cAAA8C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBtB,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMuB,KAAK,GAAG;IACZ1D,WAAW;IACXE,eAAe;IACfE,OAAO;IACPE,SAAS;IACTc,KAAK;IACLgB,QAAQ;IACRe,MAAM;IACNC;EACF,CAAC;EAED,oBACE3D,OAAA,CAACC,WAAW,CAACiE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5D,QAAA,EAChCA;EAAQ;IAAA8D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAChE,GAAA,CA/LWF,YAAY;AAAAmE,EAAA,GAAZnE,YAAY;AAAA,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}