{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Gérer les erreurs d'authentification (401)\n  if (error.response && error.response.status === 401) {\n    // Supprimer le token et rediriger vers la page de connexion\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n\n  // Gérer les erreurs de permission (403)\n  if (error.response && error.response.status === 403) {\n    console.error('Erreur de permission:', error.response.data);\n  }\n  return Promise.reject(error);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get('/livres/livres/', {\n    params\n  }),\n  getById: id => api.get(`/livres/livres/${id}/`),\n  create: data => api.post('/livres/livres/', data),\n  update: (id, data) => api.put(`/livres/livres/${id}/`, data),\n  delete: id => api.delete(`/livres/livres/${id}/`),\n  emprunter: id => api.post(`/livres/livres/${id}/emprunter/`),\n  reserver: id => api.post(`/livres/livres/${id}/reserver/`),\n  getRecommendations: params => api.get('/livres/livres/recommendations/', {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`/livres/livres/${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get('/livres/ebooks/', {\n    params\n  }),\n  getById: id => api.get(`/livres/ebooks/${id}/`),\n  create: data => api.post('/livres/ebooks/', data),\n  update: (id, data) => api.put(`/livres/ebooks/${id}/`, data),\n  delete: id => api.delete(`/livres/ebooks/${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get('/livres/categories/'),\n  getById: id => api.get(`/livres/categories/${id}/`),\n  create: data => api.post('/livres/categories/', data),\n  update: (id, data) => api.put(`/livres/categories/${id}/`, data),\n  delete: id => api.delete(`/livres/categories/${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get('/livres/emprunts/'),\n  getById: id => api.get(`/livres/emprunts/${id}/`),\n  retourner: id => api.post(`/livres/emprunts/${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get('/livres/reservations/'),\n  getById: id => api.get(`/livres/reservations/${id}/`),\n  annuler: id => api.delete(`/livres/reservations/${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get('/utilisateurs/profiles/me/'),\n  updateProfile: data => api.put('/utilisateurs/profiles/me/', data),\n  getNotifications: () => api.get('/utilisateurs/notifications/'),\n  markNotificationRead: id => api.post(`/utilisateurs/notifications/${id}/read/`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href", "console", "livresAPI", "getAll", "params", "get", "getById", "id", "post", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "categoriesAPI", "empruntsAPI", "retourner", "reservationsAPI", "annuler", "utilisateursAPI", "getProfile", "updateProfile", "getNotifications", "markNotificationRead"], "sources": ["C:/Users/<USER>/Desktop/projetMAI/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Gérer les erreurs d'authentification (401)\n    if (error.response && error.response.status === 401) {\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n\n    // Gérer les erreurs de permission (403)\n    if (error.response && error.response.status === 403) {\n      console.error('Erreur de permission:', error.response.data);\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get('/livres/livres/', { params }),\n  getById: (id) => api.get(`/livres/livres/${id}/`),\n  create: (data) => api.post('/livres/livres/', data),\n  update: (id, data) => api.put(`/livres/livres/${id}/`, data),\n  delete: (id) => api.delete(`/livres/livres/${id}/`),\n  emprunter: (id) => api.post(`/livres/livres/${id}/emprunter/`),\n  reserver: (id) => api.post(`/livres/livres/${id}/reserver/`),\n  getRecommendations: (params) => api.get('/livres/livres/recommendations/', { params }),\n  getSimilar: (id, params) => api.get(`/livres/livres/${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get('/livres/ebooks/', { params }),\n  getById: (id) => api.get(`/livres/ebooks/${id}/`),\n  create: (data) => api.post('/livres/ebooks/', data),\n  update: (id, data) => api.put(`/livres/ebooks/${id}/`, data),\n  delete: (id) => api.delete(`/livres/ebooks/${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get('/livres/categories/'),\n  getById: (id) => api.get(`/livres/categories/${id}/`),\n  create: (data) => api.post('/livres/categories/', data),\n  update: (id, data) => api.put(`/livres/categories/${id}/`, data),\n  delete: (id) => api.delete(`/livres/categories/${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get('/livres/emprunts/'),\n  getById: (id) => api.get(`/livres/emprunts/${id}/`),\n  retourner: (id) => api.post(`/livres/emprunts/${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get('/livres/reservations/'),\n  getById: (id) => api.get(`/livres/reservations/${id}/`),\n  annuler: (id) => api.delete(`/livres/reservations/${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get('/utilisateurs/profiles/me/'),\n  updateProfile: (data) => api.put('/utilisateurs/profiles/me/', data),\n  getNotifications: () => api.get('/utilisateurs/notifications/'),\n  markNotificationRead: (id) => api.post(`/utilisateurs/notifications/${id}/read/`),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;EACvC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnD;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;;EAEA;EACA,IAAIR,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnDK,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;EAC7D;EAEA,OAAOG,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC,iBAAiB,EAAE;IAAED;EAAO,CAAC,CAAC;EAC1DE,OAAO,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,kBAAkBE,EAAE,GAAG,CAAC;EACjD7B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAAC+B,IAAI,CAAC,iBAAiB,EAAElB,IAAI,CAAC;EACnDmB,MAAM,EAAEA,CAACF,EAAE,EAAEjB,IAAI,KAAKb,GAAG,CAACiC,GAAG,CAAC,kBAAkBH,EAAE,GAAG,EAAEjB,IAAI,CAAC;EAC5DqB,MAAM,EAAGJ,EAAE,IAAK9B,GAAG,CAACkC,MAAM,CAAC,kBAAkBJ,EAAE,GAAG,CAAC;EACnDK,SAAS,EAAGL,EAAE,IAAK9B,GAAG,CAAC+B,IAAI,CAAC,kBAAkBD,EAAE,aAAa,CAAC;EAC9DM,QAAQ,EAAGN,EAAE,IAAK9B,GAAG,CAAC+B,IAAI,CAAC,kBAAkBD,EAAE,YAAY,CAAC;EAC5DO,kBAAkB,EAAGV,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC,iCAAiC,EAAE;IAAED;EAAO,CAAC,CAAC;EACtFW,UAAU,EAAEA,CAACR,EAAE,EAAEH,MAAM,KAAK3B,GAAG,CAAC4B,GAAG,CAAC,kBAAkBE,EAAE,WAAW,EAAE;IAAEH;EAAO,CAAC;AACjF,CAAC;;AAED;AACA,OAAO,MAAMY,SAAS,GAAG;EACvBb,MAAM,EAAGC,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC,iBAAiB,EAAE;IAAED;EAAO,CAAC,CAAC;EAC1DE,OAAO,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,kBAAkBE,EAAE,GAAG,CAAC;EACjD7B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAAC+B,IAAI,CAAC,iBAAiB,EAAElB,IAAI,CAAC;EACnDmB,MAAM,EAAEA,CAACF,EAAE,EAAEjB,IAAI,KAAKb,GAAG,CAACiC,GAAG,CAAC,kBAAkBH,EAAE,GAAG,EAAEjB,IAAI,CAAC;EAC5DqB,MAAM,EAAGJ,EAAE,IAAK9B,GAAG,CAACkC,MAAM,CAAC,kBAAkBJ,EAAE,GAAG;AACpD,CAAC;;AAED;AACA,OAAO,MAAMU,aAAa,GAAG;EAC3Bd,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC,qBAAqB,CAAC;EAC5CC,OAAO,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,sBAAsBE,EAAE,GAAG,CAAC;EACrD7B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAAC+B,IAAI,CAAC,qBAAqB,EAAElB,IAAI,CAAC;EACvDmB,MAAM,EAAEA,CAACF,EAAE,EAAEjB,IAAI,KAAKb,GAAG,CAACiC,GAAG,CAAC,sBAAsBH,EAAE,GAAG,EAAEjB,IAAI,CAAC;EAChEqB,MAAM,EAAGJ,EAAE,IAAK9B,GAAG,CAACkC,MAAM,CAAC,sBAAsBJ,EAAE,GAAG;AACxD,CAAC;;AAED;AACA,OAAO,MAAMW,WAAW,GAAG;EACzBf,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC,mBAAmB,CAAC;EAC1CC,OAAO,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,oBAAoBE,EAAE,GAAG,CAAC;EACnDY,SAAS,EAAGZ,EAAE,IAAK9B,GAAG,CAAC+B,IAAI,CAAC,oBAAoBD,EAAE,aAAa;AACjE,CAAC;;AAED;AACA,OAAO,MAAMa,eAAe,GAAG;EAC7BjB,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC,uBAAuB,CAAC;EAC9CC,OAAO,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,wBAAwBE,EAAE,GAAG,CAAC;EACvDc,OAAO,EAAGd,EAAE,IAAK9B,GAAG,CAACkC,MAAM,CAAC,wBAAwBJ,EAAE,GAAG;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMe,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAM9C,GAAG,CAAC4B,GAAG,CAAC,4BAA4B,CAAC;EACvDmB,aAAa,EAAGlC,IAAI,IAAKb,GAAG,CAACiC,GAAG,CAAC,4BAA4B,EAAEpB,IAAI,CAAC;EACpEmC,gBAAgB,EAAEA,CAAA,KAAMhD,GAAG,CAAC4B,GAAG,CAAC,8BAA8B,CAAC;EAC/DqB,oBAAoB,EAAGnB,EAAE,IAAK9B,GAAG,CAAC+B,IAAI,CAAC,+BAA+BD,EAAE,QAAQ;AAClF,CAAC;AAED,eAAe9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}