{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\EditBook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditBook = () => {\n  _s();\n  var _currentUser$profile;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    quantitie_Dispo: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    url: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    if (!isAdmin) {\n      navigate('/books');\n      return;\n    }\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les catégories\n        const categoriesResponse = await livresAPI.getCategories();\n        // S'assurer que categories est bien un tableau\n        if (Array.isArray(categoriesResponse.data)) {\n          setCategories(categoriesResponse.data);\n        } else if (categoriesResponse.data && typeof categoriesResponse.data === 'object') {\n          // Si c'est un objet, essayer de le convertir en tableau\n          setCategories(Object.values(categoriesResponse.data));\n        } else {\n          console.error('Format de catégories inattendu:', categoriesResponse.data);\n          setCategories([]);\n        }\n\n        // Récupérer les données du livre\n        const bookResponse = await livresAPI.getById(id);\n        const book = bookResponse.data;\n\n        // Formater la date pour l'input date\n        const datePublication = book.date_publication ? new Date(book.date_publication).toISOString().split('T')[0] : '';\n\n        // Afficher les données du livre pour le débogage\n        console.log('Données du livre:', book);\n        setFormData({\n          titre: book.titre || '',\n          autheur: book.autheur || '',\n          category: String(book.category) || '',\n          // Convertir en string pour s'assurer de la compatibilité\n          price: book.price || '',\n          quantitie_Total: book.quantitie_Total || '',\n          quantitie_Dispo: book.quantitie_Dispo || '',\n          desc: book.desc || '',\n          date_publication: datePublication,\n          isbn: book.isbn || '',\n          url: book.url || '',\n          image: null\n        });\n\n        // Définir l'aperçu de l'image si elle existe\n        if (book.image_url) {\n          setImagePreview(book.image_url);\n        } else if (book.image) {\n          setImagePreview(`http://localhost:8000/media/book_${id}.jpg`);\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      files\n    } = e.target;\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n\n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n\n      // Envoyer les données au serveur\n      await livresAPI.update(id, data);\n\n      // Rediriger vers la page de détail du livre\n      navigate(`/books/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification du livre:', err);\n      setError('Erreur lors de la modification du livre. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      message: \"Chargement des donn\\xE9es...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-book-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"add-book-title\",\n      children: \"Modifier un livre\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-book-card\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"add-book-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"titre\",\n              children: [\"Titre \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"titre\",\n              name: \"titre\",\n              value: formData.titre,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"autheur\",\n              children: [\"Auteur \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"autheur\",\n              name: \"autheur\",\n              value: formData.autheur,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: [\"Cat\\xE9gorie \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"S\\xE9lectionner une cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), Array.isArray(categories) && categories.filter(category => category && category.id && category.name) // Filtrer les éléments null ou undefined\n              .map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: String(category.id),\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isbn\",\n              children: [\"ISBN \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"isbn\",\n              name: \"isbn\",\n              value: formData.isbn,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              children: [\"Prix \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"price\",\n              name: \"price\",\n              value: formData.price,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"date_publication\",\n              children: [\"Date de publication \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 69\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"date_publication\",\n              name: \"date_publication\",\n              value: formData.date_publication,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantitie_Total\",\n              children: [\"Quantit\\xE9 totale \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 64\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"quantitie_Total\",\n              name: \"quantitie_Total\",\n              value: formData.quantitie_Total,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantitie_Dispo\",\n              children: [\"Quantit\\xE9 disponible \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 68\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"quantitie_Dispo\",\n              name: \"quantitie_Dispo\",\n              value: formData.quantitie_Dispo,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"url\",\n            children: \"URL (optionnel)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"url\",\n            name: \"url\",\n            value: formData.url,\n            onChange: handleChange,\n            placeholder: \"https://example.com\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"desc\",\n            children: [\"Description \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 47\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"desc\",\n            name: \"desc\",\n            value: formData.desc,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: \"Image (optionnel)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            name: \"image\",\n            onChange: handleChange,\n            accept: \"image/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Aper\\xE7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: `/books/${id}`,\n            className: \"cancel-button\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            children: \"Enregistrer les modifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(EditBook, \"plfNkfsjPnYx6VpfBBZYY10hoDg=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = EditBook;\nexport default EditBook;\nvar _c;\n$RefreshReg$(_c, \"EditBook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "livresAPI", "useAuth", "Loading", "jsxDEV", "_jsxDEV", "EditBook", "_s", "_currentUser$profile", "id", "navigate", "isAuthenticated", "currentUser", "loading", "setLoading", "error", "setError", "categories", "setCategories", "imagePreview", "setImagePreview", "formData", "setFormData", "titre", "autheur", "category", "price", "quantitie_Total", "quantitie_Dispo", "desc", "date_publication", "isbn", "url", "image", "isAdmin", "profile", "user_type", "is_superuser", "fetchData", "categoriesResponse", "getCategories", "Array", "isArray", "data", "Object", "values", "console", "bookResponse", "getById", "book", "datePublication", "Date", "toISOString", "split", "log", "String", "image_url", "err", "handleChange", "e", "name", "value", "type", "files", "target", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "FormData", "keys", "for<PERSON>ach", "key", "append", "update", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "onSubmit", "htmlFor", "onChange", "required", "filter", "map", "placeholder", "accept", "src", "alt", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/EditBook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\n\nconst EditBook = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { isAuthenticated, currentUser } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    quantitie_Dispo: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    url: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    if (!isAdmin) {\n      navigate('/books');\n      return;\n    }\n\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les catégories\n        const categoriesResponse = await livresAPI.getCategories();\n        // S'assurer que categories est bien un tableau\n        if (Array.isArray(categoriesResponse.data)) {\n          setCategories(categoriesResponse.data);\n        } else if (categoriesResponse.data && typeof categoriesResponse.data === 'object') {\n          // Si c'est un objet, essayer de le convertir en tableau\n          setCategories(Object.values(categoriesResponse.data));\n        } else {\n          console.error('Format de catégories inattendu:', categoriesResponse.data);\n          setCategories([]);\n        }\n\n        // Récupérer les données du livre\n        const bookResponse = await livresAPI.getById(id);\n        const book = bookResponse.data;\n\n        // Formater la date pour l'input date\n        const datePublication = book.date_publication\n          ? new Date(book.date_publication).toISOString().split('T')[0]\n          : '';\n\n        // Afficher les données du livre pour le débogage\n        console.log('Données du livre:', book);\n\n        setFormData({\n          titre: book.titre || '',\n          autheur: book.autheur || '',\n          category: String(book.category) || '', // Convertir en string pour s'assurer de la compatibilité\n          price: book.price || '',\n          quantitie_Total: book.quantitie_Total || '',\n          quantitie_Dispo: book.quantitie_Dispo || '',\n          desc: book.desc || '',\n          date_publication: datePublication,\n          isbn: book.isbn || '',\n          url: book.url || '',\n          image: null\n        });\n\n        // Définir l'aperçu de l'image si elle existe\n        if (book.image_url) {\n          setImagePreview(book.image_url);\n        } else if (book.image) {\n          setImagePreview(`http://localhost:8000/media/book_${id}.jpg`);\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n\n  const handleChange = (e) => {\n    const { name, value, type, files } = e.target;\n\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n\n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n\n      // Envoyer les données au serveur\n      await livresAPI.update(id, data);\n\n      // Rediriger vers la page de détail du livre\n      navigate(`/books/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification du livre:', err);\n      setError('Erreur lors de la modification du livre. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <Loading message=\"Chargement des données...\" />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">{error}</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"add-book-container\">\n      <h1 className=\"add-book-title\">Modifier un livre</h1>\n\n      <div className=\"add-book-card\">\n        <form className=\"add-book-form\" onSubmit={handleSubmit}>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"titre\">Titre <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"titre\"\n                name=\"titre\"\n                value={formData.titre}\n                onChange={handleChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"autheur\">Auteur <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"autheur\"\n                name=\"autheur\"\n                value={formData.autheur}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"category\">Catégorie <span className=\"required\">*</span></label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"\">Sélectionner une catégorie</option>\n                {Array.isArray(categories) && categories\n                  .filter(category => category && category.id && category.name) // Filtrer les éléments null ou undefined\n                  .map(category => (\n                    <option key={category.id} value={String(category.id)}>\n                      {category.name}\n                    </option>\n                  ))}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"isbn\">ISBN <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"isbn\"\n                name=\"isbn\"\n                value={formData.isbn}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"price\">Prix <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"date_publication\">Date de publication <span className=\"required\">*</span></label>\n              <input\n                type=\"date\"\n                id=\"date_publication\"\n                name=\"date_publication\"\n                value={formData.date_publication}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"quantitie_Total\">Quantité totale <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"quantitie_Total\"\n                name=\"quantitie_Total\"\n                value={formData.quantitie_Total}\n                onChange={handleChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"quantitie_Dispo\">Quantité disponible <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"quantitie_Dispo\"\n                name=\"quantitie_Dispo\"\n                value={formData.quantitie_Dispo}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"url\">URL (optionnel)</label>\n            <input\n              type=\"url\"\n              id=\"url\"\n              name=\"url\"\n              value={formData.url}\n              onChange={handleChange}\n              placeholder=\"https://example.com\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"desc\">Description <span className=\"required\">*</span></label>\n            <textarea\n              id=\"desc\"\n              name=\"desc\"\n              value={formData.desc}\n              onChange={handleChange}\n              required\n            ></textarea>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"image\">Image (optionnel)</label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              name=\"image\"\n              onChange={handleChange}\n              accept=\"image/*\"\n            />\n            {imagePreview && (\n              <div className=\"image-preview\">\n                <img src={imagePreview} alt=\"Aperçu\" />\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-actions\">\n            <Link to={`/books/${id}`} className=\"cancel-button\">Annuler</Link>\n            <button type=\"submit\" className=\"submit-button\">Enregistrer les modifications</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default EditBook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,eAAe;IAAEC;EAAY,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAAtB,WAAW,aAAXA,WAAW,wBAAAJ,oBAAA,GAAXI,WAAW,CAAEuB,OAAO,cAAA3B,oBAAA,uBAApBA,oBAAA,CAAsB4B,SAAS,MAAK,OAAO,KAAIxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,YAAY;EAExFxC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACc,eAAe,EAAE;MACpBD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI,CAACwB,OAAO,EAAE;MACZxB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,MAAM4B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFxB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMyB,kBAAkB,GAAG,MAAMtC,SAAS,CAACuC,aAAa,CAAC,CAAC;QAC1D;QACA,IAAIC,KAAK,CAACC,OAAO,CAACH,kBAAkB,CAACI,IAAI,CAAC,EAAE;UAC1CzB,aAAa,CAACqB,kBAAkB,CAACI,IAAI,CAAC;QACxC,CAAC,MAAM,IAAIJ,kBAAkB,CAACI,IAAI,IAAI,OAAOJ,kBAAkB,CAACI,IAAI,KAAK,QAAQ,EAAE;UACjF;UACAzB,aAAa,CAAC0B,MAAM,CAACC,MAAM,CAACN,kBAAkB,CAACI,IAAI,CAAC,CAAC;QACvD,CAAC,MAAM;UACLG,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAEwB,kBAAkB,CAACI,IAAI,CAAC;UACzEzB,aAAa,CAAC,EAAE,CAAC;QACnB;;QAEA;QACA,MAAM6B,YAAY,GAAG,MAAM9C,SAAS,CAAC+C,OAAO,CAACvC,EAAE,CAAC;QAChD,MAAMwC,IAAI,GAAGF,YAAY,CAACJ,IAAI;;QAE9B;QACA,MAAMO,eAAe,GAAGD,IAAI,CAACnB,gBAAgB,GACzC,IAAIqB,IAAI,CAACF,IAAI,CAACnB,gBAAgB,CAAC,CAACsB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3D,EAAE;;QAEN;QACAP,OAAO,CAACQ,GAAG,CAAC,mBAAmB,EAAEL,IAAI,CAAC;QAEtC3B,WAAW,CAAC;UACVC,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,IAAI,EAAE;UACvBC,OAAO,EAAEyB,IAAI,CAACzB,OAAO,IAAI,EAAE;UAC3BC,QAAQ,EAAE8B,MAAM,CAACN,IAAI,CAACxB,QAAQ,CAAC,IAAI,EAAE;UAAE;UACvCC,KAAK,EAAEuB,IAAI,CAACvB,KAAK,IAAI,EAAE;UACvBC,eAAe,EAAEsB,IAAI,CAACtB,eAAe,IAAI,EAAE;UAC3CC,eAAe,EAAEqB,IAAI,CAACrB,eAAe,IAAI,EAAE;UAC3CC,IAAI,EAAEoB,IAAI,CAACpB,IAAI,IAAI,EAAE;UACrBC,gBAAgB,EAAEoB,eAAe;UACjCnB,IAAI,EAAEkB,IAAI,CAAClB,IAAI,IAAI,EAAE;UACrBC,GAAG,EAAEiB,IAAI,CAACjB,GAAG,IAAI,EAAE;UACnBC,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACA,IAAIgB,IAAI,CAACO,SAAS,EAAE;UAClBpC,eAAe,CAAC6B,IAAI,CAACO,SAAS,CAAC;QACjC,CAAC,MAAM,IAAIP,IAAI,CAAChB,KAAK,EAAE;UACrBb,eAAe,CAAC,oCAAoCX,EAAE,MAAM,CAAC;QAC/D;QAEAK,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAO2C,GAAG,EAAE;QACZX,OAAO,CAAC/B,KAAK,CAAC,wCAAwC,EAAE0C,GAAG,CAAC;QAC5DzC,QAAQ,CAAC,sEAAsE,CAAC;QAChFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,EAAE,EAAEE,eAAe,EAAEuB,OAAO,EAAExB,QAAQ,CAAC,CAAC;EAE5C,MAAMgD,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAE7C,IAAIF,IAAI,KAAK,MAAM,EAAE;MACnBxC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACuC,IAAI,GAAGG,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACA,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACZ,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvB/C,eAAe,CAAC6C,MAAM,CAACG,MAAM,CAAC;QAChC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACLzC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACuC,IAAI,GAAGC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI;MACFzD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM6B,IAAI,GAAG,IAAI6B,QAAQ,CAAC,CAAC;MAC3B5B,MAAM,CAAC6B,IAAI,CAACpD,QAAQ,CAAC,CAACqD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAItD,QAAQ,CAACsD,GAAG,CAAC,KAAK,IAAI,EAAE;UAC1BhC,IAAI,CAACiC,MAAM,CAACD,GAAG,EAAEtD,QAAQ,CAACsD,GAAG,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,MAAM1E,SAAS,CAAC4E,MAAM,CAACpE,EAAE,EAAEkC,IAAI,CAAC;;MAEhC;MACAjC,QAAQ,CAAC,UAAUD,EAAE,EAAE,CAAC;IAC1B,CAAC,CAAC,OAAOgD,GAAG,EAAE;MACZX,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE0C,GAAG,CAAC;MAC9DzC,QAAQ,CAAC,8DAA8D,CAAC;MACxEF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACF,OAAO;MAAC2E,OAAO,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxD;EAEA,IAAInE,KAAK,EAAE;IACT,oBACEV,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/E,OAAA;QAAG8E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAErE;MAAK;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC7E,OAAA;QACE8E,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAK8E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC/E,OAAA;MAAI8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAiB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErD7E,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B/E,OAAA;QAAM8E,SAAS,EAAC,eAAe;QAACM,QAAQ,EAAEnB,YAAa;QAAAc,QAAA,gBACrD/E,OAAA;UAAK8E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,QAAM,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxE7E,OAAA;cACEyD,IAAI,EAAC,MAAM;cACXrD,EAAE,EAAC,OAAO;cACVmD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAExC,QAAQ,CAACE,KAAM;cACtBoE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,SAAS;cAAAN,QAAA,GAAC,SAAO,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E7E,OAAA;cACEyD,IAAI,EAAC,MAAM;cACXrD,EAAE,EAAC,SAAS;cACZmD,IAAI,EAAC,SAAS;cACdC,KAAK,EAAExC,QAAQ,CAACG,OAAQ;cACxBmE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,UAAU;cAAAN,QAAA,GAAC,eAAU,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E7E,OAAA;cACEI,EAAE,EAAC,UAAU;cACbmD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAExC,QAAQ,CAACI,QAAS;cACzBkE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;cAAAR,QAAA,gBAER/E,OAAA;gBAAQwD,KAAK,EAAC,EAAE;gBAAAuB,QAAA,EAAC;cAA0B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDzC,KAAK,CAACC,OAAO,CAACzB,UAAU,CAAC,IAAIA,UAAU,CACrC4E,MAAM,CAACpE,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAChB,EAAE,IAAIgB,QAAQ,CAACmC,IAAI,CAAC,CAAC;cAAA,CAC7DkC,GAAG,CAACrE,QAAQ,iBACXpB,OAAA;gBAA0BwD,KAAK,EAAEN,MAAM,CAAC9B,QAAQ,CAAChB,EAAE,CAAE;gBAAA2E,QAAA,EAClD3D,QAAQ,CAACmC;cAAI,GADHnC,QAAQ,CAAChB,EAAE;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE7E,OAAA;cACEyD,IAAI,EAAC,MAAM;cACXrD,EAAE,EAAC,MAAM;cACTmD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExC,QAAQ,CAACU,IAAK;cACrB4D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,OAAK,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvE7E,OAAA;cACEyD,IAAI,EAAC,QAAQ;cACbrD,EAAE,EAAC,OAAO;cACVmD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAExC,QAAQ,CAACK,KAAM;cACtBiE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,kBAAkB;cAAAN,QAAA,GAAC,sBAAoB,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjG7E,OAAA;cACEyD,IAAI,EAAC,MAAM;cACXrD,EAAE,EAAC,kBAAkB;cACrBmD,IAAI,EAAC,kBAAkB;cACvBC,KAAK,EAAExC,QAAQ,CAACS,gBAAiB;cACjC6D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,iBAAiB;cAAAN,QAAA,GAAC,qBAAgB,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5F7E,OAAA;cACEyD,IAAI,EAAC,QAAQ;cACbrD,EAAE,EAAC,iBAAiB;cACpBmD,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAExC,QAAQ,CAACM,eAAgB;cAChCgE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7E,OAAA;YAAK8E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/E,OAAA;cAAOqF,OAAO,EAAC,iBAAiB;cAAAN,QAAA,GAAC,yBAAoB,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7E,OAAA;cACEyD,IAAI,EAAC,QAAQ;cACbrD,EAAE,EAAC,iBAAiB;cACpBmD,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAExC,QAAQ,CAACO,eAAgB;cAChC+D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/E,OAAA;YAAOqF,OAAO,EAAC,KAAK;YAAAN,QAAA,EAAC;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C7E,OAAA;YACEyD,IAAI,EAAC,KAAK;YACVrD,EAAE,EAAC,KAAK;YACRmD,IAAI,EAAC,KAAK;YACVC,KAAK,EAAExC,QAAQ,CAACW,GAAI;YACpB2D,QAAQ,EAAEjC,YAAa;YACvBqC,WAAW,EAAC;UAAqB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/E,OAAA;YAAOqF,OAAO,EAAC,MAAM;YAAAN,QAAA,GAAC,cAAY,eAAA/E,OAAA;cAAM8E,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7E7E,OAAA;YACEI,EAAE,EAAC,MAAM;YACTmD,IAAI,EAAC,MAAM;YACXC,KAAK,EAAExC,QAAQ,CAACQ,IAAK;YACrB8D,QAAQ,EAAEjC,YAAa;YACvBkC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/E,OAAA;YAAOqF,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD7E,OAAA;YACEyD,IAAI,EAAC,MAAM;YACXrD,EAAE,EAAC,OAAO;YACVmD,IAAI,EAAC,OAAO;YACZ+B,QAAQ,EAAEjC,YAAa;YACvBsC,MAAM,EAAC;UAAS;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EACD/D,YAAY,iBACXd,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/E,OAAA;cAAK4F,GAAG,EAAE9E,YAAa;cAAC+E,GAAG,EAAC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN7E,OAAA;UAAK8E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/E,OAAA,CAACL,IAAI;YAACmG,EAAE,EAAE,UAAU1F,EAAE,EAAG;YAAC0E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClE7E,OAAA;YAAQyD,IAAI,EAAC,QAAQ;YAACqB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA6B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA5UID,QAAQ;EAAA,QACGR,SAAS,EACPC,WAAW,EACaG,OAAO;AAAA;AAAAkG,EAAA,GAH5C9F,QAAQ;AA8Ud,eAAeA,QAAQ;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}