{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { empruntsAPI, reservationsAPI } from '../services/api';\nimport './Profile.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _currentUser$profile, _currentUser$profile2;\n  const {\n    currentUser\n  } = useAuth();\n  const [emprunts, setEmprunts] = useState([]);\n  const [reservations, setReservations] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    photo: null\n  });\n  const [photoPreview, setPhotoPreview] = useState(null);\n  const [updateSuccess, setUpdateSuccess] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Vérifier si l'utilisateur est connecté\n        if (!currentUser) {\n          console.log(\"Utilisateur non connecté ou données utilisateur non disponibles\");\n          setLoading(false);\n          return;\n        }\n        console.log(\"Données utilisateur disponibles:\", currentUser);\n        try {\n          // Récupérer les emprunts\n          const empruntsResponse = await empruntsAPI.getAll();\n          setEmprunts(empruntsResponse.data.results || []);\n        } catch (empruntsErr) {\n          console.error('Erreur lors de la récupération des emprunts:', empruntsErr);\n          setEmprunts([]);\n        }\n        try {\n          // Récupérer les réservations\n          const reservationsResponse = await reservationsAPI.getAll();\n          setReservations(reservationsResponse.data.results || []);\n        } catch (reservationsErr) {\n          console.error('Erreur lors de la récupération des réservations:', reservationsErr);\n          setReservations([]);\n        }\n        try {\n          // Récupérer les notifications\n          const notificationsResponse = await utilisateursAPI.getNotifications();\n          setNotifications(notificationsResponse.data || []);\n        } catch (notificationsErr) {\n          console.error('Erreur lors de la récupération des notifications:', notificationsErr);\n          setNotifications([]);\n        }\n\n        // Initialiser le formulaire avec les données de l'utilisateur\n        setFormData({\n          first_name: currentUser.first_name || '',\n          last_name: currentUser.last_name || '',\n          email: currentUser.email || '',\n          photo: null\n        });\n        if (currentUser.profile && currentUser.profile.photo) {\n          setPhotoPreview(currentUser.profile.photo);\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [currentUser]);\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handlePhotoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        photo: file\n      }));\n\n      // Créer un aperçu de la photo\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    console.log('Soumission du formulaire de profil');\n    try {\n      setLoading(true);\n      console.log('État de chargement activé');\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      console.log('FormData créé');\n\n      // Créer un objet user pour les données utilisateur\n      const userData = {\n        first_name: formData.first_name,\n        last_name: formData.last_name,\n        email: formData.email\n      };\n      console.log('Données utilisateur préparées:', userData);\n\n      // Ajouter l'objet user en tant que JSON\n      formDataObj.append('user', JSON.stringify(userData));\n      console.log('Données utilisateur ajoutées au FormData');\n\n      // Ajouter la photo si elle existe\n      if (formData.photo) {\n        formDataObj.append('photo', formData.photo);\n        console.log('Photo ajoutée au FormData:', formData.photo.name);\n      } else {\n        console.log('Pas de photo à ajouter');\n      }\n\n      // Log pour le débogage\n      console.log('FormData prêt à être envoyé');\n\n      // Vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n      console.log('Envoi de la requête de mise à jour du profil...');\n      const response = await utilisateursAPI.updateProfile(formDataObj);\n      console.log('Réponse reçue:', response);\n      console.log('Mise à jour du profil réussie');\n      setUpdateSuccess(true);\n      setTimeout(() => {\n        setUpdateSuccess(false);\n      }, 3000);\n      setLoading(false);\n    } catch (err) {\n      var _err$response;\n      console.error('Erreur lors de la mise à jour du profil:', err);\n      console.error('Détails de l\\'erreur:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data);\n      setError('Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleRetourLivre = async empruntId => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const empruntsResponse = await empruntsAPI.getAll();\n      setEmprunts(empruntsResponse.data.results || []);\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleAnnulerReservation = async reservationId => {\n    try {\n      setLoading(true);\n      await reservationsAPI.annuler(reservationId);\n\n      // Mettre à jour la liste des réservations\n      const reservationsResponse = await reservationsAPI.getAll();\n      setReservations(reservationsResponse.data.results || []);\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de l\\'annulation de la réservation:', err);\n      setError('Erreur lors de l\\'annulation de la réservation. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleMarkNotificationRead = async notificationId => {\n    try {\n      await utilisateursAPI.markNotificationRead(notificationId);\n\n      // Mettre à jour la liste des notifications\n      const notificationsResponse = await utilisateursAPI.getNotifications();\n      setNotifications(notificationsResponse.data || []);\n    } catch (err) {\n      console.error('Erreur lors du marquage de la notification:', err);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement de votre profil...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Une erreur est survenue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-button\",\n          onClick: () => window.location.reload(),\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n  if (!currentUser) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Utilisateur non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Impossible de charger les informations de votre profil. Veuillez vous reconnecter.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"login-button\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'profile' ? 'active' : ''}`,\n        onClick: () => handleTabChange('profile'),\n        children: \"Profil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'emprunts' ? 'active' : ''}`,\n        onClick: () => handleTabChange('emprunts'),\n        children: [\"Emprunts \", emprunts.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: emprunts.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 44\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'reservations' ? 'active' : ''}`,\n        onClick: () => handleTabChange('reservations'),\n        children: [\"R\\xE9servations \", reservations.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: reservations.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 52\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'notifications' ? 'active' : ''}`,\n        onClick: () => handleTabChange('notifications'),\n        children: [\"Notifications \", notifications.filter(n => !n.est_lue).length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: notifications.filter(n => !n.est_lue).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-content\",\n      children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-photo-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-photo\",\n              children: photoPreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: photoPreview,\n                alt: \"Photo de profil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png\",\n                alt: \"Photo par d\\xE9faut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"photo-upload\",\n              className: \"change-photo-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-camera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"photo-upload\",\n              accept: \"image/*\",\n              style: {\n                display: 'none'\n              },\n              onChange: handlePhotoChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-name\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: currentUser.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge ${((_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || currentUser.is_superuser ? 'admin' : 'etudiant'}`,\n              children: ((_currentUser$profile2 = currentUser.profile) === null || _currentUser$profile2 === void 0 ? void 0 : _currentUser$profile2.user_type) === 'admin' || currentUser.is_superuser ? 'Administrateur' : 'Étudiant'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), updateSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-message\",\n          children: \"Profil mis \\xE0 jour avec succ\\xE8s !\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"profile-form\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"first_name\",\n                children: \"Pr\\xE9nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"first_name\",\n                name: \"first_name\",\n                value: formData.first_name,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"last_name\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"last_name\",\n                name: \"last_name\",\n                value: formData.last_name,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"update-button\",\n            disabled: loading,\n            children: loading ? 'Mise à jour...' : 'Mettre à jour le profil'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), activeTab === 'emprunts' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"emprunts-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes emprunts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), emprunts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucun emprunt en cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-grid\",\n          children: emprunts.map(emprunt => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: emprunt.livre_titre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${emprunt.est_retourne ? 'returned' : 'active'}`,\n                children: emprunt.est_retourne ? 'Retourné' : 'En cours'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date d'emprunt:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 26\n                }, this), \" \", new Date(emprunt.date_emprunt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de retour pr\\xE9vue:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 26\n                }, this), \" \", new Date(emprunt.date_retour_prevue).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 23\n              }, this), emprunt.date_retour_effective && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de retour effective:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 28\n                }, this), \" \", new Date(emprunt.date_retour_effective).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 21\n            }, this), !emprunt.est_retourne && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button return\",\n              onClick: () => handleRetourLivre(emprunt.id),\n              disabled: loading,\n              children: \"Retourner le livre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 23\n            }, this)]\n          }, emprunt.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this), activeTab === 'reservations' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reservations-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes r\\xE9servations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), reservations.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucune r\\xE9servation en cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-grid\",\n          children: reservations.map(reservation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: reservation.livre_titre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${reservation.est_active ? 'active' : 'inactive'}`,\n                children: reservation.est_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-details\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de r\\xE9servation:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 26\n                }, this), \" \", new Date(reservation.date_reservation).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this), reservation.est_active && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button cancel\",\n              onClick: () => handleAnnulerReservation(reservation.id),\n              disabled: loading,\n              children: \"Annuler la r\\xE9servation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 23\n            }, this)]\n          }, reservation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 11\n      }, this), activeTab === 'notifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notifications-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucune notification.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notifications-container\",\n          children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `notification-item ${notification.est_lue ? 'read' : 'unread'}`,\n            onClick: () => !notification.est_lue && handleMarkNotificationRead(notification.id),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `notification-type ${notification.type}`,\n                children: [notification.type === 'retard' && 'Retard', notification.type === 'rappel' && 'Rappel', notification.type === 'nouveau' && 'Nouveau', notification.type === 'emprunt' && 'Emprunt', notification.type === 'reservation' && 'Réservation', notification.type === 'disponible' && 'Disponible']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"notification-date\",\n                children: new Date(notification.date).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-content\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: notification.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 21\n            }, this), notification.livre_id && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/books/${notification.livre_id}`,\n              className: \"notification-link\",\n              children: \"Voir le livre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 23\n            }, this)]\n          }, notification.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"3zC43ukmeE+dTZXbBM5M7BQ2b7k=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useAuth", "empruntsAPI", "reservationsAPI", "jsxDEV", "_jsxDEV", "Profile", "_s", "_currentUser$profile", "_currentUser$profile2", "currentUser", "emprunts", "set<PERSON>mp<PERSON><PERSON>", "reservations", "setReservations", "notifications", "setNotifications", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "formData", "setFormData", "first_name", "last_name", "email", "photo", "photoPreview", "setPhotoPreview", "updateSuccess", "setUpdateSuccess", "fetchData", "console", "log", "empruntsResponse", "getAll", "data", "results", "empruntsErr", "reservationsResponse", "reservationsErr", "notificationsResponse", "utilisateursAPI", "getNotifications", "notificationsErr", "profile", "err", "handleTabChange", "tab", "handleInputChange", "e", "name", "value", "target", "prev", "handlePhotoChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formDataObj", "FormData", "userData", "append", "JSON", "stringify", "pair", "entries", "response", "updateProfile", "setTimeout", "_err$response", "handleRetourLivre", "empruntId", "retourner", "handleAnnulerReservation", "reservationId", "annuler", "handleMarkNotificationRead", "notificationId", "markNotificationRead", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "to", "length", "filter", "n", "est_lue", "src", "alt", "htmlFor", "type", "id", "accept", "style", "display", "onChange", "username", "user_type", "is_superuser", "onSubmit", "disabled", "map", "emp<PERSON><PERSON>", "livre_titre", "est_retourne", "Date", "date_emprunt", "toLocaleDateString", "date_retour_prevue", "date_retour_effective", "reservation", "est_active", "date_reservation", "notification", "date", "message", "livre_id", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { empruntsAPI, reservationsAPI } from '../services/api';\nimport './Profile.css';\n\nconst Profile = () => {\n  const { currentUser } = useAuth();\n  const [emprunts, setEmprunts] = useState([]);\n  const [reservations, setReservations] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    photo: null\n  });\n  const [photoPreview, setPhotoPreview] = useState(null);\n  const [updateSuccess, setUpdateSuccess] = useState(false);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Vérifier si l'utilisateur est connecté\n        if (!currentUser) {\n          console.log(\"Utilisateur non connecté ou données utilisateur non disponibles\");\n          setLoading(false);\n          return;\n        }\n\n        console.log(\"Données utilisateur disponibles:\", currentUser);\n\n        try {\n          // Récupérer les emprunts\n          const empruntsResponse = await empruntsAPI.getAll();\n          setEmprunts(empruntsResponse.data.results || []);\n        } catch (empruntsErr) {\n          console.error('Erreur lors de la récupération des emprunts:', empruntsErr);\n          setEmprunts([]);\n        }\n\n        try {\n          // Récupérer les réservations\n          const reservationsResponse = await reservationsAPI.getAll();\n          setReservations(reservationsResponse.data.results || []);\n        } catch (reservationsErr) {\n          console.error('Erreur lors de la récupération des réservations:', reservationsErr);\n          setReservations([]);\n        }\n\n        try {\n          // Récupérer les notifications\n          const notificationsResponse = await utilisateursAPI.getNotifications();\n          setNotifications(notificationsResponse.data || []);\n        } catch (notificationsErr) {\n          console.error('Erreur lors de la récupération des notifications:', notificationsErr);\n          setNotifications([]);\n        }\n\n        // Initialiser le formulaire avec les données de l'utilisateur\n        setFormData({\n          first_name: currentUser.first_name || '',\n          last_name: currentUser.last_name || '',\n          email: currentUser.email || '',\n          photo: null\n        });\n\n        if (currentUser.profile && currentUser.profile.photo) {\n          setPhotoPreview(currentUser.profile.photo);\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [currentUser]);\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handlePhotoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        photo: file\n      }));\n\n      // Créer un aperçu de la photo\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    console.log('Soumission du formulaire de profil');\n\n    try {\n      setLoading(true);\n      console.log('État de chargement activé');\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      console.log('FormData créé');\n\n      // Créer un objet user pour les données utilisateur\n      const userData = {\n        first_name: formData.first_name,\n        last_name: formData.last_name,\n        email: formData.email\n      };\n      console.log('Données utilisateur préparées:', userData);\n\n      // Ajouter l'objet user en tant que JSON\n      formDataObj.append('user', JSON.stringify(userData));\n      console.log('Données utilisateur ajoutées au FormData');\n\n      // Ajouter la photo si elle existe\n      if (formData.photo) {\n        formDataObj.append('photo', formData.photo);\n        console.log('Photo ajoutée au FormData:', formData.photo.name);\n      } else {\n        console.log('Pas de photo à ajouter');\n      }\n\n      // Log pour le débogage\n      console.log('FormData prêt à être envoyé');\n\n      // Vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n\n      console.log('Envoi de la requête de mise à jour du profil...');\n      const response = await utilisateursAPI.updateProfile(formDataObj);\n      console.log('Réponse reçue:', response);\n\n      console.log('Mise à jour du profil réussie');\n      setUpdateSuccess(true);\n      setTimeout(() => {\n        setUpdateSuccess(false);\n      }, 3000);\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de la mise à jour du profil:', err);\n      console.error('Détails de l\\'erreur:', err.response?.data);\n      setError('Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleRetourLivre = async (empruntId) => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const empruntsResponse = await empruntsAPI.getAll();\n      setEmprunts(empruntsResponse.data.results || []);\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleAnnulerReservation = async (reservationId) => {\n    try {\n      setLoading(true);\n      await reservationsAPI.annuler(reservationId);\n\n      // Mettre à jour la liste des réservations\n      const reservationsResponse = await reservationsAPI.getAll();\n      setReservations(reservationsResponse.data.results || []);\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de l\\'annulation de la réservation:', err);\n      setError('Erreur lors de l\\'annulation de la réservation. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleMarkNotificationRead = async (notificationId) => {\n    try {\n      await utilisateursAPI.markNotificationRead(notificationId);\n\n      // Mettre à jour la liste des notifications\n      const notificationsResponse = await utilisateursAPI.getNotifications();\n      setNotifications(notificationsResponse.data || []);\n    } catch (err) {\n      console.error('Erreur lors du marquage de la notification:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Chargement de votre profil...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"error-message\">\n          <h2>Une erreur est survenue</h2>\n          <p>{error}</p>\n          <button\n            className=\"retry-button\"\n            onClick={() => window.location.reload()}\n          >\n            Réessayer\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  if (!currentUser) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"error-message\">\n          <h2>Utilisateur non trouvé</h2>\n          <p>Impossible de charger les informations de votre profil. Veuillez vous reconnecter.</p>\n          <Link to=\"/login\" className=\"login-button\">Se connecter</Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"profile-container\">\n      <div className=\"profile-tabs\">\n        <button\n          className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}\n          onClick={() => handleTabChange('profile')}\n        >\n          Profil\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'emprunts' ? 'active' : ''}`}\n          onClick={() => handleTabChange('emprunts')}\n        >\n          Emprunts {emprunts.length > 0 && <span className=\"badge\">{emprunts.length}</span>}\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'reservations' ? 'active' : ''}`}\n          onClick={() => handleTabChange('reservations')}\n        >\n          Réservations {reservations.length > 0 && <span className=\"badge\">{reservations.length}</span>}\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'notifications' ? 'active' : ''}`}\n          onClick={() => handleTabChange('notifications')}\n        >\n          Notifications {notifications.filter(n => !n.est_lue).length > 0 &&\n            <span className=\"badge\">{notifications.filter(n => !n.est_lue).length}</span>}\n        </button>\n      </div>\n\n      <div className=\"profile-content\">\n        {activeTab === 'profile' && (\n          <div className=\"profile-info\">\n            <div className=\"profile-header\">\n              <div className=\"profile-photo-container\">\n                <div className=\"profile-photo\">\n                  {photoPreview ? (\n                    <img src={photoPreview} alt=\"Photo de profil\" />\n                  ) : (\n                    <img src=\"https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png\" alt=\"Photo par défaut\" />\n                  )}\n                </div>\n                <label htmlFor=\"photo-upload\" className=\"change-photo-btn\">\n                  <i className=\"fas fa-camera\"></i>\n                </label>\n                <input\n                  type=\"file\"\n                  id=\"photo-upload\"\n                  accept=\"image/*\"\n                  style={{ display: 'none' }}\n                  onChange={handlePhotoChange}\n                />\n              </div>\n\n              <div className=\"profile-name\">\n                <h2>{currentUser.username}</h2>\n                <span className={`badge ${currentUser.profile?.user_type === 'admin' || currentUser.is_superuser ? 'admin' : 'etudiant'}`}>\n                  {currentUser.profile?.user_type === 'admin' || currentUser.is_superuser ? 'Administrateur' : 'Étudiant'}\n                </span>\n              </div>\n            </div>\n\n            {updateSuccess && (\n              <div className=\"success-message\">\n                Profil mis à jour avec succès !\n              </div>\n            )}\n\n            <form className=\"profile-form\" onSubmit={handleSubmit}>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"first_name\">Prénom</label>\n                  <input\n                    type=\"text\"\n                    id=\"first_name\"\n                    name=\"first_name\"\n                    value={formData.first_name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"last_name\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"last_name\"\n                    name=\"last_name\"\n                    value={formData.last_name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"email\">Email</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              <button type=\"submit\" className=\"update-button\" disabled={loading}>\n                {loading ? 'Mise à jour...' : 'Mettre à jour le profil'}\n              </button>\n            </form>\n          </div>\n        )}\n\n        {activeTab === 'emprunts' && (\n          <div className=\"emprunts-list\">\n            <h2>Mes emprunts</h2>\n\n            {emprunts.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucun emprunt en cours.</p>\n            ) : (\n              <div className=\"items-grid\">\n                {emprunts.map(emprunt => (\n                  <div key={emprunt.id} className=\"item-card\">\n                    <div className=\"item-header\">\n                      <h3>{emprunt.livre_titre}</h3>\n                      <span className={`status ${emprunt.est_retourne ? 'returned' : 'active'}`}>\n                        {emprunt.est_retourne ? 'Retourné' : 'En cours'}\n                      </span>\n                    </div>\n\n                    <div className=\"item-details\">\n                      <p><strong>Date d'emprunt:</strong> {new Date(emprunt.date_emprunt).toLocaleDateString()}</p>\n                      <p><strong>Date de retour prévue:</strong> {new Date(emprunt.date_retour_prevue).toLocaleDateString()}</p>\n                      {emprunt.date_retour_effective && (\n                        <p><strong>Date de retour effective:</strong> {new Date(emprunt.date_retour_effective).toLocaleDateString()}</p>\n                      )}\n                    </div>\n\n                    {!emprunt.est_retourne && (\n                      <button\n                        className=\"action-button return\"\n                        onClick={() => handleRetourLivre(emprunt.id)}\n                        disabled={loading}\n                      >\n                        Retourner le livre\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'reservations' && (\n          <div className=\"reservations-list\">\n            <h2>Mes réservations</h2>\n\n            {reservations.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucune réservation en cours.</p>\n            ) : (\n              <div className=\"items-grid\">\n                {reservations.map(reservation => (\n                  <div key={reservation.id} className=\"item-card\">\n                    <div className=\"item-header\">\n                      <h3>{reservation.livre_titre}</h3>\n                      <span className={`status ${reservation.est_active ? 'active' : 'inactive'}`}>\n                        {reservation.est_active ? 'Active' : 'Inactive'}\n                      </span>\n                    </div>\n\n                    <div className=\"item-details\">\n                      <p><strong>Date de réservation:</strong> {new Date(reservation.date_reservation).toLocaleDateString()}</p>\n                    </div>\n\n                    {reservation.est_active && (\n                      <button\n                        className=\"action-button cancel\"\n                        onClick={() => handleAnnulerReservation(reservation.id)}\n                        disabled={loading}\n                      >\n                        Annuler la réservation\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'notifications' && (\n          <div className=\"notifications-list\">\n            <h2>Mes notifications</h2>\n\n            {notifications.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucune notification.</p>\n            ) : (\n              <div className=\"notifications-container\">\n                {notifications.map(notification => (\n                  <div\n                    key={notification.id}\n                    className={`notification-item ${notification.est_lue ? 'read' : 'unread'}`}\n                    onClick={() => !notification.est_lue && handleMarkNotificationRead(notification.id)}\n                  >\n                    <div className=\"notification-header\">\n                      <span className={`notification-type ${notification.type}`}>\n                        {notification.type === 'retard' && 'Retard'}\n                        {notification.type === 'rappel' && 'Rappel'}\n                        {notification.type === 'nouveau' && 'Nouveau'}\n                        {notification.type === 'emprunt' && 'Emprunt'}\n                        {notification.type === 'reservation' && 'Réservation'}\n                        {notification.type === 'disponible' && 'Disponible'}\n                      </span>\n                      <span className=\"notification-date\">\n                        {new Date(notification.date).toLocaleDateString()}\n                      </span>\n                    </div>\n\n                    <div className=\"notification-content\">\n                      <p>{notification.message}</p>\n                    </div>\n\n                    {notification.livre_id && (\n                      <a\n                        href={`/books/${notification.livre_id}`}\n                        className=\"notification-link\"\n                      >\n                        Voir le livre\n                      </a>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,eAAe,QAAQ,iBAAiB;AAC9D,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACpB,MAAM;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EACjC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAI,CAACV,WAAW,EAAE;UAChBwB,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;UAC9Ef,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAc,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEzB,WAAW,CAAC;QAE5D,IAAI;UACF;UACA,MAAM0B,gBAAgB,GAAG,MAAMlC,WAAW,CAACmC,MAAM,CAAC,CAAC;UACnDzB,WAAW,CAACwB,gBAAgB,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;QAClD,CAAC,CAAC,OAAOC,WAAW,EAAE;UACpBN,OAAO,CAACb,KAAK,CAAC,8CAA8C,EAAEmB,WAAW,CAAC;UAC1E5B,WAAW,CAAC,EAAE,CAAC;QACjB;QAEA,IAAI;UACF;UACA,MAAM6B,oBAAoB,GAAG,MAAMtC,eAAe,CAACkC,MAAM,CAAC,CAAC;UAC3DvB,eAAe,CAAC2B,oBAAoB,CAACH,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;QAC1D,CAAC,CAAC,OAAOG,eAAe,EAAE;UACxBR,OAAO,CAACb,KAAK,CAAC,kDAAkD,EAAEqB,eAAe,CAAC;UAClF5B,eAAe,CAAC,EAAE,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAM6B,qBAAqB,GAAG,MAAMC,eAAe,CAACC,gBAAgB,CAAC,CAAC;UACtE7B,gBAAgB,CAAC2B,qBAAqB,CAACL,IAAI,IAAI,EAAE,CAAC;QACpD,CAAC,CAAC,OAAOQ,gBAAgB,EAAE;UACzBZ,OAAO,CAACb,KAAK,CAAC,mDAAmD,EAAEyB,gBAAgB,CAAC;UACpF9B,gBAAgB,CAAC,EAAE,CAAC;QACtB;;QAEA;QACAQ,WAAW,CAAC;UACVC,UAAU,EAAEf,WAAW,CAACe,UAAU,IAAI,EAAE;UACxCC,SAAS,EAAEhB,WAAW,CAACgB,SAAS,IAAI,EAAE;UACtCC,KAAK,EAAEjB,WAAW,CAACiB,KAAK,IAAI,EAAE;UAC9BC,KAAK,EAAE;QACT,CAAC,CAAC;QAEF,IAAIlB,WAAW,CAACqC,OAAO,IAAIrC,WAAW,CAACqC,OAAO,CAACnB,KAAK,EAAE;UACpDE,eAAe,CAACpB,WAAW,CAACqC,OAAO,CAACnB,KAAK,CAAC;QAC5C;QAEAR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;QACZd,OAAO,CAACb,KAAK,CAAC,wCAAwC,EAAE2B,GAAG,CAAC;QAC5D1B,QAAQ,CAAC,sEAAsE,CAAC;QAChFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDa,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACvB,WAAW,CAAC,CAAC;EAEjB,MAAMuC,eAAe,GAAIC,GAAG,IAAK;IAC/BhC,YAAY,CAACgC,GAAG,CAAC;EACnB,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,iBAAiB,GAAIL,CAAC,IAAK;IAC/B,MAAMM,IAAI,GAAGN,CAAC,CAACG,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRlC,WAAW,CAACgC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP5B,KAAK,EAAE8B;MACT,CAAC,CAAC,CAAC;;MAEH;MACA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBhC,eAAe,CAAC8B,MAAM,CAACG,MAAM,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClBhC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBc,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,MAAMgC,WAAW,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAClClC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;MAE5B;MACA,MAAMkC,QAAQ,GAAG;QACf5C,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;QAC7BC,KAAK,EAAEJ,QAAQ,CAACI;MAClB,CAAC;MACDO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,QAAQ,CAAC;;MAEvD;MACAF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;MACpDnC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAIZ,QAAQ,CAACK,KAAK,EAAE;QAClBuC,WAAW,CAACG,MAAM,CAAC,OAAO,EAAE/C,QAAQ,CAACK,KAAK,CAAC;QAC3CM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEZ,QAAQ,CAACK,KAAK,CAACyB,IAAI,CAAC;MAChE,CAAC,MAAM;QACLnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACvC;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,KAAK,IAAIsC,IAAI,IAAIN,WAAW,CAACO,OAAO,CAAC,CAAC,EAAE;QACtCxC,OAAO,CAACC,GAAG,CAAC,sBAAsBsC,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;MAEAvC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,MAAMwC,QAAQ,GAAG,MAAM/B,eAAe,CAACgC,aAAa,CAACT,WAAW,CAAC;MACjEjC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwC,QAAQ,CAAC;MAEvCzC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CH,gBAAgB,CAAC,IAAI,CAAC;MACtB6C,UAAU,CAAC,MAAM;QACf7C,gBAAgB,CAAC,KAAK,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;MAERZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MAAA,IAAA8B,aAAA;MACZ5C,OAAO,CAACb,KAAK,CAAC,0CAA0C,EAAE2B,GAAG,CAAC;MAC9Dd,OAAO,CAACb,KAAK,CAAC,uBAAuB,GAAAyD,aAAA,GAAE9B,GAAG,CAAC2B,QAAQ,cAAAG,aAAA,uBAAZA,aAAA,CAAcxC,IAAI,CAAC;MAC1DhB,QAAQ,CAAC,wEAAwE,CAAC;MAClFF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2D,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACF5D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMlB,WAAW,CAAC+E,SAAS,CAACD,SAAS,CAAC;;MAEtC;MACA,MAAM5C,gBAAgB,GAAG,MAAMlC,WAAW,CAACmC,MAAM,CAAC,CAAC;MACnDzB,WAAW,CAACwB,gBAAgB,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;MAEhDnB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAE2B,GAAG,CAAC;MACrD1B,QAAQ,CAAC,+DAA+D,CAAC;MACzEF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8D,wBAAwB,GAAG,MAAOC,aAAa,IAAK;IACxD,IAAI;MACF/D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMjB,eAAe,CAACiF,OAAO,CAACD,aAAa,CAAC;;MAE5C;MACA,MAAM1C,oBAAoB,GAAG,MAAMtC,eAAe,CAACkC,MAAM,CAAC,CAAC;MAC3DvB,eAAe,CAAC2B,oBAAoB,CAACH,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;MAExDnB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,iDAAiD,EAAE2B,GAAG,CAAC;MACrE1B,QAAQ,CAAC,+EAA+E,CAAC;MACzFF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiE,0BAA0B,GAAG,MAAOC,cAAc,IAAK;IAC3D,IAAI;MACF,MAAM1C,eAAe,CAAC2C,oBAAoB,CAACD,cAAc,CAAC;;MAE1D;MACA,MAAM3C,qBAAqB,GAAG,MAAMC,eAAe,CAACC,gBAAgB,CAAC,CAAC;MACtE7B,gBAAgB,CAAC2B,qBAAqB,CAACL,IAAI,IAAI,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,6CAA6C,EAAE2B,GAAG,CAAC;IACnE;EACF,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKmF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCpF,OAAA;QAAKmF,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBpF,OAAA;UAAKmF,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCxF,OAAA;UAAAoF,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIxE,KAAK,EAAE;IACT,oBACEhB,OAAA;MAAKmF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCpF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpF,OAAA;UAAAoF,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCxF,OAAA;UAAAoF,QAAA,EAAIpE;QAAK;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdxF,OAAA;UACEmF,SAAS,EAAC,cAAc;UACxBM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACnF,WAAW,EAAE;IAChB,oBACEL,OAAA;MAAKmF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCpF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpF,OAAA;UAAAoF,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BxF,OAAA;UAAAoF,QAAA,EAAG;QAAkF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzFxF,OAAA,CAACL,IAAI;UAACkG,EAAE,EAAC,QAAQ;UAACV,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExF,OAAA;IAAKmF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCpF,OAAA;MAAKmF,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BpF,OAAA;QACEmF,SAAS,EAAE,cAAcvE,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QACnE6E,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,SAAS,CAAE;QAAAwC,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxF,OAAA;QACEmF,SAAS,EAAE,cAAcvE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACpE6E,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,UAAU,CAAE;QAAAwC,QAAA,GAC5C,WACU,EAAC9E,QAAQ,CAACwF,MAAM,GAAG,CAAC,iBAAI9F,OAAA;UAAMmF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE9E,QAAQ,CAACwF;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACTxF,OAAA;QACEmF,SAAS,EAAE,cAAcvE,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxE6E,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,cAAc,CAAE;QAAAwC,QAAA,GAChD,kBACc,EAAC5E,YAAY,CAACsF,MAAM,GAAG,CAAC,iBAAI9F,OAAA;UAAMmF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE5E,YAAY,CAACsF;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACTxF,OAAA;QACEmF,SAAS,EAAE,cAAcvE,SAAS,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;QACzE6E,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,eAAe,CAAE;QAAAwC,QAAA,GACjD,gBACe,EAAC1E,aAAa,CAACqF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,OAAO,CAAC,CAACH,MAAM,GAAG,CAAC,iBAC7D9F,OAAA;UAAMmF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE1E,aAAa,CAACqF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,OAAO,CAAC,CAACH;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENxF,OAAA;MAAKmF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7BxE,SAAS,KAAK,SAAS,iBACtBZ,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpF,OAAA;UAAKmF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpF,OAAA;YAAKmF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCpF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B5D,YAAY,gBACXxB,OAAA;gBAAKkG,GAAG,EAAE1E,YAAa;gBAAC2E,GAAG,EAAC;cAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEhDxF,OAAA;gBAAKkG,GAAG,EAAC,uGAAuG;gBAACC,GAAG,EAAC;cAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC1I;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxF,OAAA;cAAOoG,OAAO,EAAC,cAAc;cAACjB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eACxDpF,OAAA;gBAAGmF,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACRxF,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,cAAc;cACjBC,MAAM,EAAC,SAAS;cAChBC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAC3BC,QAAQ,EAAEtD;YAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BpF,OAAA;cAAAoF,QAAA,EAAK/E,WAAW,CAACsG;YAAQ;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/BxF,OAAA;cAAMmF,SAAS,EAAE,SAAS,EAAAhF,oBAAA,GAAAE,WAAW,CAACqC,OAAO,cAAAvC,oBAAA,uBAAnBA,oBAAA,CAAqByG,SAAS,MAAK,OAAO,IAAIvG,WAAW,CAACwG,YAAY,GAAG,OAAO,GAAG,UAAU,EAAG;cAAAzB,QAAA,EACvH,EAAAhF,qBAAA,GAAAC,WAAW,CAACqC,OAAO,cAAAtC,qBAAA,uBAAnBA,qBAAA,CAAqBwG,SAAS,MAAK,OAAO,IAAIvG,WAAW,CAACwG,YAAY,GAAG,gBAAgB,GAAG;YAAU;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL9D,aAAa,iBACZ1B,OAAA;UAAKmF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEjC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAEDxF,OAAA;UAAMmF,SAAS,EAAC,cAAc;UAAC2B,QAAQ,EAAElD,YAAa;UAAAwB,QAAA,gBACpDpF,OAAA;YAAKmF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBpF,OAAA;cAAKmF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpF,OAAA;gBAAOoG,OAAO,EAAC,YAAY;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CxF,OAAA;gBACEqG,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,YAAY;gBACftD,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE/B,QAAQ,CAACE,UAAW;gBAC3BsF,QAAQ,EAAE5D;cAAkB;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxF,OAAA;cAAKmF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpF,OAAA;gBAAOoG,OAAO,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCxF,OAAA;gBACEqG,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,WAAW;gBACdtD,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE/B,QAAQ,CAACG,SAAU;gBAC1BqF,QAAQ,EAAE5D;cAAkB;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAOoG,OAAO,EAAC,OAAO;cAAAhB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxF,OAAA;cACEqG,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVtD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE/B,QAAQ,CAACI,KAAM;cACtBoF,QAAQ,EAAE5D;YAAkB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAQqG,IAAI,EAAC,QAAQ;YAAClB,SAAS,EAAC,eAAe;YAAC4B,QAAQ,EAAEjG,OAAQ;YAAAsE,QAAA,EAC/DtE,OAAO,GAAG,gBAAgB,GAAG;UAAyB;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA5E,SAAS,KAAK,UAAU,iBACvBZ,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpF,OAAA;UAAAoF,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEpBlF,QAAQ,CAACwF,MAAM,KAAK,CAAC,gBACpB9F,OAAA;UAAGmF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE/DxF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB9E,QAAQ,CAAC0G,GAAG,CAACC,OAAO,iBACnBjH,OAAA;YAAsBmF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzCpF,OAAA;cAAKmF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpF,OAAA;gBAAAoF,QAAA,EAAK6B,OAAO,CAACC;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BxF,OAAA;gBAAMmF,SAAS,EAAE,UAAU8B,OAAO,CAACE,YAAY,GAAG,UAAU,GAAG,QAAQ,EAAG;gBAAA/B,QAAA,EACvE6B,OAAO,CAACE,YAAY,GAAG,UAAU,GAAG;cAAU;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxF,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI4B,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI4B,IAAI,CAACH,OAAO,CAACM,kBAAkB,CAAC,CAACD,kBAAkB,CAAC,CAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACzGyB,OAAO,CAACO,qBAAqB,iBAC5BxH,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI4B,IAAI,CAACH,OAAO,CAACO,qBAAqB,CAAC,CAACF,kBAAkB,CAAC,CAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAChH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL,CAACyB,OAAO,CAACE,YAAY,iBACpBnH,OAAA;cACEmF,SAAS,EAAC,sBAAsB;cAChCM,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAACuC,OAAO,CAACX,EAAE,CAAE;cAC7CS,QAAQ,EAAEjG,OAAQ;cAAAsE,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,GAxBOyB,OAAO,CAACX,EAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEA5E,SAAS,KAAK,cAAc,iBAC3BZ,OAAA;QAAKmF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpF,OAAA;UAAAoF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAExBhF,YAAY,CAACsF,MAAM,KAAK,CAAC,gBACxB9F,OAAA;UAAGmF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpExF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB5E,YAAY,CAACwG,GAAG,CAACS,WAAW,iBAC3BzH,OAAA;YAA0BmF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC7CpF,OAAA;cAAKmF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpF,OAAA;gBAAAoF,QAAA,EAAKqC,WAAW,CAACP;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCxF,OAAA;gBAAMmF,SAAS,EAAE,UAAUsC,WAAW,CAACC,UAAU,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAAtC,QAAA,EACzEqC,WAAW,CAACC,UAAU,GAAG,QAAQ,GAAG;cAAU;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxF,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI4B,IAAI,CAACK,WAAW,CAACE,gBAAgB,CAAC,CAACL,kBAAkB,CAAC,CAAC;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,EAELiC,WAAW,CAACC,UAAU,iBACrB1H,OAAA;cACEmF,SAAS,EAAC,sBAAsB;cAChCM,OAAO,EAAEA,CAAA,KAAMZ,wBAAwB,CAAC4C,WAAW,CAACnB,EAAE,CAAE;cACxDS,QAAQ,EAAEjG,OAAQ;cAAAsE,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,GApBOiC,WAAW,CAACnB,EAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBnB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEA5E,SAAS,KAAK,eAAe,iBAC5BZ,OAAA;QAAKmF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCpF,OAAA;UAAAoF,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEzB9E,aAAa,CAACoF,MAAM,KAAK,CAAC,gBACzB9F,OAAA;UAAGmF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE5DxF,OAAA;UAAKmF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrC1E,aAAa,CAACsG,GAAG,CAACY,YAAY,iBAC7B5H,OAAA;YAEEmF,SAAS,EAAE,qBAAqByC,YAAY,CAAC3B,OAAO,GAAG,MAAM,GAAG,QAAQ,EAAG;YAC3ER,OAAO,EAAEA,CAAA,KAAM,CAACmC,YAAY,CAAC3B,OAAO,IAAIjB,0BAA0B,CAAC4C,YAAY,CAACtB,EAAE,CAAE;YAAAlB,QAAA,gBAEpFpF,OAAA;cAAKmF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCpF,OAAA;gBAAMmF,SAAS,EAAE,qBAAqByC,YAAY,CAACvB,IAAI,EAAG;gBAAAjB,QAAA,GACvDwC,YAAY,CAACvB,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAC1CuB,YAAY,CAACvB,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAC1CuB,YAAY,CAACvB,IAAI,KAAK,SAAS,IAAI,SAAS,EAC5CuB,YAAY,CAACvB,IAAI,KAAK,SAAS,IAAI,SAAS,EAC5CuB,YAAY,CAACvB,IAAI,KAAK,aAAa,IAAI,aAAa,EACpDuB,YAAY,CAACvB,IAAI,KAAK,YAAY,IAAI,YAAY;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACPxF,OAAA;gBAAMmF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAChC,IAAIgC,IAAI,CAACQ,YAAY,CAACC,IAAI,CAAC,CAACP,kBAAkB,CAAC;cAAC;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxF,OAAA;cAAKmF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnCpF,OAAA;gBAAAoF,QAAA,EAAIwC,YAAY,CAACE;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,EAELoC,YAAY,CAACG,QAAQ,iBACpB/H,OAAA;cACEgI,IAAI,EAAE,UAAUJ,YAAY,CAACG,QAAQ,EAAG;cACxC5C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA,GA7BIoC,YAAY,CAACtB,EAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8BjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CA7eID,OAAO;EAAA,QACaL,OAAO;AAAA;AAAAqI,EAAA,GAD3BhI,OAAO;AA+eb,eAAeA,OAAO;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}