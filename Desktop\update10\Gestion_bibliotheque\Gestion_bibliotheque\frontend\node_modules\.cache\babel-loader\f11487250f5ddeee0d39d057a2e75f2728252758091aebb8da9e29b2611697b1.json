{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\EbookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookCard = ({\n  ebook\n}) => {\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL de l'image\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/ebooks/${ebook.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [ebook.image || ebook.static_image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: ebook.static_image_url ? ebook.static_image_url : config.getBookImageUrl(ebook.image),\n          alt: ebook.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-status green\",\n          children: ebook.format.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", ebook.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: ebook.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [\"Format: \", ebook.format.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = EbookCard;\nexport default EbookCard;\nvar _c;\n$RefreshReg$(_c, \"EbookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "EbookCard", "ebook", "console", "log", "id", "titre", "className", "children", "to", "image", "static_image_url", "src", "getBookImageUrl", "alt", "onError", "e", "error", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "format", "toUpperCase", "autheur", "category_name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/EbookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\n\nconst EbookCard = ({ ebook }) => {\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL de l'image\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/ebooks/${ebook.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {ebook.image || ebook.static_image_url ? (\n            <img\n              src={ebook.static_image_url ? ebook.static_image_url : config.getBookImageUrl(ebook.image)}\n              alt={ebook.titre}\n              onError={(e) => {\n                console.error(`Erreur de chargement d'image: ${e.target.src}`);\n                e.target.onerror = null;\n                e.target.src = config.DEFAULT_BOOK_IMAGE;\n              }}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className=\"book-card-status green\">\n            {ebook.format.toUpperCase()}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{ebook.titre}</h3>\n          <p className=\"book-card-author\">Par {ebook.autheur}</p>\n          <p className=\"book-card-category\">{ebook.category_name}</p>\n          <p className=\"book-card-availability\">\n            Format: {ebook.format.toUpperCase()}\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default EbookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB,CAAC,CAAC;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B;EACAC,OAAO,CAACC,GAAG,CAAC,yBAAyBF,KAAK,CAACG,EAAE,YAAYH,KAAK,CAACI,KAAK,EAAE,CAAC;EAEvE,oBACEN,OAAA;IAAKO,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBR,OAAA,CAACH,IAAI;MAACY,EAAE,EAAE,WAAWP,KAAK,CAACG,EAAE,EAAG;MAACE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACzDR,OAAA;QAAKO,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7BN,KAAK,CAACQ,KAAK,IAAIR,KAAK,CAACS,gBAAgB,gBACpCX,OAAA;UACEY,GAAG,EAAEV,KAAK,CAACS,gBAAgB,GAAGT,KAAK,CAACS,gBAAgB,GAAGb,MAAM,CAACe,eAAe,CAACX,KAAK,CAACQ,KAAK,CAAE;UAC3FI,GAAG,EAAEZ,KAAK,CAACI,KAAM;UACjBS,OAAO,EAAGC,CAAC,IAAK;YACdb,OAAO,CAACc,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACN,GAAG,EAAE,CAAC;YAC9DI,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACN,GAAG,GAAGd,MAAM,CAACsB,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFxB,OAAA;UAAKO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCR,OAAA;YAAAQ,QAAA,EAAM;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACDxB,OAAA;UAAKO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCN,KAAK,CAACuB,MAAM,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxB,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA;UAAIO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEN,KAAK,CAACI;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDxB,OAAA;UAAGO,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACN,KAAK,CAACyB,OAAO;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDxB,OAAA;UAAGO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEN,KAAK,CAAC0B;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DxB,OAAA;UAAGO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,UAC5B,EAACN,KAAK,CAACuB,MAAM,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GAtCI5B,SAAS;AAwCf,eAAeA,SAAS;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}