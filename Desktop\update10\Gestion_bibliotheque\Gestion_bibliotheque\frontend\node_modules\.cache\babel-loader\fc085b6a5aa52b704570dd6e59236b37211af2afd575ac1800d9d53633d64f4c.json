{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\Statistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { statisticsAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport Loading from '../components/Loading';\nimport './Statistics.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Statistics = () => {\n  _s();\n  const {\n    showError\n  } = useAlert();\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        const response = await statisticsAPI.getAll();\n        setStatistics(response.data);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des statistiques:', error);\n        showError('Impossible de charger les statistiques. Veuillez réessayer plus tard.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchStatistics();\n  }, [showError]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      fullScreen: true,\n      message: \"Chargement des statistiques...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 12\n    }, this);\n  }\n  if (!statistics) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"statistics-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Erreur de chargement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Impossible de charger les statistiques. Veuillez r\\xE9essayer plus tard.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Préparation des données pour l'affichage sans Chart.js\n  const topBooks = statistics.top_livres ? statistics.top_livres.labels.map((label, index) => ({\n    titre: label,\n    count: statistics.top_livres.values[index]\n  })) : [];\n  const topStudents = statistics.top_etudiants ? statistics.top_etudiants.labels.map((label, index) => ({\n    username: label,\n    retournes: statistics.top_etudiants.retournes[index],\n    non_retournes: statistics.top_etudiants.non_retournes[index],\n    total: statistics.top_etudiants.retournes[index] + statistics.top_etudiants.non_retournes[index]\n  })) : [];\n  const loansPerDay = statistics.emprunts_par_jour ? statistics.emprunts_par_jour.dates.map((date, index) => ({\n    date: date,\n    count: statistics.emprunts_par_jour.counts[index]\n  })) : [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"statistics-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"statistics-title\",\n      children: \"Statistiques de la Biblioth\\xE8que\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kpi-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-book\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total des Livres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: statistics.kpi.total_livres\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-money-bill-wave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Valeur Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: [statistics.kpi.valeur_totale.toFixed(2), \" MAD\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-calendar-check\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"R\\xE9servations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: statistics.kpi.total_reservations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exchange-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Emprunts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: statistics.kpi.livres_retournes + statistics.kpi.livres_non_retournes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-subtext\",\n            children: [statistics.kpi.livres_retournes, \" retourn\\xE9s, \", statistics.kpi.livres_non_retournes, \" en cours\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Top 10 des Livres les Plus Emprunt\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: topBooksChartData,\n            options: topBooksChartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Top 10 des Utilisateurs les Plus Actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: topStudentsChartData,\n            options: topStudentsChartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Emprunts par Jour (30 derniers jours)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: loansPerDayChartData,\n            options: loansPerDayChartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Statistiques des Utilisateurs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-responsive\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"statistics-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emprunts totaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emprunts en cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: statistics.stats_utilisateurs.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.total_emprunts\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.emprunts_en_cours\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(Statistics, \"CCH/vsbx/uF9PATHBKqBrATFNcI=\", false, function () {\n  return [useAlert];\n});\n_c = Statistics;\nexport default Statistics;\nvar _c;\n$RefreshReg$(_c, \"Statistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "statisticsAPI", "useAlert", "Loading", "jsxDEV", "_jsxDEV", "Statistics", "_s", "showError", "statistics", "setStatistics", "loading", "setLoading", "fetchStatistics", "response", "getAll", "data", "error", "console", "fullScreen", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "topBooks", "top_livres", "labels", "map", "label", "index", "titre", "count", "values", "topStudents", "top_etudiants", "username", "retournes", "non_retournes", "total", "loansPerDay", "emprunts_par_jour", "dates", "date", "counts", "kpi", "total_livres", "valeur_totale", "toFixed", "total_reservations", "livres_retournes", "livres_non_retournes", "Bar", "topBooksChartData", "options", "topBooksChartOptions", "topStudentsChartData", "topStudentsChartOptions", "Line", "loansPerDayChartData", "loansPerDayChartOptions", "stats_utilisateurs", "user", "type", "total_emprunts", "emprunts_en_cours", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/Statistics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { statisticsAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport Loading from '../components/Loading';\nimport './Statistics.css';\n\nconst Statistics = () => {\n  const { showError } = useAlert();\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        const response = await statisticsAPI.getAll();\n        setStatistics(response.data);\n      } catch (error) {\n        console.error('Erreur lors de la récupération des statistiques:', error);\n        showError('Impossible de charger les statistiques. Veuillez réessayer plus tard.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStatistics();\n  }, [showError]);\n\n  if (loading) {\n    return <Loading fullScreen message=\"Chargement des statistiques...\" />;\n  }\n\n  if (!statistics) {\n    return (\n      <div className=\"statistics-error\">\n        <h2>Erreur de chargement</h2>\n        <p>Impossible de charger les statistiques. Veuillez réessayer plus tard.</p>\n      </div>\n    );\n  }\n\n  // Préparation des données pour l'affichage sans Chart.js\n  const topBooks = statistics.top_livres ?\n    statistics.top_livres.labels.map((label, index) => ({\n      titre: label,\n      count: statistics.top_livres.values[index]\n    })) : [];\n\n  const topStudents = statistics.top_etudiants ?\n    statistics.top_etudiants.labels.map((label, index) => ({\n      username: label,\n      retournes: statistics.top_etudiants.retournes[index],\n      non_retournes: statistics.top_etudiants.non_retournes[index],\n      total: statistics.top_etudiants.retournes[index] + statistics.top_etudiants.non_retournes[index]\n    })) : [];\n\n  const loansPerDay = statistics.emprunts_par_jour ?\n    statistics.emprunts_par_jour.dates.map((date, index) => ({\n      date: date,\n      count: statistics.emprunts_par_jour.counts[index]\n    })) : [];\n\n  return (\n    <div className=\"statistics-page\">\n      <h1 className=\"statistics-title\">Statistiques de la Bibliothèque</h1>\n\n      {/* KPI Cards */}\n      <div className=\"kpi-container\">\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-book\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Total des Livres</h3>\n            <p className=\"kpi-value\">{statistics.kpi.total_livres}</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Valeur Totale</h3>\n            <p className=\"kpi-value\">{statistics.kpi.valeur_totale.toFixed(2)} MAD</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-calendar-check\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Réservations</h3>\n            <p className=\"kpi-value\">{statistics.kpi.total_reservations}</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-exchange-alt\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Emprunts</h3>\n            <p className=\"kpi-value\">{statistics.kpi.livres_retournes + statistics.kpi.livres_non_retournes}</p>\n            <p className=\"kpi-subtext\">\n              {statistics.kpi.livres_retournes} retournés, {statistics.kpi.livres_non_retournes} en cours\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts */}\n      <div className=\"charts-container\">\n        <div className=\"chart-section\">\n          <h2>Top 10 des Livres les Plus Empruntés</h2>\n          <div className=\"chart-wrapper\">\n            <Bar data={topBooksChartData} options={topBooksChartOptions} />\n          </div>\n        </div>\n\n        <div className=\"chart-section\">\n          <h2>Top 10 des Utilisateurs les Plus Actifs</h2>\n          <div className=\"chart-wrapper\">\n            <Bar data={topStudentsChartData} options={topStudentsChartOptions} />\n          </div>\n        </div>\n\n        <div className=\"chart-section\">\n          <h2>Emprunts par Jour (30 derniers jours)</h2>\n          <div className=\"chart-wrapper\">\n            <Line data={loansPerDayChartData} options={loansPerDayChartOptions} />\n          </div>\n        </div>\n      </div>\n\n      {/* User Statistics Table */}\n      <div className=\"table-container\">\n        <h2>Statistiques des Utilisateurs</h2>\n        <div className=\"table-responsive\">\n          <table className=\"statistics-table\">\n            <thead>\n              <tr>\n                <th>Utilisateur</th>\n                <th>Type</th>\n                <th>Emprunts totaux</th>\n                <th>Emprunts en cours</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statistics.stats_utilisateurs.map((user, index) => (\n                <tr key={index}>\n                  <td>{user.username}</td>\n                  <td>{user.type}</td>\n                  <td>{user.total_emprunts}</td>\n                  <td>{user.emprunts_en_cours}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Statistics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAU,CAAC,GAAGN,QAAQ,CAAC,CAAC;EAChC,MAAM,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAMb,aAAa,CAACc,MAAM,CAAC,CAAC;QAC7CL,aAAa,CAACI,QAAQ,CAACE,IAAI,CAAC;MAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxET,SAAS,CAAC,uEAAuE,CAAC;MACpF,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,IAAIG,OAAO,EAAE;IACX,oBAAON,OAAA,CAACF,OAAO;MAACgB,UAAU;MAACC,OAAO,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxE;EAEA,IAAI,CAACf,UAAU,EAAE;IACf,oBACEJ,OAAA;MAAKoB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BrB,OAAA;QAAAqB,QAAA,EAAI;MAAoB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BnB,OAAA;QAAAqB,QAAA,EAAG;MAAqE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC;EAEV;;EAEA;EACA,MAAMG,QAAQ,GAAGlB,UAAU,CAACmB,UAAU,GACpCnB,UAAU,CAACmB,UAAU,CAACC,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IAClDC,KAAK,EAAEF,KAAK;IACZG,KAAK,EAAEzB,UAAU,CAACmB,UAAU,CAACO,MAAM,CAACH,KAAK;EAC3C,CAAC,CAAC,CAAC,GAAG,EAAE;EAEV,MAAMI,WAAW,GAAG3B,UAAU,CAAC4B,aAAa,GAC1C5B,UAAU,CAAC4B,aAAa,CAACR,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IACrDM,QAAQ,EAAEP,KAAK;IACfQ,SAAS,EAAE9B,UAAU,CAAC4B,aAAa,CAACE,SAAS,CAACP,KAAK,CAAC;IACpDQ,aAAa,EAAE/B,UAAU,CAAC4B,aAAa,CAACG,aAAa,CAACR,KAAK,CAAC;IAC5DS,KAAK,EAAEhC,UAAU,CAAC4B,aAAa,CAACE,SAAS,CAACP,KAAK,CAAC,GAAGvB,UAAU,CAAC4B,aAAa,CAACG,aAAa,CAACR,KAAK;EACjG,CAAC,CAAC,CAAC,GAAG,EAAE;EAEV,MAAMU,WAAW,GAAGjC,UAAU,CAACkC,iBAAiB,GAC9ClC,UAAU,CAACkC,iBAAiB,CAACC,KAAK,CAACd,GAAG,CAAC,CAACe,IAAI,EAAEb,KAAK,MAAM;IACvDa,IAAI,EAAEA,IAAI;IACVX,KAAK,EAAEzB,UAAU,CAACkC,iBAAiB,CAACG,MAAM,CAACd,KAAK;EAClD,CAAC,CAAC,CAAC,GAAG,EAAE;EAEV,oBACE3B,OAAA;IAAKoB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BrB,OAAA;MAAIoB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGrEnB,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BrB,OAAA;QAAKoB,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrB,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBrB,OAAA;YAAGoB,SAAS,EAAC;UAAa;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNnB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAAqB,QAAA,EAAI;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEjB,UAAU,CAACsC,GAAG,CAACC;UAAY;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKoB,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrB,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBrB,OAAA;YAAGoB,SAAS,EAAC;UAAwB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNnB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAAqB,QAAA,EAAI;UAAa;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBnB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAEjB,UAAU,CAACsC,GAAG,CAACE,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKoB,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrB,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBrB,OAAA;YAAGoB,SAAS,EAAC;UAAuB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACNnB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAAqB,QAAA,EAAI;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBnB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEjB,UAAU,CAACsC,GAAG,CAACI;UAAkB;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKoB,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrB,OAAA;UAAKoB,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBrB,OAAA;YAAGoB,SAAS,EAAC;UAAqB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNnB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrB,OAAA;YAAAqB,QAAA,EAAI;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBnB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEjB,UAAU,CAACsC,GAAG,CAACK,gBAAgB,GAAG3C,UAAU,CAACsC,GAAG,CAACM;UAAoB;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpGnB,OAAA;YAAGoB,SAAS,EAAC,aAAa;YAAAC,QAAA,GACvBjB,UAAU,CAACsC,GAAG,CAACK,gBAAgB,EAAC,iBAAY,EAAC3C,UAAU,CAACsC,GAAG,CAACM,oBAAoB,EAAC,WACpF;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnB,OAAA;MAAKoB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BrB,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrB,OAAA;UAAAqB,QAAA,EAAI;QAAoC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CnB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrB,OAAA,CAACiD,GAAG;YAACtC,IAAI,EAAEuC,iBAAkB;YAACC,OAAO,EAAEC;UAAqB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrB,OAAA;UAAAqB,QAAA,EAAI;QAAuC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDnB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrB,OAAA,CAACiD,GAAG;YAACtC,IAAI,EAAE0C,oBAAqB;YAACF,OAAO,EAAEG;UAAwB;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrB,OAAA;UAAAqB,QAAA,EAAI;QAAqC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CnB,OAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrB,OAAA,CAACuD,IAAI;YAAC5C,IAAI,EAAE6C,oBAAqB;YAACL,OAAO,EAAEM;UAAwB;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnB,OAAA;MAAKoB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BrB,OAAA;QAAAqB,QAAA,EAAI;MAA6B;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCnB,OAAA;QAAKoB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BrB,OAAA;UAAOoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACjCrB,OAAA;YAAAqB,QAAA,eACErB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAAqB,QAAA,EAAI;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBnB,OAAA;gBAAAqB,QAAA,EAAI;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbnB,OAAA;gBAAAqB,QAAA,EAAI;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBnB,OAAA;gBAAAqB,QAAA,EAAI;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnB,OAAA;YAAAqB,QAAA,EACGjB,UAAU,CAACsD,kBAAkB,CAACjC,GAAG,CAAC,CAACkC,IAAI,EAAEhC,KAAK,kBAC7C3B,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAAqB,QAAA,EAAKsC,IAAI,CAAC1B;cAAQ;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBnB,OAAA;gBAAAqB,QAAA,EAAKsC,IAAI,CAACC;cAAI;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBnB,OAAA;gBAAAqB,QAAA,EAAKsC,IAAI,CAACE;cAAc;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BnB,OAAA;gBAAAqB,QAAA,EAAKsC,IAAI,CAACG;cAAiB;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAJ1BQ,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA9JID,UAAU;EAAA,QACQJ,QAAQ;AAAA;AAAAkE,EAAA,GAD1B9D,UAAU;AAgKhB,eAAeA,UAAU;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}