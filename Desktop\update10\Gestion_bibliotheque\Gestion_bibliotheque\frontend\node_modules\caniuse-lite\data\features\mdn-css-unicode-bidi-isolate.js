module.exports={A:{D:{"1":"0 9 nB oB pB qB rB sB tB uB vB wB xB OC yB PC zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB GC RC SC","2":"J RB K D E F A B C L M G","33":"1 2 3 4 5 6 7 8 N O P SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB"},L:{"1":"I"},B:{"1":"0 9 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB","2":"C L M G N O P"},C:{"1":"0 9 pB qB rB sB tB uB vB wB xB OC yB PC zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB GC RC SC qC rC","2":"pC NC J RB K D E F sC tC","33":"1 2 3 4 5 6 7 8 A B C L M G N O P SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB"},M:{"1":"GC"},A:{"2":"K D E F A B oC"},F:{"1":"0 aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","2":"F B C 6C 7C 8C 9C HC mC AD IC","33":"1 2 3 4 5 6 7 8 G N O P SB TB UB VB WB XB YB ZB"},K:{"1":"H","2":"A B C HC mC IC"},E:{"1":"B C L M G HC IC zC 0C 1C VC WC JC 2C KC XC YC ZC aC bC 3C LC cC dC eC fC gC 4C MC hC iC jC kC lC","2":"J RB uC TC vC 5C","33":"K D E F A wC xC yC UC"},G:{"1":"KD LD MD ND OD PD QD RD SD TD UD VC WC JC VD KC XC YC ZC aC bC WD LC cC dC eC fC gC XD MC hC iC jC kC lC","2":"TC BD nC CD","33":"E DD ED FD GD HD ID JD"},P:{"1":"1 2 3 4 5 6 7 8 fD gD hD iD jD UC kD lD mD nD oD KC LC MC pD","2":"J"},I:{"1":"I","2":"NC J ZD aD bD cD nC dD eD"}},B:6,C:"isolate from unicode-bidi",D:undefined};
