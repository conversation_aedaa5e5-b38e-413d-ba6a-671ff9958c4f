{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\EbookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookCard = ({\n  ebook\n}) => {\n  // Log des informations de l'ebook\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n\n  // Utiliser une URL directe pour l'image basée sur l'ID de l'ebook\n  const imageUrl = `http://localhost:8000/static/images/ebooks/ebook_${ebook.id}.jpg`;\n  console.log(`EbookCard - URL d'image directe: ${imageUrl}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/ebooks/${ebook.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: ebook.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-status green\",\n          children: ebook.format.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", ebook.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: ebook.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [\"Format: \", ebook.format.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_c = EbookCard;\nexport default EbookCard;\nvar _c;\n$RefreshReg$(_c, \"EbookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "EbookCard", "ebook", "console", "log", "id", "titre", "imageUrl", "className", "children", "to", "src", "alt", "onError", "e", "error", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "format", "toUpperCase", "autheur", "category_name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/EbookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\n\nconst EbookCard = ({ ebook }) => {\n  // Log des informations de l'ebook\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n\n  // Utiliser une URL directe pour l'image basée sur l'ID de l'ebook\n  const imageUrl = `http://localhost:8000/static/images/ebooks/ebook_${ebook.id}.jpg`;\n\n  console.log(`EbookCard - URL d'image directe: ${imageUrl}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/ebooks/${ebook.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          <img\n            src={imageUrl}\n            alt={ebook.titre}\n            onError={(e) => {\n              console.error(`Erreur de chargement d'image: ${e.target.src}`);\n              e.target.onerror = null;\n              e.target.src = config.DEFAULT_BOOK_IMAGE;\n            }}\n          />\n          <div className=\"book-card-status green\">\n            {ebook.format.toUpperCase()}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{ebook.titre}</h3>\n          <p className=\"book-card-author\">Par {ebook.autheur}</p>\n          <p className=\"book-card-category\">{ebook.category_name}</p>\n          <p className=\"book-card-availability\">\n            Format: {ebook.format.toUpperCase()}\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default EbookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB,CAAC,CAAC;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B;EACAC,OAAO,CAACC,GAAG,CAAC,yBAAyBF,KAAK,CAACG,EAAE,YAAYH,KAAK,CAACI,KAAK,EAAE,CAAC;;EAEvE;EACA,MAAMC,QAAQ,GAAG,oDAAoDL,KAAK,CAACG,EAAE,MAAM;EAEnFF,OAAO,CAACC,GAAG,CAAC,oCAAoCG,QAAQ,EAAE,CAAC;EAE3D,oBACEP,OAAA;IAAKQ,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBT,OAAA,CAACH,IAAI;MAACa,EAAE,EAAE,WAAWR,KAAK,CAACG,EAAE,EAAG;MAACG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACzDT,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BT,OAAA;UACEW,GAAG,EAAEJ,QAAS;UACdK,GAAG,EAAEV,KAAK,CAACI,KAAM;UACjBO,OAAO,EAAGC,CAAC,IAAK;YACdX,OAAO,CAACY,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACL,GAAG,EAAE,CAAC;YAC9DG,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACL,GAAG,GAAGb,MAAM,CAACoB,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtB,OAAA;UAAKQ,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCP,KAAK,CAACqB,MAAM,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtB,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCT,OAAA;UAAIQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEP,KAAK,CAACI;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDtB,OAAA;UAAGQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACP,KAAK,CAACuB,OAAO;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDtB,OAAA;UAAGQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEP,KAAK,CAACwB;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DtB,OAAA;UAAGQ,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,UAC5B,EAACP,KAAK,CAACqB,MAAM,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GArCI1B,SAAS;AAuCf,eAAeA,SAAS;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}