{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\BookDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport SimilarBooks from '../components/SimilarBooks';\nimport config from '../config';\nimport './BookDetail.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookDetail = ({\n  showAlert\n}) => {\n  _s();\n  var _currentUser$profile;\n  const {\n    id\n  } = useParams();\n  const [book, setBook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [actionMessage, setActionMessage] = useState({\n    text: '',\n    type: ''\n  });\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    const fetchBook = async () => {\n      try {\n        setLoading(true);\n        const response = await livresAPI.getById(id);\n        setBook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement du livre:', err);\n        setError('Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        if (showAlert) {\n          showAlert('error', 'Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        }\n        setLoading(false);\n      }\n    };\n    fetchBook();\n  }, [id, showAlert]);\n  const handleEmprunt = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.emprunter(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n\n      // Mettre à jour les données du livre\n      const updatedBook = await livresAPI.getById(id);\n      setBook(updatedBook.data);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Erreur lors de l\\'emprunt:', err);\n      setActionMessage({\n        text: ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || 'Erreur lors de l\\'emprunt. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({\n          text: '',\n          type: ''\n        });\n      }, 5000);\n    }\n  };\n  const handleReservation = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.reserver(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Erreur lors de la réservation:', err);\n      setActionMessage({\n        text: ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || 'Erreur lors de la réservation. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({\n          text: '',\n          type: ''\n        });\n      }, 5000);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      message: \"Chargement du livre...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  if (!book) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: \"Livre non trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/books\",\n        className: \"back-link\",\n        children: \"Retour \\xE0 la liste des livres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-detail-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/books\",\n        className: \"back-button\",\n        children: \"\\u2190 Retour aux livres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-actions\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/books/${id}/edit`,\n          className: \"edit-button\",\n          children: \"Modifier\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), actionMessage.text && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `action-message ${actionMessage.type}`,\n      children: actionMessage.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-image\",\n        children: book.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: book.image,\n          alt: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"book-detail-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Auteur:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 16\n            }, this), \" \", book.autheur]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Cat\\xE9gorie:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 16\n            }, this), \" \", book.category_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"ISBN:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 16\n            }, this), \" \", book.isbn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date de publication:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 16\n            }, this), \" \", new Date(book.date_publication).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Prix:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 16\n            }, this), \" \", book.price, \" \\u20AC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Disponibilit\\xE9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), \" \", book.quantitie_Dispo, \" / \", book.quantitie_Total, \" exemplaires disponibles\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: book.desc\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-actions\",\n          children: [book.quantitie_Dispo > 0 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button borrow\",\n            onClick: handleEmprunt,\n            disabled: actionLoading,\n            children: actionLoading ? 'En cours...' : 'Emprunter'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button reserve\",\n            onClick: handleReservation,\n            disabled: actionLoading,\n            children: actionLoading ? 'En cours...' : 'Réserver'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), book.url && book.url !== 'https://www.google.com' && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: book.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"action-button preview\",\n            children: \"Aper\\xE7u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SimilarBooks, {\n      bookId: id,\n      showAlert: showAlert\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(BookDetail, \"iT1jEivz54VDAj0mtViD4zJZuBk=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = BookDetail;\nexport default BookDetail;\nvar _c;\n$RefreshReg$(_c, \"BookDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "livresAPI", "useAuth", "Loading", "SimilarBooks", "config", "jsxDEV", "_jsxDEV", "BookDetail", "show<PERSON><PERSON><PERSON>", "_s", "_currentUser$profile", "id", "book", "setBook", "loading", "setLoading", "error", "setError", "actionLoading", "setActionLoading", "actionMessage", "setActionMessage", "text", "type", "isAuthenticated", "currentUser", "navigate", "isAdmin", "profile", "user_type", "is_superuser", "fetchBook", "response", "getById", "data", "err", "console", "handleEmprunt", "emprunter", "detail", "updatedBook", "_err$response", "_err$response$data", "setTimeout", "handleReservation", "reserver", "_err$response2", "_err$response2$data", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "to", "image", "src", "alt", "titre", "autheur", "category_name", "isbn", "Date", "date_publication", "toLocaleDateString", "price", "quantitie_Dispo", "quantitie_Total", "desc", "disabled", "url", "href", "target", "rel", "bookId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/BookDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport SimilarBooks from '../components/SimilarBooks';\nimport config from '../config';\nimport './BookDetail.css';\n\nconst BookDetail = ({ showAlert }) => {\n  const { id } = useParams();\n  const [book, setBook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [actionMessage, setActionMessage] = useState({ text: '', type: '' });\n\n  const { isAuthenticated, currentUser } = useAuth();\n  const navigate = useNavigate();\n\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    const fetchBook = async () => {\n      try {\n        setLoading(true);\n        const response = await livresAPI.getById(id);\n        setBook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement du livre:', err);\n        setError('Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        if (showAlert) {\n          showAlert('error', 'Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        }\n        setLoading(false);\n      }\n    };\n\n    fetchBook();\n  }, [id, showAlert]);\n\n  const handleEmprunt = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.emprunter(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n\n      // Mettre à jour les données du livre\n      const updatedBook = await livresAPI.getById(id);\n      setBook(updatedBook.data);\n    } catch (err) {\n      console.error('Erreur lors de l\\'emprunt:', err);\n      setActionMessage({\n        text: err.response?.data?.detail || 'Erreur lors de l\\'emprunt. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({ text: '', type: '' });\n      }, 5000);\n    }\n  };\n\n  const handleReservation = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.reserver(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n    } catch (err) {\n      console.error('Erreur lors de la réservation:', err);\n      setActionMessage({\n        text: err.response?.data?.detail || 'Erreur lors de la réservation. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({ text: '', type: '' });\n      }, 5000);\n    }\n  };\n\n  if (loading) {\n    return <Loading message=\"Chargement du livre...\" />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">{error}</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  if (!book) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">Livre non trouvé</p>\n        <Link to=\"/books\" className=\"back-link\">\n          Retour à la liste des livres\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"book-detail-container\">\n      <div className=\"book-detail-header\">\n        <Link to=\"/books\" className=\"back-button\">\n          &larr; Retour aux livres\n        </Link>\n\n        {isAdmin && (\n          <div className=\"admin-actions\">\n            <Link to={`/books/${id}/edit`} className=\"edit-button\">\n              Modifier\n            </Link>\n          </div>\n        )}\n      </div>\n\n      {actionMessage.text && (\n        <div className={`action-message ${actionMessage.type}`}>\n          {actionMessage.text}\n        </div>\n      )}\n\n      <div className=\"book-detail-content\">\n        <div className=\"book-detail-image\">\n          {book.image ? (\n            <img src={book.image} alt={book.titre} />\n          ) : (\n            <div className=\"book-detail-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"book-detail-info\">\n          <h1 className=\"book-detail-title\">{book.titre}</h1>\n\n          <div className=\"book-detail-meta\">\n            <p><strong>Auteur:</strong> {book.autheur}</p>\n            <p><strong>Catégorie:</strong> {book.category_name}</p>\n            <p><strong>ISBN:</strong> {book.isbn}</p>\n            <p><strong>Date de publication:</strong> {new Date(book.date_publication).toLocaleDateString()}</p>\n            <p><strong>Prix:</strong> {book.price} €</p>\n            <p className={`book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`}>\n              <strong>Disponibilité:</strong> {book.quantitie_Dispo} / {book.quantitie_Total} exemplaires disponibles\n            </p>\n          </div>\n\n          <div className=\"book-detail-description\">\n            <h3>Description</h3>\n            <p>{book.desc}</p>\n          </div>\n\n          <div className=\"book-detail-actions\">\n            {book.quantitie_Dispo > 0 ? (\n              <button\n                className=\"action-button borrow\"\n                onClick={handleEmprunt}\n                disabled={actionLoading}\n              >\n                {actionLoading ? 'En cours...' : 'Emprunter'}\n              </button>\n            ) : (\n              <button\n                className=\"action-button reserve\"\n                onClick={handleReservation}\n                disabled={actionLoading}\n              >\n                {actionLoading ? 'En cours...' : 'Réserver'}\n              </button>\n            )}\n\n            {book.url && book.url !== 'https://www.google.com' && (\n              <a\n                href={book.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"action-button preview\"\n              >\n                Aperçu\n              </a>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Livres similaires */}\n      <SimilarBooks bookId={id} showAlert={showAlert} />\n    </div>\n  );\n};\n\nexport default BookDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE1E,MAAM;IAAEC,eAAe;IAAEC;EAAY,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAClD,MAAMyB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAE9B,MAAM4B,OAAO,GAAG,CAAAF,WAAW,aAAXA,WAAW,wBAAAf,oBAAA,GAAXe,WAAW,CAAEG,OAAO,cAAAlB,oBAAA,uBAApBA,oBAAA,CAAsBmB,SAAS,MAAK,OAAO,KAAIJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,YAAY;EAExFlC,SAAS,CAAC,MAAM;IACd,MAAMmC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFhB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMiB,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,OAAO,CAACtB,EAAE,CAAC;QAC5CE,OAAO,CAACmB,QAAQ,CAACE,IAAI,CAAC;QACtBnB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZC,OAAO,CAACpB,KAAK,CAAC,qCAAqC,EAAEmB,GAAG,CAAC;QACzDlB,QAAQ,CAAC,mEAAmE,CAAC;QAC7E,IAAIT,SAAS,EAAE;UACbA,SAAS,CAAC,OAAO,EAAE,mEAAmE,CAAC;QACzF;QACAO,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACpB,EAAE,EAAEH,SAAS,CAAC,CAAC;EAEnB,MAAM6B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACb,eAAe,EAAE;MACpBE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI;MACFP,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMa,QAAQ,GAAG,MAAMhC,SAAS,CAACsC,SAAS,CAAC3B,EAAE,CAAC;MAC9CU,gBAAgB,CAAC;QACfC,IAAI,EAAEU,QAAQ,CAACE,IAAI,CAACK,MAAM;QAC1BhB,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,MAAMiB,WAAW,GAAG,MAAMxC,SAAS,CAACiC,OAAO,CAACtB,EAAE,CAAC;MAC/CE,OAAO,CAAC2B,WAAW,CAACN,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAM,aAAA,EAAAC,kBAAA;MACZN,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEmB,GAAG,CAAC;MAChDd,gBAAgB,CAAC;QACfC,IAAI,EAAE,EAAAmB,aAAA,GAAAN,GAAG,CAACH,QAAQ,cAAAS,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcP,IAAI,cAAAQ,kBAAA,uBAAlBA,kBAAA,CAAoBH,MAAM,KAAI,gDAAgD;QACpFhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,SAAS;MACRJ,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAwB,UAAU,CAAC,MAAM;QACftB,gBAAgB,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;MAC1C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpB,eAAe,EAAE;MACpBE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI;MACFP,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMa,QAAQ,GAAG,MAAMhC,SAAS,CAAC6C,QAAQ,CAAClC,EAAE,CAAC;MAC7CU,gBAAgB,CAAC;QACfC,IAAI,EAAEU,QAAQ,CAACE,IAAI,CAACK,MAAM;QAC1BhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,GAAG,EAAE;MAAA,IAAAW,cAAA,EAAAC,mBAAA;MACZX,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEmB,GAAG,CAAC;MACpDd,gBAAgB,CAAC;QACfC,IAAI,EAAE,EAAAwB,cAAA,GAAAX,GAAG,CAACH,QAAQ,cAAAc,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBR,MAAM,KAAI,oDAAoD;QACxFhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,SAAS;MACRJ,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAwB,UAAU,CAAC,MAAM;QACftB,gBAAgB,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;MAC1C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACJ,OAAO;MAAC8C,OAAO,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,IAAIpC,KAAK,EAAE;IACT,oBACEV,OAAA;MAAK+C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhD,OAAA;QAAG+C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEtC;MAAK;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC9C,OAAA;QACE+C,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACxC,IAAI,EAAE;IACT,oBACEN,OAAA;MAAK+C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BhD,OAAA;QAAG+C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACjD9C,OAAA,CAACR,IAAI;QAAC6D,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAExC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACE9C,OAAA;IAAK+C,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpChD,OAAA;MAAK+C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjChD,OAAA,CAACR,IAAI;QAAC6D,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE1C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAENzB,OAAO,iBACNrB,OAAA;QAAK+C,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BhD,OAAA,CAACR,IAAI;UAAC6D,EAAE,EAAE,UAAUhD,EAAE,OAAQ;UAAC0C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEvD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELhC,aAAa,CAACE,IAAI,iBACjBhB,OAAA;MAAK+C,SAAS,EAAE,kBAAkBjC,aAAa,CAACG,IAAI,EAAG;MAAA+B,QAAA,EACpDlC,aAAa,CAACE;IAAI;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN,eAED9C,OAAA;MAAK+C,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClChD,OAAA;QAAK+C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/B1C,IAAI,CAACgD,KAAK,gBACTtD,OAAA;UAAKuD,GAAG,EAAEjD,IAAI,CAACgD,KAAM;UAACE,GAAG,EAAElD,IAAI,CAACmD;QAAM;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzC9C,OAAA;UAAK+C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnChD,OAAA;YAAAgD,QAAA,EAAM;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN9C,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhD,OAAA;UAAI+C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAE1C,IAAI,CAACmD;QAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEnD9C,OAAA;UAAK+C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BhD,OAAA;YAAAgD,QAAA,gBAAGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAACoD,OAAO;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9C9C,OAAA;YAAAgD,QAAA,gBAAGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAACqD,aAAa;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvD9C,OAAA;YAAAgD,QAAA,gBAAGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAACsD,IAAI;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzC9C,OAAA;YAAAgD,QAAA,gBAAGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAoB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIe,IAAI,CAACvD,IAAI,CAACwD,gBAAgB,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnG9C,OAAA;YAAAgD,QAAA,gBAAGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC0D,KAAK,EAAC,SAAE;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5C9C,OAAA;YAAG+C,SAAS,EAAE,4BAA4BzC,IAAI,CAAC2D,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,EAAG;YAAAjB,QAAA,gBACjGhD,OAAA;cAAAgD,QAAA,EAAQ;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC2D,eAAe,EAAC,KAAG,EAAC3D,IAAI,CAAC4D,eAAe,EAAC,0BACjF;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9C,OAAA;UAAK+C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtChD,OAAA;YAAAgD,QAAA,EAAI;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB9C,OAAA;YAAAgD,QAAA,EAAI1C,IAAI,CAAC6D;UAAI;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEN9C,OAAA;UAAK+C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjC1C,IAAI,CAAC2D,eAAe,GAAG,CAAC,gBACvBjE,OAAA;YACE+C,SAAS,EAAC,sBAAsB;YAChCE,OAAO,EAAElB,aAAc;YACvBqC,QAAQ,EAAExD,aAAc;YAAAoC,QAAA,EAEvBpC,aAAa,GAAG,aAAa,GAAG;UAAW;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,gBAET9C,OAAA;YACE+C,SAAS,EAAC,uBAAuB;YACjCE,OAAO,EAAEX,iBAAkB;YAC3B8B,QAAQ,EAAExD,aAAc;YAAAoC,QAAA,EAEvBpC,aAAa,GAAG,aAAa,GAAG;UAAU;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACT,EAEAxC,IAAI,CAAC+D,GAAG,IAAI/D,IAAI,CAAC+D,GAAG,KAAK,wBAAwB,iBAChDrE,OAAA;YACEsE,IAAI,EAAEhE,IAAI,CAAC+D,GAAI;YACfE,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBzB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClC;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACH,YAAY;MAAC4E,MAAM,EAAEpE,EAAG;MAACH,SAAS,EAAEA;IAAU;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEV,CAAC;AAAC3C,EAAA,CArNIF,UAAU;EAAA,QACCV,SAAS,EAOiBI,OAAO,EAC/BF,WAAW;AAAA;AAAAiF,EAAA,GATxBzE,UAAU;AAuNhB,eAAeA,UAAU;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}