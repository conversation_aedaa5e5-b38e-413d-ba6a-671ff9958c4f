{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { FaHome, FaBook, FaTablet, FaUser, FaSignOutAlt, FaChartBar } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  _s();\n  var _currentUser$profile, _currentUser$profile2;\n  const location = useLocation();\n  const {\n    currentUser,\n    logout\n  } = useAuth();\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  const handleLogout = () => {\n    logout();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-icon\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            textDecoration: 'none',\n            color: 'inherit'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"BiblioDesk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"sidebar-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        className: `sidebar-list-item ${location.pathname === '/' ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: [/*#__PURE__*/_jsxDEV(FaHome, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), \" Accueil\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: `sidebar-list-item ${location.pathname === '/books' ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/books\",\n          children: [/*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), \" Livres\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: `sidebar-list-item ${location.pathname === '/ebooks' ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/ebooks\",\n          children: [/*#__PURE__*/_jsxDEV(FaTablet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), \" E-books\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: `sidebar-list-item ${location.pathname === '/statistics' ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/statistics\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartBar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), \" Statistiques\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: `sidebar-list-item ${location.pathname === '/profile' ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profile\",\n          children: [/*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), \" Profil\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"sidebar-list-item\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"#\",\n          onClick: handleLogout,\n          children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), \" D\\xE9connexion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-info-picture\",\n        children: currentUser !== null && currentUser !== void 0 && (_currentUser$profile2 = currentUser.profile) !== null && _currentUser$profile2 !== void 0 && _currentUser$profile2.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: currentUser.profile.photo,\n          alt: \"Avatar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png\",\n          alt: \"Avatar par d\\xE9faut\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-info-name\",\n        children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"7StED+yPSM0NX3yW1UCBvE6JQ0M=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useAuth", "FaHome", "FaBook", "FaTablet", "FaUser", "FaSignOutAlt", "FaChartBar", "jsxDEV", "_jsxDEV", "Sidebar", "_s", "_currentUser$profile", "_currentUser$profile2", "location", "currentUser", "logout", "isAdmin", "profile", "user_type", "is_superuser", "handleLogout", "className", "children", "to", "style", "textDecoration", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pathname", "onClick", "photo", "src", "alt", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { FaHome, FaBook, FaTablet, FaUser, FaSignOutAlt, FaChartBar } from 'react-icons/fa';\n\nconst Sidebar = () => {\n  const location = useLocation();\n  const { currentUser, logout } = useAuth();\n\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  return (\n    <div className=\"sidebar\">\n      <div className=\"sidebar-header\">\n        <div className=\"app-icon\">\n          <Link to=\"/\" style={{ textDecoration: 'none', color: 'inherit' }}>\n            <h2>BiblioDesk</h2>\n          </Link>\n        </div>\n      </div>\n      <ul className=\"sidebar-list\">\n        <li className={`sidebar-list-item ${location.pathname === '/' ? 'active' : ''}`}>\n          <Link to=\"/\">\n            <FaHome /> Accueil\n          </Link>\n        </li>\n        <li className={`sidebar-list-item ${location.pathname === '/books' ? 'active' : ''}`}>\n          <Link to=\"/books\">\n            <FaBook /> Livres\n          </Link>\n        </li>\n        <li className={`sidebar-list-item ${location.pathname === '/ebooks' ? 'active' : ''}`}>\n          <Link to=\"/ebooks\">\n            <FaTablet /> E-books\n          </Link>\n        </li>\n        <li className={`sidebar-list-item ${location.pathname === '/statistics' ? 'active' : ''}`}>\n          <Link to=\"/statistics\">\n            <FaChartBar /> Statistiques\n          </Link>\n        </li>\n        <li className={`sidebar-list-item ${location.pathname === '/profile' ? 'active' : ''}`}>\n          <Link to=\"/profile\">\n            <FaUser /> Profil\n          </Link>\n        </li>\n        <li className=\"sidebar-list-item\">\n          <Link to=\"#\" onClick={handleLogout}>\n            <FaSignOutAlt /> Déconnexion\n          </Link>\n        </li>\n      </ul>\n      <div className=\"account-info\">\n        <div className=\"account-info-picture\">\n          {currentUser?.profile?.photo ? (\n            <img src={currentUser.profile.photo} alt=\"Avatar\" />\n          ) : (\n            <img src=\"https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png\" alt=\"Avatar par défaut\" />\n          )}\n        </div>\n        <div className=\"account-info-name\">{currentUser?.username}</div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACpB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,WAAW;IAAEC;EAAO,CAAC,GAAGf,OAAO,CAAC,CAAC;EAEzC,MAAMgB,OAAO,GAAG,CAAAF,WAAW,aAAXA,WAAW,wBAAAH,oBAAA,GAAXG,WAAW,CAAEG,OAAO,cAAAN,oBAAA,uBAApBA,oBAAA,CAAsBO,SAAS,MAAK,OAAO,KAAIJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,YAAY;EAExF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEP,OAAA;IAAKa,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBd,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7Bd,OAAA;QAAKa,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,GAAG;UAACC,KAAK,EAAE;YAAEC,cAAc,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAJ,QAAA,eAC/Dd,OAAA;YAAAc,QAAA,EAAI;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtB,OAAA;MAAIa,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC1Bd,OAAA;QAAIa,SAAS,EAAE,qBAAqBR,QAAQ,CAACkB,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAT,QAAA,eAC9Ed,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,GAAG;UAAAD,QAAA,gBACVd,OAAA,CAACP,MAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLtB,OAAA;QAAIa,SAAS,EAAE,qBAAqBR,QAAQ,CAACkB,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAT,QAAA,eACnFd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,QAAQ;UAAAD,QAAA,gBACfd,OAAA,CAACN,MAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLtB,OAAA;QAAIa,SAAS,EAAE,qBAAqBR,QAAQ,CAACkB,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAT,QAAA,eACpFd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,SAAS;UAAAD,QAAA,gBAChBd,OAAA,CAACL,QAAQ;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLtB,OAAA;QAAIa,SAAS,EAAE,qBAAqBR,QAAQ,CAACkB,QAAQ,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAT,QAAA,eACxFd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,aAAa;UAAAD,QAAA,gBACpBd,OAAA,CAACF,UAAU;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLtB,OAAA;QAAIa,SAAS,EAAE,qBAAqBR,QAAQ,CAACkB,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAT,QAAA,eACrFd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,UAAU;UAAAD,QAAA,gBACjBd,OAAA,CAACJ,MAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLtB,OAAA;QAAIa,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAC/Bd,OAAA,CAACV,IAAI;UAACyB,EAAE,EAAC,GAAG;UAACS,OAAO,EAAEZ,YAAa;UAAAE,QAAA,gBACjCd,OAAA,CAACH,YAAY;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACLtB,OAAA;MAAKa,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3Bd,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClCR,WAAW,aAAXA,WAAW,gBAAAF,qBAAA,GAAXE,WAAW,CAAEG,OAAO,cAAAL,qBAAA,eAApBA,qBAAA,CAAsBqB,KAAK,gBAC1BzB,OAAA;UAAK0B,GAAG,EAAEpB,WAAW,CAACG,OAAO,CAACgB,KAAM;UAACE,GAAG,EAAC;QAAQ;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpDtB,OAAA;UAAK0B,GAAG,EAAC,uGAAuG;UAACC,GAAG,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC3I;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNtB,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAER,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB;MAAQ;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CA/DID,OAAO;EAAA,QACMV,WAAW,EACIC,OAAO;AAAA;AAAAqC,EAAA,GAFnC5B,OAAO;AAiEb,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}