{"ast": null, "code": "// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  // Fonction pour construire une URL d'image complète\n  getImageUrl: imagePath => {\n    if (!imagePath) return null;\n    if (imagePath.startsWith('http')) return imagePath;\n    return `${config.MEDIA_BASE_URL}${imagePath}`;\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_BASE_URL", "MEDIA_BASE_URL", "getImageUrl", "imagePath", "startsWith"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/config.js"], "sourcesContent": ["// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  \n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  \n  // Fonction pour construire une URL d'image complète\n  getImageUrl: (imagePath) => {\n    if (!imagePath) return null;\n    if (imagePath.startsWith('http')) return imagePath;\n    return `${config.MEDIA_BASE_URL}${imagePath}`;\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,YAAY,EAAE,uBAAuB;EAErC;EACAC,cAAc,EAAE,uBAAuB;EAEvC;EACAC,WAAW,EAAGC,SAAS,IAAK;IAC1B,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAC3B,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOD,SAAS;IAClD,OAAO,GAAGJ,MAAM,CAACE,cAAc,GAAGE,SAAS,EAAE;EAC/C;AACF,CAAC;AAED,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}