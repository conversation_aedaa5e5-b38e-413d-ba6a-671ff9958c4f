{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Créer un objet d'erreur plus détaillé\n  const enhancedError = {\n    message: 'Une erreur est survenue',\n    originalError: error,\n    timestamp: new Date().toISOString()\n  };\n\n  // Gérer les erreurs réseau (pas de réponse du serveur)\n  if (!error.response) {\n    enhancedError.type = 'network';\n    enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n    console.error('Erreur réseau:', error);\n  }\n  // Gérer les erreurs avec réponse du serveur\n  else {\n    enhancedError.status = error.response.status;\n    enhancedError.data = error.response.data;\n\n    // Gérer les erreurs d'authentification (401)\n    if (error.response.status === 401) {\n      enhancedError.type = 'auth';\n      enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    // Gérer les erreurs de permission (403)\n    else if (error.response.status === 403) {\n      enhancedError.type = 'permission';\n      enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n      console.error('Erreur de permission:', error.response.data);\n    }\n    // Gérer les erreurs de validation (400)\n    else if (error.response.status === 400) {\n      enhancedError.type = 'validation';\n      enhancedError.message = 'Les données fournies sont invalides.';\n      console.error('Erreur de validation:', error.response.data);\n    }\n    // Gérer les erreurs serveur (500)\n    else if (error.response.status >= 500) {\n      enhancedError.type = 'server';\n      enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n      console.error('Erreur serveur:', error.response.data);\n    }\n  }\n\n  // Enregistrer l'erreur dans la console avec plus de détails\n  console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n  return Promise.reject(enhancedError);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.LIVRES, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.LIVRES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: params => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.EBOOKS, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: id => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: id => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: data => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: id => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "error", "Promise", "reject", "response", "enhancedError", "message", "originalError", "timestamp", "Date", "toISOString", "type", "console", "status", "removeItem", "window", "location", "href", "livresAPI", "getAll", "params", "get", "ENDPOINTS", "LIVRES", "getById", "id", "post", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "EBOOKS", "categoriesAPI", "CATEGORIES", "empruntsAPI", "EMPRUNTS", "retourner", "reservationsAPI", "RESERVATIONS", "annuler", "utilisateursAPI", "getProfile", "UTILISATEURS", "updateProfile", "getNotifications", "markNotificationRead"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Créer un objet d'erreur plus détaillé\n    const enhancedError = {\n      message: 'Une erreur est survenue',\n      originalError: error,\n      timestamp: new Date().toISOString(),\n    };\n\n    // Gérer les erreurs réseau (pas de réponse du serveur)\n    if (!error.response) {\n      enhancedError.type = 'network';\n      enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n      console.error('Erreur réseau:', error);\n    }\n    // Gérer les erreurs avec réponse du serveur\n    else {\n      enhancedError.status = error.response.status;\n      enhancedError.data = error.response.data;\n\n      // Gérer les erreurs d'authentification (401)\n      if (error.response.status === 401) {\n        enhancedError.type = 'auth';\n        enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n        // Supprimer le token et rediriger vers la page de connexion\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n      // Gérer les erreurs de permission (403)\n      else if (error.response.status === 403) {\n        enhancedError.type = 'permission';\n        enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n        console.error('Erreur de permission:', error.response.data);\n      }\n      // Gérer les erreurs de validation (400)\n      else if (error.response.status === 400) {\n        enhancedError.type = 'validation';\n        enhancedError.message = 'Les données fournies sont invalides.';\n        console.error('Erreur de validation:', error.response.data);\n      }\n      // Gérer les erreurs serveur (500)\n      else if (error.response.status >= 500) {\n        enhancedError.type = 'server';\n        enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n        console.error('Erreur serveur:', error.response.data);\n      }\n    }\n\n    // Enregistrer l'erreur dans la console avec plus de détails\n    console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n\n    return Promise.reject(enhancedError);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.LIVRES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: (params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, { params }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.EBOOKS, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: (id) => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: (id) => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: (data) => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;EACvC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,MAAMI,aAAa,GAAG;IACpBC,OAAO,EAAE,yBAAyB;IAClCC,aAAa,EAAEN,KAAK;IACpBO,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,IAAI,CAACT,KAAK,CAACG,QAAQ,EAAE;IACnBC,aAAa,CAACM,IAAI,GAAG,SAAS;IAC9BN,aAAa,CAACC,OAAO,GAAG,oEAAoE;IAC5FM,OAAO,CAACX,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACxC;EACA;EAAA,KACK;IACHI,aAAa,CAACQ,MAAM,GAAGZ,KAAK,CAACG,QAAQ,CAACS,MAAM;IAC5CR,aAAa,CAACN,IAAI,GAAGE,KAAK,CAACG,QAAQ,CAACL,IAAI;;IAExC;IACA,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACjCR,aAAa,CAACM,IAAI,GAAG,MAAM;MAC3BN,aAAa,CAACC,OAAO,GAAG,6CAA6C;MACrE;MACAT,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IACA;IAAA,KACK,IAAIhB,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACtCR,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,iEAAiE;MACzFM,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IAC7D;IACA;IAAA,KACK,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACtCR,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,sCAAsC;MAC9DM,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IAC7D;IACA;IAAA,KACK,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,IAAI,GAAG,EAAE;MACrCR,aAAa,CAACM,IAAI,GAAG,QAAQ;MAC7BN,aAAa,CAACC,OAAO,GAAG,uEAAuE;MAC/FM,OAAO,CAACX,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IACvD;EACF;;EAEA;EACAa,OAAO,CAACX,KAAK,CAAC,eAAeI,aAAa,CAACC,OAAO,EAAE,EAAED,aAAa,CAAC;EAEpE,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;AACtC,CACF,CAAC;;AAED;AACA,OAAO,MAAMa,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACC,MAAM,EAAE;IAAEH;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAChEtC,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACwC,IAAI,CAACzC,UAAU,CAACqC,SAAS,CAACC,MAAM,EAAExB,IAAI,CAAC;EAC7D4B,MAAM,EAAEA,CAACF,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC0C,GAAG,CAAC,GAAG3C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC3E8B,MAAM,EAAGJ,EAAE,IAAKvC,GAAG,CAAC2C,MAAM,CAAC,GAAG5C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAClEK,SAAS,EAAGL,EAAE,IAAKvC,GAAG,CAACwC,IAAI,CAAC,GAAGzC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,aAAa,CAAC;EAC7EM,QAAQ,EAAGN,EAAE,IAAKvC,GAAG,CAACwC,IAAI,CAAC,GAAGzC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,YAAY,CAAC;EAC3EO,kBAAkB,EAAGZ,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,kBAAkB,EAAE;IAAEH;EAAO,CAAC,CAAC;EACrGa,UAAU,EAAEA,CAACR,EAAE,EAAEL,MAAM,KAAKlC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,WAAW,EAAE;IAAEL;EAAO,CAAC;AAChG,CAAC;;AAED;AACA,OAAO,MAAMc,SAAS,GAAG;EACvBf,MAAM,EAAGC,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACa,MAAM,EAAE;IAAEf;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG,CAAC;EAChEtC,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACwC,IAAI,CAACzC,UAAU,CAACqC,SAAS,CAACa,MAAM,EAAEpC,IAAI,CAAC;EAC7D4B,MAAM,EAAEA,CAACF,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC0C,GAAG,CAAC,GAAG3C,UAAU,CAACqC,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC3E8B,MAAM,EAAGJ,EAAE,IAAKvC,GAAG,CAAC2C,MAAM,CAAC,GAAG5C,UAAU,CAACqC,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG;AACnE,CAAC;;AAED;AACA,OAAO,MAAMW,aAAa,GAAG;EAC3BjB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACe,UAAU,CAAC;EACtDb,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG,CAAC;EACpEtC,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACwC,IAAI,CAACzC,UAAU,CAACqC,SAAS,CAACe,UAAU,EAAEtC,IAAI,CAAC;EACjE4B,MAAM,EAAEA,CAACF,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC0C,GAAG,CAAC,GAAG3C,UAAU,CAACqC,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC/E8B,MAAM,EAAGJ,EAAE,IAAKvC,GAAG,CAAC2C,MAAM,CAAC,GAAG5C,UAAU,CAACqC,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG;AACvE,CAAC;;AAED;AACA,OAAO,MAAMa,WAAW,GAAG;EACzBnB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACiB,QAAQ,CAAC;EACpDf,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACiB,QAAQ,GAAGd,EAAE,GAAG,CAAC;EAClEe,SAAS,EAAGf,EAAE,IAAKvC,GAAG,CAACwC,IAAI,CAAC,GAAGzC,UAAU,CAACqC,SAAS,CAACiB,QAAQ,GAAGd,EAAE,aAAa;AAChF,CAAC;;AAED;AACA,OAAO,MAAMgB,eAAe,GAAG;EAC7BtB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACoB,YAAY,CAAC;EACxDlB,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACoB,YAAY,GAAGjB,EAAE,GAAG,CAAC;EACtEkB,OAAO,EAAGlB,EAAE,IAAKvC,GAAG,CAAC2C,MAAM,CAAC,GAAG5C,UAAU,CAACqC,SAAS,CAACoB,YAAY,GAAGjB,EAAE,GAAG;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMmB,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAM3D,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACwB,YAAY,cAAc,CAAC;EAC7EC,aAAa,EAAGhD,IAAI,IAAKb,GAAG,CAAC0C,GAAG,CAAC,GAAG3C,UAAU,CAACqC,SAAS,CAACwB,YAAY,cAAc,EAAE/C,IAAI,CAAC;EAC1FiD,gBAAgB,EAAEA,CAAA,KAAM9D,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACwB,YAAY,gBAAgB,CAAC;EACrFG,oBAAoB,EAAGxB,EAAE,IAAKvC,GAAG,CAACwC,IAAI,CAAC,GAAGzC,UAAU,CAACqC,SAAS,CAACwB,YAAY,iBAAiBrB,EAAE,QAAQ;AACxG,CAAC;AAED,eAAevC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}