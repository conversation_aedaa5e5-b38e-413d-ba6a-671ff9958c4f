{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { empruntsAPI, reservationsAPI, utilisateursAPI } from '../services/api';\nimport config from '../config';\nimport './Profile.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _currentUser$profile, _currentUser$profile2;\n  const {\n    currentUser,\n    updateProfile,\n    getNotifications,\n    markNotificationRead\n  } = useAuth();\n  const [emprunts, setEmprunts] = useState([]);\n  const [reservations, setReservations] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    photo: null\n  });\n  const [photoPreview, setPhotoPreview] = useState(null);\n  const [updateSuccess, setUpdateSuccess] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Vérifier si l'utilisateur est connecté\n        if (!currentUser) {\n          console.log(\"Utilisateur non connecté ou données utilisateur non disponibles\");\n          setLoading(false);\n          return;\n        }\n        console.log(\"Données utilisateur disponibles:\", currentUser);\n        try {\n          // Récupérer les emprunts\n          const empruntsResponse = await empruntsAPI.getAll();\n          console.log('Réponse des emprunts:', empruntsResponse.data);\n\n          // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n          if (empruntsResponse.data && Array.isArray(empruntsResponse.data.results)) {\n            setEmprunts(empruntsResponse.data.results);\n          } else if (empruntsResponse.data && Array.isArray(empruntsResponse.data)) {\n            setEmprunts(empruntsResponse.data);\n          } else {\n            console.error('Format de réponse inattendu pour les emprunts:', empruntsResponse.data);\n            setEmprunts([]);\n          }\n        } catch (empruntsErr) {\n          console.error('Erreur lors de la récupération des emprunts:', empruntsErr);\n          setEmprunts([]);\n        }\n        try {\n          // Récupérer les réservations\n          const reservationsResponse = await reservationsAPI.getAll();\n          console.log('Réponse des réservations:', reservationsResponse.data);\n\n          // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n          if (reservationsResponse.data && Array.isArray(reservationsResponse.data.results)) {\n            setReservations(reservationsResponse.data.results);\n          } else if (reservationsResponse.data && Array.isArray(reservationsResponse.data)) {\n            setReservations(reservationsResponse.data);\n          } else {\n            console.error('Format de réponse inattendu pour les réservations:', reservationsResponse.data);\n            setReservations([]);\n          }\n        } catch (reservationsErr) {\n          console.error('Erreur lors de la récupération des réservations:', reservationsErr);\n          setReservations([]);\n        }\n        try {\n          // Récupérer les notifications\n          const notificationsResponse = await getNotifications();\n          setNotifications(notificationsResponse.data || []);\n        } catch (notificationsErr) {\n          console.error('Erreur lors de la récupération des notifications:', notificationsErr);\n          setNotifications([]);\n        }\n\n        // Initialiser le formulaire avec les données de l'utilisateur\n        setFormData({\n          first_name: currentUser.first_name || '',\n          last_name: currentUser.last_name || '',\n          email: currentUser.email || '',\n          photo: null\n        });\n        if (currentUser.profile && currentUser.profile.photo) {\n          setPhotoPreview(currentUser.profile.photo);\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [currentUser]);\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handlePhotoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        photo: file\n      }));\n\n      // Créer un aperçu de la photo\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    console.log('Soumission du formulaire de profil');\n    try {\n      setLoading(true);\n      console.log('État de chargement activé');\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      console.log('FormData créé');\n\n      // Créer un objet user pour les données utilisateur\n      const userData = {\n        first_name: formData.first_name,\n        last_name: formData.last_name,\n        email: formData.email\n      };\n      console.log('Données utilisateur préparées:', userData);\n\n      // Ajouter l'objet user en tant que JSON\n      formDataObj.append('user', JSON.stringify(userData));\n      console.log('Données utilisateur ajoutées au FormData');\n\n      // Ajouter la photo si elle existe\n      if (formData.photo) {\n        formDataObj.append('photo', formData.photo);\n        console.log('Photo ajoutée au FormData:', formData.photo.name);\n      } else {\n        console.log('Pas de photo à ajouter');\n      }\n\n      // Log pour le débogage\n      console.log('FormData prêt à être envoyé');\n\n      // Vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n      console.log('Envoi de la requête de mise à jour du profil...');\n      const result = await updateProfile(formDataObj);\n      console.log('Résultat de la mise à jour:', result);\n      if (result.success) {\n        console.log('Mise à jour du profil réussie');\n        setUpdateSuccess(true);\n        setTimeout(() => {\n          setUpdateSuccess(false);\n        }, 3000);\n      } else {\n        console.error('Échec de la mise à jour du profil:', result.message);\n        setError(result.message || 'Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      }\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de la mise à jour du profil:', err);\n      setError('Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleRetourLivre = async empruntId => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const empruntsResponse = await empruntsAPI.getAll();\n      setEmprunts(empruntsResponse.data.results || []);\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleAnnulerReservation = async reservationId => {\n    try {\n      setLoading(true);\n      await reservationsAPI.annuler(reservationId);\n\n      // Mettre à jour la liste des réservations\n      const reservationsResponse = await reservationsAPI.getAll();\n      setReservations(reservationsResponse.data.results || []);\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de l\\'annulation de la réservation:', err);\n      setError('Erreur lors de l\\'annulation de la réservation. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  const handleMarkNotificationRead = async notificationId => {\n    try {\n      await markNotificationRead(notificationId);\n\n      // Mettre à jour la liste des notifications\n      const notificationsResponse = await getNotifications();\n      setNotifications(notificationsResponse.data || []);\n    } catch (err) {\n      console.error('Erreur lors du marquage de la notification:', err);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement de votre profil...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Une erreur est survenue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-button\",\n          onClick: () => window.location.reload(),\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this);\n  }\n  if (!currentUser) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Utilisateur non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Impossible de charger les informations de votre profil. Veuillez vous reconnecter.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"login-button\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'profile' ? 'active' : ''}`,\n        onClick: () => handleTabChange('profile'),\n        children: \"Profil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'emprunts' ? 'active' : ''}`,\n        onClick: () => handleTabChange('emprunts'),\n        children: [\"Emprunts \", emprunts.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: emprunts.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 44\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'reservations' ? 'active' : ''}`,\n        onClick: () => handleTabChange('reservations'),\n        children: [\"R\\xE9servations \", reservations.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: reservations.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 52\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'notifications' ? 'active' : ''}`,\n        onClick: () => handleTabChange('notifications'),\n        children: [\"Notifications \", notifications.filter(n => !n.est_lue).length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge\",\n          children: notifications.filter(n => !n.est_lue).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-content\",\n      children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-photo-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-photo\",\n              children: photoPreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: photoPreview.startsWith('http') ? photoPreview : config.getProfileImageUrl(photoPreview),\n                alt: \"Photo de profil\",\n                onError: e => {\n                  e.target.onerror = null;\n                  e.target.src = config.DEFAULT_PROFILE_IMAGE;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                src: config.DEFAULT_PROFILE_IMAGE,\n                alt: \"Photo par d\\xE9faut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"photo-upload\",\n              className: \"change-photo-btn\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-camera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"photo-upload\",\n              accept: \"image/*\",\n              style: {\n                display: 'none'\n              },\n              onChange: handlePhotoChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-name\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: currentUser.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge ${((_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || currentUser.is_superuser ? 'admin' : 'etudiant'}`,\n              children: ((_currentUser$profile2 = currentUser.profile) === null || _currentUser$profile2 === void 0 ? void 0 : _currentUser$profile2.user_type) === 'admin' || currentUser.is_superuser ? 'Administrateur' : 'Étudiant'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), updateSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-message\",\n          children: \"Profil mis \\xE0 jour avec succ\\xE8s !\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"profile-form\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"first_name\",\n                children: \"Pr\\xE9nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"first_name\",\n                name: \"first_name\",\n                value: formData.first_name,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"last_name\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"last_name\",\n                name: \"last_name\",\n                value: formData.last_name,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"update-button\",\n            disabled: loading,\n            children: loading ? 'Mise à jour...' : 'Mettre à jour le profil'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), activeTab === 'emprunts' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"emprunts-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes emprunts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), emprunts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucun emprunt en cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-grid\",\n          children: emprunts.map(emprunt => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: emprunt.livre_titre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${emprunt.est_retourne ? 'returned' : 'active'}`,\n                children: emprunt.est_retourne ? 'Retourné' : 'En cours'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date d'emprunt:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 26\n                }, this), \" \", new Date(emprunt.date_emprunt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de retour pr\\xE9vue:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 26\n                }, this), \" \", new Date(emprunt.date_retour_prevue).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 23\n              }, this), emprunt.date_retour_effective && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de retour effective:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 28\n                }, this), \" \", new Date(emprunt.date_retour_effective).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 21\n            }, this), !emprunt.est_retourne && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button return\",\n              onClick: () => handleRetourLivre(emprunt.id),\n              disabled: loading,\n              children: \"Retourner le livre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 23\n            }, this)]\n          }, emprunt.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this), activeTab === 'reservations' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reservations-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes r\\xE9servations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this), reservations.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucune r\\xE9servation en cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-grid\",\n          children: reservations.map(reservation => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: reservation.livre_titre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${reservation.est_active ? 'active' : 'inactive'}`,\n                children: reservation.est_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-details\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Date de r\\xE9servation:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 26\n                }, this), \" \", new Date(reservation.date_reservation).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 21\n            }, this), reservation.est_active && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button cancel\",\n              onClick: () => handleAnnulerReservation(reservation.id),\n              disabled: loading,\n              children: \"Annuler la r\\xE9servation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 23\n            }, this)]\n          }, reservation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this), activeTab === 'notifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notifications-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Mes notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"no-items\",\n          children: \"Vous n'avez aucune notification.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notifications-container\",\n          children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `notification-item ${notification.est_lue ? 'read' : 'unread'}`,\n            onClick: () => !notification.est_lue && handleMarkNotificationRead(notification.id),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `notification-type ${notification.type}`,\n                children: [notification.type === 'retard' && 'Retard', notification.type === 'rappel' && 'Rappel', notification.type === 'nouveau' && 'Nouveau', notification.type === 'emprunt' && 'Emprunt', notification.type === 'reservation' && 'Réservation', notification.type === 'disponible' && 'Disponible']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"notification-date\",\n                children: new Date(notification.date).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-content\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: notification.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 21\n            }, this), notification.livre_id && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/books/${notification.livre_id}`,\n              className: \"notification-link\",\n              children: \"Voir le livre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 23\n            }, this)]\n          }, notification.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"O7NMHFpm1/SN5+EQE8AinwhrG+s=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useAuth", "empruntsAPI", "reservationsAPI", "utilisateursAPI", "config", "jsxDEV", "_jsxDEV", "Profile", "_s", "_currentUser$profile", "_currentUser$profile2", "currentUser", "updateProfile", "getNotifications", "markNotificationRead", "emprunts", "set<PERSON>mp<PERSON><PERSON>", "reservations", "setReservations", "notifications", "setNotifications", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "formData", "setFormData", "first_name", "last_name", "email", "photo", "photoPreview", "setPhotoPreview", "updateSuccess", "setUpdateSuccess", "fetchData", "console", "log", "empruntsResponse", "getAll", "data", "Array", "isArray", "results", "empruntsErr", "reservationsResponse", "reservationsErr", "notificationsResponse", "notificationsErr", "profile", "err", "handleTabChange", "tab", "handleInputChange", "e", "name", "value", "target", "prev", "handlePhotoChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formDataObj", "FormData", "userData", "append", "JSON", "stringify", "pair", "entries", "success", "setTimeout", "message", "handleRetourLivre", "empruntId", "retourner", "handleAnnulerReservation", "reservationId", "annuler", "handleMarkNotificationRead", "notificationId", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "to", "length", "filter", "n", "est_lue", "src", "startsWith", "getProfileImageUrl", "alt", "onError", "onerror", "DEFAULT_PROFILE_IMAGE", "htmlFor", "type", "id", "accept", "style", "display", "onChange", "username", "user_type", "is_superuser", "onSubmit", "disabled", "map", "emp<PERSON><PERSON>", "livre_titre", "est_retourne", "Date", "date_emprunt", "toLocaleDateString", "date_retour_prevue", "date_retour_effective", "reservation", "est_active", "date_reservation", "notification", "date", "livre_id", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { empruntsAPI, reservationsAPI, utilisateursAPI } from '../services/api';\nimport config from '../config';\nimport './Profile.css';\n\nconst Profile = () => {\n  const { currentUser, updateProfile, getNotifications, markNotificationRead } = useAuth();\n  const [emprunts, setEmprunts] = useState([]);\n  const [reservations, setReservations] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [formData, setFormData] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    photo: null\n  });\n  const [photoPreview, setPhotoPreview] = useState(null);\n  const [updateSuccess, setUpdateSuccess] = useState(false);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Vérifier si l'utilisateur est connecté\n        if (!currentUser) {\n          console.log(\"Utilisateur non connecté ou données utilisateur non disponibles\");\n          setLoading(false);\n          return;\n        }\n\n        console.log(\"Données utilisateur disponibles:\", currentUser);\n\n        try {\n          // Récupérer les emprunts\n          const empruntsResponse = await empruntsAPI.getAll();\n          console.log('Réponse des emprunts:', empruntsResponse.data);\n\n          // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n          if (empruntsResponse.data && Array.isArray(empruntsResponse.data.results)) {\n            setEmprunts(empruntsResponse.data.results);\n          } else if (empruntsResponse.data && Array.isArray(empruntsResponse.data)) {\n            setEmprunts(empruntsResponse.data);\n          } else {\n            console.error('Format de réponse inattendu pour les emprunts:', empruntsResponse.data);\n            setEmprunts([]);\n          }\n        } catch (empruntsErr) {\n          console.error('Erreur lors de la récupération des emprunts:', empruntsErr);\n          setEmprunts([]);\n        }\n\n        try {\n          // Récupérer les réservations\n          const reservationsResponse = await reservationsAPI.getAll();\n          console.log('Réponse des réservations:', reservationsResponse.data);\n\n          // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n          if (reservationsResponse.data && Array.isArray(reservationsResponse.data.results)) {\n            setReservations(reservationsResponse.data.results);\n          } else if (reservationsResponse.data && Array.isArray(reservationsResponse.data)) {\n            setReservations(reservationsResponse.data);\n          } else {\n            console.error('Format de réponse inattendu pour les réservations:', reservationsResponse.data);\n            setReservations([]);\n          }\n        } catch (reservationsErr) {\n          console.error('Erreur lors de la récupération des réservations:', reservationsErr);\n          setReservations([]);\n        }\n\n        try {\n          // Récupérer les notifications\n          const notificationsResponse = await getNotifications();\n          setNotifications(notificationsResponse.data || []);\n        } catch (notificationsErr) {\n          console.error('Erreur lors de la récupération des notifications:', notificationsErr);\n          setNotifications([]);\n        }\n\n        // Initialiser le formulaire avec les données de l'utilisateur\n        setFormData({\n          first_name: currentUser.first_name || '',\n          last_name: currentUser.last_name || '',\n          email: currentUser.email || '',\n          photo: null\n        });\n\n        if (currentUser.profile && currentUser.profile.photo) {\n          setPhotoPreview(currentUser.profile.photo);\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [currentUser]);\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handlePhotoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prev => ({\n        ...prev,\n        photo: file\n      }));\n\n      // Créer un aperçu de la photo\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setPhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    console.log('Soumission du formulaire de profil');\n\n    try {\n      setLoading(true);\n      console.log('État de chargement activé');\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      console.log('FormData créé');\n\n      // Créer un objet user pour les données utilisateur\n      const userData = {\n        first_name: formData.first_name,\n        last_name: formData.last_name,\n        email: formData.email\n      };\n      console.log('Données utilisateur préparées:', userData);\n\n      // Ajouter l'objet user en tant que JSON\n      formDataObj.append('user', JSON.stringify(userData));\n      console.log('Données utilisateur ajoutées au FormData');\n\n      // Ajouter la photo si elle existe\n      if (formData.photo) {\n        formDataObj.append('photo', formData.photo);\n        console.log('Photo ajoutée au FormData:', formData.photo.name);\n      } else {\n        console.log('Pas de photo à ajouter');\n      }\n\n      // Log pour le débogage\n      console.log('FormData prêt à être envoyé');\n\n      // Vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n\n      console.log('Envoi de la requête de mise à jour du profil...');\n      const result = await updateProfile(formDataObj);\n      console.log('Résultat de la mise à jour:', result);\n\n      if (result.success) {\n        console.log('Mise à jour du profil réussie');\n        setUpdateSuccess(true);\n        setTimeout(() => {\n          setUpdateSuccess(false);\n        }, 3000);\n      } else {\n        console.error('Échec de la mise à jour du profil:', result.message);\n        setError(result.message || 'Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      }\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de la mise à jour du profil:', err);\n      setError('Erreur lors de la mise à jour du profil. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleRetourLivre = async (empruntId) => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const empruntsResponse = await empruntsAPI.getAll();\n      setEmprunts(empruntsResponse.data.results || []);\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleAnnulerReservation = async (reservationId) => {\n    try {\n      setLoading(true);\n      await reservationsAPI.annuler(reservationId);\n\n      // Mettre à jour la liste des réservations\n      const reservationsResponse = await reservationsAPI.getAll();\n      setReservations(reservationsResponse.data.results || []);\n\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors de l\\'annulation de la réservation:', err);\n      setError('Erreur lors de l\\'annulation de la réservation. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  const handleMarkNotificationRead = async (notificationId) => {\n    try {\n      await markNotificationRead(notificationId);\n\n      // Mettre à jour la liste des notifications\n      const notificationsResponse = await getNotifications();\n      setNotifications(notificationsResponse.data || []);\n    } catch (err) {\n      console.error('Erreur lors du marquage de la notification:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Chargement de votre profil...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"error-message\">\n          <h2>Une erreur est survenue</h2>\n          <p>{error}</p>\n          <button\n            className=\"retry-button\"\n            onClick={() => window.location.reload()}\n          >\n            Réessayer\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  if (!currentUser) {\n    return (\n      <div className=\"profile-container\">\n        <div className=\"error-message\">\n          <h2>Utilisateur non trouvé</h2>\n          <p>Impossible de charger les informations de votre profil. Veuillez vous reconnecter.</p>\n          <Link to=\"/login\" className=\"login-button\">Se connecter</Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"profile-container\">\n      <div className=\"profile-tabs\">\n        <button\n          className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}\n          onClick={() => handleTabChange('profile')}\n        >\n          Profil\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'emprunts' ? 'active' : ''}`}\n          onClick={() => handleTabChange('emprunts')}\n        >\n          Emprunts {emprunts.length > 0 && <span className=\"badge\">{emprunts.length}</span>}\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'reservations' ? 'active' : ''}`}\n          onClick={() => handleTabChange('reservations')}\n        >\n          Réservations {reservations.length > 0 && <span className=\"badge\">{reservations.length}</span>}\n        </button>\n        <button\n          className={`tab-button ${activeTab === 'notifications' ? 'active' : ''}`}\n          onClick={() => handleTabChange('notifications')}\n        >\n          Notifications {notifications.filter(n => !n.est_lue).length > 0 &&\n            <span className=\"badge\">{notifications.filter(n => !n.est_lue).length}</span>}\n        </button>\n      </div>\n\n      <div className=\"profile-content\">\n        {activeTab === 'profile' && (\n          <div className=\"profile-info\">\n            <div className=\"profile-header\">\n              <div className=\"profile-photo-container\">\n                <div className=\"profile-photo\">\n                  {photoPreview ? (\n                    <img\n                      src={photoPreview.startsWith('http') ? photoPreview : config.getProfileImageUrl(photoPreview)}\n                      alt=\"Photo de profil\"\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = config.DEFAULT_PROFILE_IMAGE;\n                      }}\n                    />\n                  ) : (\n                    <img src={config.DEFAULT_PROFILE_IMAGE} alt=\"Photo par défaut\" />\n                  )}\n                </div>\n                <label htmlFor=\"photo-upload\" className=\"change-photo-btn\">\n                  <i className=\"fas fa-camera\"></i>\n                </label>\n                <input\n                  type=\"file\"\n                  id=\"photo-upload\"\n                  accept=\"image/*\"\n                  style={{ display: 'none' }}\n                  onChange={handlePhotoChange}\n                />\n              </div>\n\n              <div className=\"profile-name\">\n                <h2>{currentUser.username}</h2>\n                <span className={`badge ${currentUser.profile?.user_type === 'admin' || currentUser.is_superuser ? 'admin' : 'etudiant'}`}>\n                  {currentUser.profile?.user_type === 'admin' || currentUser.is_superuser ? 'Administrateur' : 'Étudiant'}\n                </span>\n              </div>\n            </div>\n\n            {updateSuccess && (\n              <div className=\"success-message\">\n                Profil mis à jour avec succès !\n              </div>\n            )}\n\n            <form className=\"profile-form\" onSubmit={handleSubmit}>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"first_name\">Prénom</label>\n                  <input\n                    type=\"text\"\n                    id=\"first_name\"\n                    name=\"first_name\"\n                    value={formData.first_name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"last_name\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"last_name\"\n                    name=\"last_name\"\n                    value={formData.last_name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"email\">Email</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              <button type=\"submit\" className=\"update-button\" disabled={loading}>\n                {loading ? 'Mise à jour...' : 'Mettre à jour le profil'}\n              </button>\n            </form>\n          </div>\n        )}\n\n        {activeTab === 'emprunts' && (\n          <div className=\"emprunts-list\">\n            <h2>Mes emprunts</h2>\n\n            {emprunts.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucun emprunt en cours.</p>\n            ) : (\n              <div className=\"items-grid\">\n                {emprunts.map(emprunt => (\n                  <div key={emprunt.id} className=\"item-card\">\n                    <div className=\"item-header\">\n                      <h3>{emprunt.livre_titre}</h3>\n                      <span className={`status ${emprunt.est_retourne ? 'returned' : 'active'}`}>\n                        {emprunt.est_retourne ? 'Retourné' : 'En cours'}\n                      </span>\n                    </div>\n\n                    <div className=\"item-details\">\n                      <p><strong>Date d'emprunt:</strong> {new Date(emprunt.date_emprunt).toLocaleDateString()}</p>\n                      <p><strong>Date de retour prévue:</strong> {new Date(emprunt.date_retour_prevue).toLocaleDateString()}</p>\n                      {emprunt.date_retour_effective && (\n                        <p><strong>Date de retour effective:</strong> {new Date(emprunt.date_retour_effective).toLocaleDateString()}</p>\n                      )}\n                    </div>\n\n                    {!emprunt.est_retourne && (\n                      <button\n                        className=\"action-button return\"\n                        onClick={() => handleRetourLivre(emprunt.id)}\n                        disabled={loading}\n                      >\n                        Retourner le livre\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'reservations' && (\n          <div className=\"reservations-list\">\n            <h2>Mes réservations</h2>\n\n            {reservations.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucune réservation en cours.</p>\n            ) : (\n              <div className=\"items-grid\">\n                {reservations.map(reservation => (\n                  <div key={reservation.id} className=\"item-card\">\n                    <div className=\"item-header\">\n                      <h3>{reservation.livre_titre}</h3>\n                      <span className={`status ${reservation.est_active ? 'active' : 'inactive'}`}>\n                        {reservation.est_active ? 'Active' : 'Inactive'}\n                      </span>\n                    </div>\n\n                    <div className=\"item-details\">\n                      <p><strong>Date de réservation:</strong> {new Date(reservation.date_reservation).toLocaleDateString()}</p>\n                    </div>\n\n                    {reservation.est_active && (\n                      <button\n                        className=\"action-button cancel\"\n                        onClick={() => handleAnnulerReservation(reservation.id)}\n                        disabled={loading}\n                      >\n                        Annuler la réservation\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'notifications' && (\n          <div className=\"notifications-list\">\n            <h2>Mes notifications</h2>\n\n            {notifications.length === 0 ? (\n              <p className=\"no-items\">Vous n'avez aucune notification.</p>\n            ) : (\n              <div className=\"notifications-container\">\n                {notifications.map(notification => (\n                  <div\n                    key={notification.id}\n                    className={`notification-item ${notification.est_lue ? 'read' : 'unread'}`}\n                    onClick={() => !notification.est_lue && handleMarkNotificationRead(notification.id)}\n                  >\n                    <div className=\"notification-header\">\n                      <span className={`notification-type ${notification.type}`}>\n                        {notification.type === 'retard' && 'Retard'}\n                        {notification.type === 'rappel' && 'Rappel'}\n                        {notification.type === 'nouveau' && 'Nouveau'}\n                        {notification.type === 'emprunt' && 'Emprunt'}\n                        {notification.type === 'reservation' && 'Réservation'}\n                        {notification.type === 'disponible' && 'Disponible'}\n                      </span>\n                      <span className=\"notification-date\">\n                        {new Date(notification.date).toLocaleDateString()}\n                      </span>\n                    </div>\n\n                    <div className=\"notification-content\">\n                      <p>{notification.message}</p>\n                    </div>\n\n                    {notification.livre_id && (\n                      <a\n                        href={`/books/${notification.livre_id}`}\n                        className=\"notification-link\"\n                      >\n                        Voir le livre\n                      </a>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;AAC/E,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACpB,MAAM;IAAEC,WAAW;IAAEC,aAAa;IAAEC,gBAAgB;IAAEC;EAAqB,CAAC,GAAGd,OAAO,CAAC,CAAC;EACxF,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IACvCgC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAMuC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAI,CAACb,WAAW,EAAE;UAChB2B,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;UAC9Ef,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAc,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE5B,WAAW,CAAC;QAE5D,IAAI;UACF;UACA,MAAM6B,gBAAgB,GAAG,MAAMvC,WAAW,CAACwC,MAAM,CAAC,CAAC;UACnDH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,gBAAgB,CAACE,IAAI,CAAC;;UAE3D;UACA,IAAIF,gBAAgB,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,gBAAgB,CAACE,IAAI,CAACG,OAAO,CAAC,EAAE;YACzE7B,WAAW,CAACwB,gBAAgB,CAACE,IAAI,CAACG,OAAO,CAAC;UAC5C,CAAC,MAAM,IAAIL,gBAAgB,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,gBAAgB,CAACE,IAAI,CAAC,EAAE;YACxE1B,WAAW,CAACwB,gBAAgB,CAACE,IAAI,CAAC;UACpC,CAAC,MAAM;YACLJ,OAAO,CAACb,KAAK,CAAC,gDAAgD,EAAEe,gBAAgB,CAACE,IAAI,CAAC;YACtF1B,WAAW,CAAC,EAAE,CAAC;UACjB;QACF,CAAC,CAAC,OAAO8B,WAAW,EAAE;UACpBR,OAAO,CAACb,KAAK,CAAC,8CAA8C,EAAEqB,WAAW,CAAC;UAC1E9B,WAAW,CAAC,EAAE,CAAC;QACjB;QAEA,IAAI;UACF;UACA,MAAM+B,oBAAoB,GAAG,MAAM7C,eAAe,CAACuC,MAAM,CAAC,CAAC;UAC3DH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,oBAAoB,CAACL,IAAI,CAAC;;UAEnE;UACA,IAAIK,oBAAoB,CAACL,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACL,IAAI,CAACG,OAAO,CAAC,EAAE;YACjF3B,eAAe,CAAC6B,oBAAoB,CAACL,IAAI,CAACG,OAAO,CAAC;UACpD,CAAC,MAAM,IAAIE,oBAAoB,CAACL,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACL,IAAI,CAAC,EAAE;YAChFxB,eAAe,CAAC6B,oBAAoB,CAACL,IAAI,CAAC;UAC5C,CAAC,MAAM;YACLJ,OAAO,CAACb,KAAK,CAAC,oDAAoD,EAAEsB,oBAAoB,CAACL,IAAI,CAAC;YAC9FxB,eAAe,CAAC,EAAE,CAAC;UACrB;QACF,CAAC,CAAC,OAAO8B,eAAe,EAAE;UACxBV,OAAO,CAACb,KAAK,CAAC,kDAAkD,EAAEuB,eAAe,CAAC;UAClF9B,eAAe,CAAC,EAAE,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAM+B,qBAAqB,GAAG,MAAMpC,gBAAgB,CAAC,CAAC;UACtDO,gBAAgB,CAAC6B,qBAAqB,CAACP,IAAI,IAAI,EAAE,CAAC;QACpD,CAAC,CAAC,OAAOQ,gBAAgB,EAAE;UACzBZ,OAAO,CAACb,KAAK,CAAC,mDAAmD,EAAEyB,gBAAgB,CAAC;UACpF9B,gBAAgB,CAAC,EAAE,CAAC;QACtB;;QAEA;QACAQ,WAAW,CAAC;UACVC,UAAU,EAAElB,WAAW,CAACkB,UAAU,IAAI,EAAE;UACxCC,SAAS,EAAEnB,WAAW,CAACmB,SAAS,IAAI,EAAE;UACtCC,KAAK,EAAEpB,WAAW,CAACoB,KAAK,IAAI,EAAE;UAC9BC,KAAK,EAAE;QACT,CAAC,CAAC;QAEF,IAAIrB,WAAW,CAACwC,OAAO,IAAIxC,WAAW,CAACwC,OAAO,CAACnB,KAAK,EAAE;UACpDE,eAAe,CAACvB,WAAW,CAACwC,OAAO,CAACnB,KAAK,CAAC;QAC5C;QAEAR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;QACZd,OAAO,CAACb,KAAK,CAAC,wCAAwC,EAAE2B,GAAG,CAAC;QAC5D1B,QAAQ,CAAC,sEAAsE,CAAC;QAChFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDa,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC1B,WAAW,CAAC,CAAC;EAEjB,MAAM0C,eAAe,GAAIC,GAAG,IAAK;IAC/BhC,YAAY,CAACgC,GAAG,CAAC;EACnB,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,iBAAiB,GAAIL,CAAC,IAAK;IAC/B,MAAMM,IAAI,GAAGN,CAAC,CAACG,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRlC,WAAW,CAACgC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP5B,KAAK,EAAE8B;MACT,CAAC,CAAC,CAAC;;MAEH;MACA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBhC,eAAe,CAAC8B,MAAM,CAACG,MAAM,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClBhC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBc,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,MAAMgC,WAAW,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAClClC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;MAE5B;MACA,MAAMkC,QAAQ,GAAG;QACf5C,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;QAC7BC,KAAK,EAAEJ,QAAQ,CAACI;MAClB,CAAC;MACDO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,QAAQ,CAAC;;MAEvD;MACAF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;MACpDnC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAIZ,QAAQ,CAACK,KAAK,EAAE;QAClBuC,WAAW,CAACG,MAAM,CAAC,OAAO,EAAE/C,QAAQ,CAACK,KAAK,CAAC;QAC3CM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEZ,QAAQ,CAACK,KAAK,CAACyB,IAAI,CAAC;MAChE,CAAC,MAAM;QACLnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACvC;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,KAAK,IAAIsC,IAAI,IAAIN,WAAW,CAACO,OAAO,CAAC,CAAC,EAAE;QACtCxC,OAAO,CAACC,GAAG,CAAC,sBAAsBsC,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;MAEAvC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,MAAM4B,MAAM,GAAG,MAAMvD,aAAa,CAAC2D,WAAW,CAAC;MAC/CjC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4B,MAAM,CAAC;MAElD,IAAIA,MAAM,CAACY,OAAO,EAAE;QAClBzC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5CH,gBAAgB,CAAC,IAAI,CAAC;QACtB4C,UAAU,CAAC,MAAM;UACf5C,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLE,OAAO,CAACb,KAAK,CAAC,oCAAoC,EAAE0C,MAAM,CAACc,OAAO,CAAC;QACnEvD,QAAQ,CAACyC,MAAM,CAACc,OAAO,IAAI,wEAAwE,CAAC;MACtG;MAEAzD,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,0CAA0C,EAAE2B,GAAG,CAAC;MAC9D1B,QAAQ,CAAC,wEAAwE,CAAC;MAClFF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0D,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACF3D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMvB,WAAW,CAACmF,SAAS,CAACD,SAAS,CAAC;;MAEtC;MACA,MAAM3C,gBAAgB,GAAG,MAAMvC,WAAW,CAACwC,MAAM,CAAC,CAAC;MACnDzB,WAAW,CAACwB,gBAAgB,CAACE,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;MAEhDrB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAE2B,GAAG,CAAC;MACrD1B,QAAQ,CAAC,+DAA+D,CAAC;MACzEF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6D,wBAAwB,GAAG,MAAOC,aAAa,IAAK;IACxD,IAAI;MACF9D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMtB,eAAe,CAACqF,OAAO,CAACD,aAAa,CAAC;;MAE5C;MACA,MAAMvC,oBAAoB,GAAG,MAAM7C,eAAe,CAACuC,MAAM,CAAC,CAAC;MAC3DvB,eAAe,CAAC6B,oBAAoB,CAACL,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;MAExDrB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,iDAAiD,EAAE2B,GAAG,CAAC;MACrE1B,QAAQ,CAAC,+EAA+E,CAAC;MACzFF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,0BAA0B,GAAG,MAAOC,cAAc,IAAK;IAC3D,IAAI;MACF,MAAM3E,oBAAoB,CAAC2E,cAAc,CAAC;;MAE1C;MACA,MAAMxC,qBAAqB,GAAG,MAAMpC,gBAAgB,CAAC,CAAC;MACtDO,gBAAgB,CAAC6B,qBAAqB,CAACP,IAAI,IAAI,EAAE,CAAC;IACpD,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZd,OAAO,CAACb,KAAK,CAAC,6CAA6C,EAAE2B,GAAG,CAAC;IACnE;EACF,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAKoF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrF,OAAA;QAAKoF,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBrF,OAAA;UAAKoF,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCzF,OAAA;UAAAqF,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItE,KAAK,EAAE;IACT,oBACEnB,OAAA;MAAKoF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrF,OAAA;QAAKoF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrF,OAAA;UAAAqF,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCzF,OAAA;UAAAqF,QAAA,EAAIlE;QAAK;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdzF,OAAA;UACEoF,SAAS,EAAC,cAAc;UACxBM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACpF,WAAW,EAAE;IAChB,oBACEL,OAAA;MAAKoF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrF,OAAA;QAAKoF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrF,OAAA;UAAAqF,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BzF,OAAA;UAAAqF,QAAA,EAAG;QAAkF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzFzF,OAAA,CAACP,IAAI;UAACqG,EAAE,EAAC,QAAQ;UAACV,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzF,OAAA;IAAKoF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCrF,OAAA;MAAKoF,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrF,OAAA;QACEoF,SAAS,EAAE,cAAcrE,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;QACnE2E,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,SAAS,CAAE;QAAAsC,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzF,OAAA;QACEoF,SAAS,EAAE,cAAcrE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACpE2E,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,UAAU,CAAE;QAAAsC,QAAA,GAC5C,WACU,EAAC5E,QAAQ,CAACsF,MAAM,GAAG,CAAC,iBAAI/F,OAAA;UAAMoF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE5E,QAAQ,CAACsF;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACTzF,OAAA;QACEoF,SAAS,EAAE,cAAcrE,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxE2E,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,cAAc,CAAE;QAAAsC,QAAA,GAChD,kBACc,EAAC1E,YAAY,CAACoF,MAAM,GAAG,CAAC,iBAAI/F,OAAA;UAAMoF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAE1E,YAAY,CAACoF;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACTzF,OAAA;QACEoF,SAAS,EAAE,cAAcrE,SAAS,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;QACzE2E,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,eAAe,CAAE;QAAAsC,QAAA,GACjD,gBACe,EAACxE,aAAa,CAACmF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,OAAO,CAAC,CAACH,MAAM,GAAG,CAAC,iBAC7D/F,OAAA;UAAMoF,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAExE,aAAa,CAACmF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,OAAO,CAAC,CAACH;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzF,OAAA;MAAKoF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7BtE,SAAS,KAAK,SAAS,iBACtBf,OAAA;QAAKoF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrF,OAAA;UAAKoF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrF,OAAA;YAAKoF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCrF,OAAA;cAAKoF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B1D,YAAY,gBACX3B,OAAA;gBACEmG,GAAG,EAAExE,YAAY,CAACyE,UAAU,CAAC,MAAM,CAAC,GAAGzE,YAAY,GAAG7B,MAAM,CAACuG,kBAAkB,CAAC1E,YAAY,CAAE;gBAC9F2E,GAAG,EAAC,iBAAiB;gBACrBC,OAAO,EAAGrD,CAAC,IAAK;kBACdA,CAAC,CAACG,MAAM,CAACmD,OAAO,GAAG,IAAI;kBACvBtD,CAAC,CAACG,MAAM,CAAC8C,GAAG,GAAGrG,MAAM,CAAC2G,qBAAqB;gBAC7C;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEFzF,OAAA;gBAAKmG,GAAG,EAAErG,MAAM,CAAC2G,qBAAsB;gBAACH,GAAG,EAAC;cAAkB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzF,OAAA;cAAO0G,OAAO,EAAC,cAAc;cAACtB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eACxDrF,OAAA;gBAAGoF,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACRzF,OAAA;cACE2G,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,cAAc;cACjBC,MAAM,EAAC,SAAS;cAChBC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAC3BC,QAAQ,EAAEzD;YAAkB;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzF,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrF,OAAA;cAAAqF,QAAA,EAAKhF,WAAW,CAAC4G;YAAQ;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/BzF,OAAA;cAAMoF,SAAS,EAAE,SAAS,EAAAjF,oBAAA,GAAAE,WAAW,CAACwC,OAAO,cAAA1C,oBAAA,uBAAnBA,oBAAA,CAAqB+G,SAAS,MAAK,OAAO,IAAI7G,WAAW,CAAC8G,YAAY,GAAG,OAAO,GAAG,UAAU,EAAG;cAAA9B,QAAA,EACvH,EAAAjF,qBAAA,GAAAC,WAAW,CAACwC,OAAO,cAAAzC,qBAAA,uBAAnBA,qBAAA,CAAqB8G,SAAS,MAAK,OAAO,IAAI7G,WAAW,CAAC8G,YAAY,GAAG,gBAAgB,GAAG;YAAU;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL5D,aAAa,iBACZ7B,OAAA;UAAKoF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEjC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAEDzF,OAAA;UAAMoF,SAAS,EAAC,cAAc;UAACgC,QAAQ,EAAErD,YAAa;UAAAsB,QAAA,gBACpDrF,OAAA;YAAKoF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBrF,OAAA;cAAKoF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrF,OAAA;gBAAO0G,OAAO,EAAC,YAAY;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CzF,OAAA;gBACE2G,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,YAAY;gBACfzD,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE/B,QAAQ,CAACE,UAAW;gBAC3ByF,QAAQ,EAAE/D;cAAkB;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzF,OAAA;cAAKoF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrF,OAAA;gBAAO0G,OAAO,EAAC,WAAW;gBAAArB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCzF,OAAA;gBACE2G,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,WAAW;gBACdzD,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE/B,QAAQ,CAACG,SAAU;gBAC1BwF,QAAQ,EAAE/D;cAAkB;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrF,OAAA;cAAO0G,OAAO,EAAC,OAAO;cAAArB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCzF,OAAA;cACE2G,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVzD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE/B,QAAQ,CAACI,KAAM;cACtBuF,QAAQ,EAAE/D;YAAkB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzF,OAAA;YAAQ2G,IAAI,EAAC,QAAQ;YAACvB,SAAS,EAAC,eAAe;YAACiC,QAAQ,EAAEpG,OAAQ;YAAAoE,QAAA,EAC/DpE,OAAO,GAAG,gBAAgB,GAAG;UAAyB;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA1E,SAAS,KAAK,UAAU,iBACvBf,OAAA;QAAKoF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrF,OAAA;UAAAqF,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEpBhF,QAAQ,CAACsF,MAAM,KAAK,CAAC,gBACpB/F,OAAA;UAAGoF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE/DzF,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB5E,QAAQ,CAAC6G,GAAG,CAACC,OAAO,iBACnBvH,OAAA;YAAsBoF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzCrF,OAAA;cAAKoF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrF,OAAA;gBAAAqF,QAAA,EAAKkC,OAAO,CAACC;cAAW;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BzF,OAAA;gBAAMoF,SAAS,EAAE,UAAUmC,OAAO,CAACE,YAAY,GAAG,UAAU,GAAG,QAAQ,EAAG;gBAAApC,QAAA,EACvEkC,OAAO,CAACE,YAAY,GAAG,UAAU,GAAG;cAAU;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzF,OAAA;cAAKoF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FzF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiC,IAAI,CAACH,OAAO,CAACM,kBAAkB,CAAC,CAACD,kBAAkB,CAAC,CAAC;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACzG8B,OAAO,CAACO,qBAAqB,iBAC5B9H,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiC,IAAI,CAACH,OAAO,CAACO,qBAAqB,CAAC,CAACF,kBAAkB,CAAC,CAAC;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAChH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL,CAAC8B,OAAO,CAACE,YAAY,iBACpBzH,OAAA;cACEoF,SAAS,EAAC,sBAAsB;cAChCM,OAAO,EAAEA,CAAA,KAAMd,iBAAiB,CAAC2C,OAAO,CAACX,EAAE,CAAE;cAC7CS,QAAQ,EAAEpG,OAAQ;cAAAoE,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,GAxBO8B,OAAO,CAACX,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEA1E,SAAS,KAAK,cAAc,iBAC3Bf,OAAA;QAAKoF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrF,OAAA;UAAAqF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAExB9E,YAAY,CAACoF,MAAM,KAAK,CAAC,gBACxB/F,OAAA;UAAGoF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpEzF,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB1E,YAAY,CAAC2G,GAAG,CAACS,WAAW,iBAC3B/H,OAAA;YAA0BoF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC7CrF,OAAA;cAAKoF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrF,OAAA;gBAAAqF,QAAA,EAAK0C,WAAW,CAACP;cAAW;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCzF,OAAA;gBAAMoF,SAAS,EAAE,UAAU2C,WAAW,CAACC,UAAU,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA3C,QAAA,EACzE0C,WAAW,CAACC,UAAU,GAAG,QAAQ,GAAG;cAAU;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzF,OAAA;cAAKoF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrF,OAAA;gBAAAqF,QAAA,gBAAGrF,OAAA;kBAAAqF,QAAA,EAAQ;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiC,IAAI,CAACK,WAAW,CAACE,gBAAgB,CAAC,CAACL,kBAAkB,CAAC,CAAC;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,EAELsC,WAAW,CAACC,UAAU,iBACrBhI,OAAA;cACEoF,SAAS,EAAC,sBAAsB;cAChCM,OAAO,EAAEA,CAAA,KAAMX,wBAAwB,CAACgD,WAAW,CAACnB,EAAE,CAAE;cACxDS,QAAQ,EAAEpG,OAAQ;cAAAoE,QAAA,EACnB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,GApBOsC,WAAW,CAACnB,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBnB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEA1E,SAAS,KAAK,eAAe,iBAC5Bf,OAAA;QAAKoF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCrF,OAAA;UAAAqF,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEzB5E,aAAa,CAACkF,MAAM,KAAK,CAAC,gBACzB/F,OAAA;UAAGoF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE5DzF,OAAA;UAAKoF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrCxE,aAAa,CAACyG,GAAG,CAACY,YAAY,iBAC7BlI,OAAA;YAEEoF,SAAS,EAAE,qBAAqB8C,YAAY,CAAChC,OAAO,GAAG,MAAM,GAAG,QAAQ,EAAG;YAC3ER,OAAO,EAAEA,CAAA,KAAM,CAACwC,YAAY,CAAChC,OAAO,IAAIhB,0BAA0B,CAACgD,YAAY,CAACtB,EAAE,CAAE;YAAAvB,QAAA,gBAEpFrF,OAAA;cAAKoF,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCrF,OAAA;gBAAMoF,SAAS,EAAE,qBAAqB8C,YAAY,CAACvB,IAAI,EAAG;gBAAAtB,QAAA,GACvD6C,YAAY,CAACvB,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAC1CuB,YAAY,CAACvB,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAC1CuB,YAAY,CAACvB,IAAI,KAAK,SAAS,IAAI,SAAS,EAC5CuB,YAAY,CAACvB,IAAI,KAAK,SAAS,IAAI,SAAS,EAC5CuB,YAAY,CAACvB,IAAI,KAAK,aAAa,IAAI,aAAa,EACpDuB,YAAY,CAACvB,IAAI,KAAK,YAAY,IAAI,YAAY;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACPzF,OAAA;gBAAMoF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAChC,IAAIqC,IAAI,CAACQ,YAAY,CAACC,IAAI,CAAC,CAACP,kBAAkB,CAAC;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzF,OAAA;cAAKoF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnCrF,OAAA;gBAAAqF,QAAA,EAAI6C,YAAY,CAACvD;cAAO;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,EAELyC,YAAY,CAACE,QAAQ,iBACpBpI,OAAA;cACEqI,IAAI,EAAE,UAAUH,YAAY,CAACE,QAAQ,EAAG;cACxChD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA,GA7BIyC,YAAY,CAACtB,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8BjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvF,EAAA,CA5gBID,OAAO;EAAA,QACoEP,OAAO;AAAA;AAAA4I,EAAA,GADlFrI,OAAO;AA8gBb,eAAeA,OAAO;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}