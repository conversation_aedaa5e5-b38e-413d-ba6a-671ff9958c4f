{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\AllEmprunts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { empruntsAPI } from '../services/api';\nimport empruntsAdminService from '../services/empruntsAdminService';\nimport { useAuth } from '../context/AuthContext';\nimport './AllEmprunts.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllEmprunts = () => {\n  _s();\n  var _currentUser$profile;\n  const [emprunts, setEmprunts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [filter, setFilter] = useState('all'); // 'all', 'active', 'returned'\n  const [searchTerm, setSearchTerm] = useState('');\n  const {\n    currentUser\n  } = useAuth();\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    const fetchEmprunts = async () => {\n      try {\n        setLoading(true);\n        console.log('Tentative de récupération des emprunts admin avec le nouveau service...');\n        try {\n          // Utiliser le nouveau service dédié\n          const data = await empruntsAdminService.getAllEmprunts();\n          console.log('Données reçues du service dédié:', data);\n\n          // Vérifier si les données sont un tableau ou un objet avec une propriété results\n          if (Array.isArray(data)) {\n            console.log('Format de réponse: tableau direct');\n            setEmprunts(data);\n          } else if (data && Array.isArray(data.results)) {\n            console.log('Format de réponse: objet avec propriété results');\n            setEmprunts(data.results);\n          } else {\n            console.error('Format de réponse inattendu pour les emprunts:', data);\n            setEmprunts([]);\n          }\n        } catch (apiErr) {\n          console.error('Erreur spécifique au service dédié:', apiErr);\n          console.error('Message d\\'erreur:', apiErr.message);\n          if (apiErr.response) {\n            console.error('Détails de la réponse d\\'erreur:', apiErr.response.data);\n            console.error('Statut HTTP:', apiErr.response.status);\n          }\n          throw apiErr; // Relancer l'erreur pour être capturée par le bloc catch externe\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors de la récupération des emprunts:', err);\n        setError('Erreur lors de la récupération des emprunts. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    if (isAdmin) {\n      fetchEmprunts();\n    } else {\n      setError('Vous n\\'avez pas les permissions nécessaires pour accéder à cette page.');\n      setLoading(false);\n    }\n  }, [isAdmin]);\n\n  // Filtrer les emprunts en fonction du filtre et du terme de recherche\n  const filteredEmprunts = emprunts.filter(emprunt => {\n    // Appliquer le filtre\n    if (filter === 'active' && emprunt.est_retourne) return false;\n    if (filter === 'returned' && !emprunt.est_retourne) return false;\n\n    // Appliquer la recherche\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      return emprunt.livre_titre.toLowerCase().includes(searchLower) || emprunt.utilisateur_nom.toLowerCase().includes(searchLower);\n    }\n    return true;\n  });\n  const handleRetourLivre = async empruntId => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const response = await empruntsAPI.getAllAdmin();\n      console.log('Réponse des emprunts admin après retour:', response.data);\n\n      // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n      if (response.data && Array.isArray(response.data.results)) {\n        setEmprunts(response.data.results);\n      } else if (response.data && Array.isArray(response.data)) {\n        setEmprunts(response.data);\n      } else {\n        console.error('Format de réponse inattendu pour les emprunts:', response.data);\n        setEmprunts([]);\n      }\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emprunts-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement des emprunts...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emprunts-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Une erreur est survenue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-button\",\n          onClick: () => window.location.reload(),\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAdmin) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emprunts-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Acc\\xE8s refus\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Vous n'avez pas les permissions n\\xE9cessaires pour acc\\xE9der \\xE0 cette page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"back-link\",\n          children: \"Retour \\xE0 l'accueil\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"emprunts-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Gestion des emprunts\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emprunts-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Rechercher par titre ou utilisateur...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `filter-button ${filter === 'all' ? 'active' : ''}`,\n          onClick: () => setFilter('all'),\n          children: \"Tous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `filter-button ${filter === 'active' ? 'active' : ''}`,\n          onClick: () => setFilter('active'),\n          children: \"En cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `filter-button ${filter === 'returned' ? 'active' : ''}`,\n          onClick: () => setFilter('returned'),\n          children: \"Retourn\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), filteredEmprunts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"no-items\",\n      children: \"Aucun emprunt trouv\\xE9.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emprunts-table\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Livre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Utilisateur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date d'emprunt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date de retour pr\\xE9vue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredEmprunts.map(emprunt => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: emprunt.est_retourne ? 'returned' : emprunt.est_en_retard ? 'late' : '',\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: `/books/${emprunt.livre_id}`,\n                children: emprunt.livre_titre\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: emprunt.utilisateur_nom\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: new Date(emprunt.date_emprunt).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: new Date(emprunt.date_retour_prevue).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${emprunt.est_retourne ? 'returned' : emprunt.est_en_retard ? 'late' : 'active'}`,\n                children: emprunt.est_retourne ? 'Retourné' : emprunt.est_en_retard ? 'En retard' : 'En cours'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [!emprunt.est_retourne && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"action-button return\",\n                onClick: () => handleRetourLivre(emprunt.id),\n                children: \"Marquer comme retourn\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 23\n              }, this), emprunt.est_retourne && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"returned-date\",\n                children: [\"Retourn\\xE9 le \", new Date(emprunt.date_retour_effective).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this)]\n          }, emprunt.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(AllEmprunts, \"AOh6d1ahq1A+Ytf44eafdhX+gUw=\", false, function () {\n  return [useAuth];\n});\n_c = AllEmprunts;\nexport default AllEmprunts;\nvar _c;\n$RefreshReg$(_c, \"AllEmprunts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "empruntsAPI", "empruntsAdminService", "useAuth", "jsxDEV", "_jsxDEV", "AllEmprunts", "_s", "_currentUser$profile", "emprunts", "set<PERSON>mp<PERSON><PERSON>", "loading", "setLoading", "error", "setError", "filter", "setFilter", "searchTerm", "setSearchTerm", "currentUser", "isAdmin", "profile", "user_type", "is_superuser", "fetchEmprunts", "console", "log", "data", "getAllEmprunts", "Array", "isArray", "results", "apiErr", "message", "response", "status", "err", "filteredEmprunts", "emp<PERSON><PERSON>", "est_retourne", "searchLower", "toLowerCase", "livre_titre", "includes", "utilisateur_nom", "handleRetourLivre", "empruntId", "retourner", "getAllAdmin", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "to", "type", "placeholder", "value", "onChange", "e", "target", "length", "map", "est_en_retard", "livre_id", "Date", "date_emprunt", "toLocaleDateString", "date_retour_prevue", "id", "date_retour_effective", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/AllEmprunts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { empruntsAPI } from '../services/api';\nimport empruntsAdminService from '../services/empruntsAdminService';\nimport { useAuth } from '../context/AuthContext';\nimport './AllEmprunts.css';\n\nconst AllEmprunts = () => {\n  const [emprunts, setEmprunts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [filter, setFilter] = useState('all'); // 'all', 'active', 'returned'\n  const [searchTerm, setSearchTerm] = useState('');\n  const { currentUser } = useAuth();\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    const fetchEmprunts = async () => {\n      try {\n        setLoading(true);\n        console.log('Tentative de récupération des emprunts admin avec le nouveau service...');\n\n        try {\n          // Utiliser le nouveau service dédié\n          const data = await empruntsAdminService.getAllEmprunts();\n          console.log('Données reçues du service dédié:', data);\n\n          // Vérifier si les données sont un tableau ou un objet avec une propriété results\n          if (Array.isArray(data)) {\n            console.log('Format de réponse: tableau direct');\n            setEmprunts(data);\n          } else if (data && Array.isArray(data.results)) {\n            console.log('Format de réponse: objet avec propriété results');\n            setEmprunts(data.results);\n          } else {\n            console.error('Format de réponse inattendu pour les emprunts:', data);\n            setEmprunts([]);\n          }\n        } catch (apiErr) {\n          console.error('Erreur spécifique au service dédié:', apiErr);\n          console.error('Message d\\'erreur:', apiErr.message);\n          if (apiErr.response) {\n            console.error('Détails de la réponse d\\'erreur:', apiErr.response.data);\n            console.error('Statut HTTP:', apiErr.response.status);\n          }\n          throw apiErr; // Relancer l'erreur pour être capturée par le bloc catch externe\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors de la récupération des emprunts:', err);\n        setError('Erreur lors de la récupération des emprunts. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    if (isAdmin) {\n      fetchEmprunts();\n    } else {\n      setError('Vous n\\'avez pas les permissions nécessaires pour accéder à cette page.');\n      setLoading(false);\n    }\n  }, [isAdmin]);\n\n  // Filtrer les emprunts en fonction du filtre et du terme de recherche\n  const filteredEmprunts = emprunts.filter(emprunt => {\n    // Appliquer le filtre\n    if (filter === 'active' && emprunt.est_retourne) return false;\n    if (filter === 'returned' && !emprunt.est_retourne) return false;\n\n    // Appliquer la recherche\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      return (\n        emprunt.livre_titre.toLowerCase().includes(searchLower) ||\n        emprunt.utilisateur_nom.toLowerCase().includes(searchLower)\n      );\n    }\n\n    return true;\n  });\n\n  const handleRetourLivre = async (empruntId) => {\n    try {\n      setLoading(true);\n      await empruntsAPI.retourner(empruntId);\n\n      // Mettre à jour la liste des emprunts\n      const response = await empruntsAPI.getAllAdmin();\n      console.log('Réponse des emprunts admin après retour:', response.data);\n\n      // Vérifier si la réponse contient un tableau results ou si c'est directement un tableau\n      if (response.data && Array.isArray(response.data.results)) {\n        setEmprunts(response.data.results);\n      } else if (response.data && Array.isArray(response.data)) {\n        setEmprunts(response.data);\n      } else {\n        console.error('Format de réponse inattendu pour les emprunts:', response.data);\n        setEmprunts([]);\n      }\n      setLoading(false);\n    } catch (err) {\n      console.error('Erreur lors du retour du livre:', err);\n      setError('Erreur lors du retour du livre. Veuillez réessayer plus tard.');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"emprunts-container\">\n        <div className=\"loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>Chargement des emprunts...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"emprunts-container\">\n        <div className=\"error-message\">\n          <h2>Une erreur est survenue</h2>\n          <p>{error}</p>\n          <button\n            className=\"retry-button\"\n            onClick={() => window.location.reload()}\n          >\n            Réessayer\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAdmin) {\n    return (\n      <div className=\"emprunts-container\">\n        <div className=\"error-message\">\n          <h2>Accès refusé</h2>\n          <p>Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>\n          <Link to=\"/\" className=\"back-link\">\n            Retour à l'accueil\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"emprunts-container\">\n      <h1>Gestion des emprunts</h1>\n\n      <div className=\"emprunts-controls\">\n        <div className=\"search-bar\">\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher par titre ou utilisateur...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </div>\n\n        <div className=\"filter-buttons\">\n          <button\n            className={`filter-button ${filter === 'all' ? 'active' : ''}`}\n            onClick={() => setFilter('all')}\n          >\n            Tous\n          </button>\n          <button\n            className={`filter-button ${filter === 'active' ? 'active' : ''}`}\n            onClick={() => setFilter('active')}\n          >\n            En cours\n          </button>\n          <button\n            className={`filter-button ${filter === 'returned' ? 'active' : ''}`}\n            onClick={() => setFilter('returned')}\n          >\n            Retournés\n          </button>\n        </div>\n      </div>\n\n      {filteredEmprunts.length === 0 ? (\n        <p className=\"no-items\">Aucun emprunt trouvé.</p>\n      ) : (\n        <div className=\"emprunts-table\">\n          <table>\n            <thead>\n              <tr>\n                <th>Livre</th>\n                <th>Utilisateur</th>\n                <th>Date d'emprunt</th>\n                <th>Date de retour prévue</th>\n                <th>Statut</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredEmprunts.map(emprunt => (\n                <tr key={emprunt.id} className={emprunt.est_retourne ? 'returned' : emprunt.est_en_retard ? 'late' : ''}>\n                  <td>\n                    <Link to={`/books/${emprunt.livre_id}`}>\n                      {emprunt.livre_titre}\n                    </Link>\n                  </td>\n                  <td>{emprunt.utilisateur_nom}</td>\n                  <td>{new Date(emprunt.date_emprunt).toLocaleDateString()}</td>\n                  <td>{new Date(emprunt.date_retour_prevue).toLocaleDateString()}</td>\n                  <td>\n                    <span className={`status ${emprunt.est_retourne ? 'returned' : emprunt.est_en_retard ? 'late' : 'active'}`}>\n                      {emprunt.est_retourne\n                        ? 'Retourné'\n                        : emprunt.est_en_retard\n                          ? 'En retard'\n                          : 'En cours'}\n                    </span>\n                  </td>\n                  <td>\n                    {!emprunt.est_retourne && (\n                      <button\n                        className=\"action-button return\"\n                        onClick={() => handleRetourLivre(emprunt.id)}\n                      >\n                        Marquer comme retourné\n                      </button>\n                    )}\n                    {emprunt.est_retourne && (\n                      <span className=\"returned-date\">\n                        Retourné le {new Date(emprunt.date_retour_effective).toLocaleDateString()}\n                      </span>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AllEmprunts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM;IAAEqB;EAAY,CAAC,GAAGhB,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAMiB,OAAO,GAAG,CAAAD,WAAW,aAAXA,WAAW,wBAAAX,oBAAA,GAAXW,WAAW,CAAEE,OAAO,cAAAb,oBAAA,uBAApBA,oBAAA,CAAsBc,SAAS,MAAK,OAAO,KAAIH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,YAAY;EAExFxB,SAAS,CAAC,MAAM;IACd,MAAMyB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFZ,UAAU,CAAC,IAAI,CAAC;QAChBa,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;QAEtF,IAAI;UACF;UACA,MAAMC,IAAI,GAAG,MAAMzB,oBAAoB,CAAC0B,cAAc,CAAC,CAAC;UACxDH,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,IAAI,CAAC;;UAErD;UACA,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;YACvBF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDhB,WAAW,CAACiB,IAAI,CAAC;UACnB,CAAC,MAAM,IAAIA,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC,EAAE;YAC9CN,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9DhB,WAAW,CAACiB,IAAI,CAACI,OAAO,CAAC;UAC3B,CAAC,MAAM;YACLN,OAAO,CAACZ,KAAK,CAAC,gDAAgD,EAAEc,IAAI,CAAC;YACrEjB,WAAW,CAAC,EAAE,CAAC;UACjB;QACF,CAAC,CAAC,OAAOsB,MAAM,EAAE;UACfP,OAAO,CAACZ,KAAK,CAAC,qCAAqC,EAAEmB,MAAM,CAAC;UAC5DP,OAAO,CAACZ,KAAK,CAAC,oBAAoB,EAAEmB,MAAM,CAACC,OAAO,CAAC;UACnD,IAAID,MAAM,CAACE,QAAQ,EAAE;YACnBT,OAAO,CAACZ,KAAK,CAAC,kCAAkC,EAAEmB,MAAM,CAACE,QAAQ,CAACP,IAAI,CAAC;YACvEF,OAAO,CAACZ,KAAK,CAAC,cAAc,EAAEmB,MAAM,CAACE,QAAQ,CAACC,MAAM,CAAC;UACvD;UACA,MAAMH,MAAM,CAAC,CAAC;QAChB;QAEApB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOwB,GAAG,EAAE;QACZX,OAAO,CAACZ,KAAK,CAAC,8CAA8C,EAAEuB,GAAG,CAAC;QAClEtB,QAAQ,CAAC,4EAA4E,CAAC;QACtFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIQ,OAAO,EAAE;MACXI,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLV,QAAQ,CAAC,yEAAyE,CAAC;MACnFF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACQ,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMiB,gBAAgB,GAAG5B,QAAQ,CAACM,MAAM,CAACuB,OAAO,IAAI;IAClD;IACA,IAAIvB,MAAM,KAAK,QAAQ,IAAIuB,OAAO,CAACC,YAAY,EAAE,OAAO,KAAK;IAC7D,IAAIxB,MAAM,KAAK,UAAU,IAAI,CAACuB,OAAO,CAACC,YAAY,EAAE,OAAO,KAAK;;IAEhE;IACA,IAAItB,UAAU,EAAE;MACd,MAAMuB,WAAW,GAAGvB,UAAU,CAACwB,WAAW,CAAC,CAAC;MAC5C,OACEH,OAAO,CAACI,WAAW,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,IACvDF,OAAO,CAACM,eAAe,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC;IAE/D;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,MAAMK,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMX,WAAW,CAAC8C,SAAS,CAACD,SAAS,CAAC;;MAEtC;MACA,MAAMZ,QAAQ,GAAG,MAAMjC,WAAW,CAAC+C,WAAW,CAAC,CAAC;MAChDvB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEQ,QAAQ,CAACP,IAAI,CAAC;;MAEtE;MACA,IAAIO,QAAQ,CAACP,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACI,QAAQ,CAACP,IAAI,CAACI,OAAO,CAAC,EAAE;QACzDrB,WAAW,CAACwB,QAAQ,CAACP,IAAI,CAACI,OAAO,CAAC;MACpC,CAAC,MAAM,IAAIG,QAAQ,CAACP,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACI,QAAQ,CAACP,IAAI,CAAC,EAAE;QACxDjB,WAAW,CAACwB,QAAQ,CAACP,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLF,OAAO,CAACZ,KAAK,CAAC,gDAAgD,EAAEqB,QAAQ,CAACP,IAAI,CAAC;QAC9EjB,WAAW,CAAC,EAAE,CAAC;MACjB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZX,OAAO,CAACZ,KAAK,CAAC,iCAAiC,EAAEuB,GAAG,CAAC;MACrDtB,QAAQ,CAAC,+DAA+D,CAAC;MACzEF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK4C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjC7C,OAAA;QAAK4C,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtB7C,OAAA;UAAK4C,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjD,OAAA;UAAA6C,QAAA,EAAG;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIzC,KAAK,EAAE;IACT,oBACER,OAAA;MAAK4C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjC7C,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7C,OAAA;UAAA6C,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCjD,OAAA;UAAA6C,QAAA,EAAIrC;QAAK;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdjD,OAAA;UACE4C,SAAS,EAAC,cAAc;UACxBM,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAR,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAClC,OAAO,EAAE;IACZ,oBACEf,OAAA;MAAK4C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjC7C,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7C,OAAA;UAAA6C,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBjD,OAAA;UAAA6C,QAAA,EAAG;QAAsE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7EjD,OAAA,CAACL,IAAI;UAAC2D,EAAE,EAAC,GAAG;UAACV,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjD,OAAA;IAAK4C,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC7C,OAAA;MAAA6C,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7BjD,OAAA;MAAK4C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC7C,OAAA;QAAK4C,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB7C,OAAA;UACEuD,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,wCAAwC;UACpDC,KAAK,EAAE7C,UAAW;UAClB8C,QAAQ,EAAGC,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7C,OAAA;UACE4C,SAAS,EAAE,iBAAiBlC,MAAM,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC/DwC,OAAO,EAAEA,CAAA,KAAMvC,SAAS,CAAC,KAAK,CAAE;UAAAkC,QAAA,EACjC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjD,OAAA;UACE4C,SAAS,EAAE,iBAAiBlC,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAClEwC,OAAO,EAAEA,CAAA,KAAMvC,SAAS,CAAC,QAAQ,CAAE;UAAAkC,QAAA,EACpC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjD,OAAA;UACE4C,SAAS,EAAE,iBAAiBlC,MAAM,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACpEwC,OAAO,EAAEA,CAAA,KAAMvC,SAAS,CAAC,UAAU,CAAE;UAAAkC,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELjB,gBAAgB,CAAC6B,MAAM,KAAK,CAAC,gBAC5B7D,OAAA;MAAG4C,SAAS,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAEjDjD,OAAA;MAAK4C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B7C,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAA6C,QAAA,eACE7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdjD,OAAA;cAAA6C,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBjD,OAAA;cAAA6C,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBjD,OAAA;cAAA6C,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BjD,OAAA;cAAA6C,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfjD,OAAA;cAAA6C,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRjD,OAAA;UAAA6C,QAAA,EACGb,gBAAgB,CAAC8B,GAAG,CAAC7B,OAAO,iBAC3BjC,OAAA;YAAqB4C,SAAS,EAAEX,OAAO,CAACC,YAAY,GAAG,UAAU,GAAGD,OAAO,CAAC8B,aAAa,GAAG,MAAM,GAAG,EAAG;YAAAlB,QAAA,gBACtG7C,OAAA;cAAA6C,QAAA,eACE7C,OAAA,CAACL,IAAI;gBAAC2D,EAAE,EAAE,UAAUrB,OAAO,CAAC+B,QAAQ,EAAG;gBAAAnB,QAAA,EACpCZ,OAAO,CAACI;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLjD,OAAA;cAAA6C,QAAA,EAAKZ,OAAO,CAACM;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClCjD,OAAA;cAAA6C,QAAA,EAAK,IAAIoB,IAAI,CAAChC,OAAO,CAACiC,YAAY,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DjD,OAAA;cAAA6C,QAAA,EAAK,IAAIoB,IAAI,CAAChC,OAAO,CAACmC,kBAAkB,CAAC,CAACD,kBAAkB,CAAC;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpEjD,OAAA;cAAA6C,QAAA,eACE7C,OAAA;gBAAM4C,SAAS,EAAE,UAAUX,OAAO,CAACC,YAAY,GAAG,UAAU,GAAGD,OAAO,CAAC8B,aAAa,GAAG,MAAM,GAAG,QAAQ,EAAG;gBAAAlB,QAAA,EACxGZ,OAAO,CAACC,YAAY,GACjB,UAAU,GACVD,OAAO,CAAC8B,aAAa,GACnB,WAAW,GACX;cAAU;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLjD,OAAA;cAAA6C,QAAA,GACG,CAACZ,OAAO,CAACC,YAAY,iBACpBlC,OAAA;gBACE4C,SAAS,EAAC,sBAAsB;gBAChCM,OAAO,EAAEA,CAAA,KAAMV,iBAAiB,CAACP,OAAO,CAACoC,EAAE,CAAE;gBAAAxB,QAAA,EAC9C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,EACAhB,OAAO,CAACC,YAAY,iBACnBlC,OAAA;gBAAM4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,iBAClB,EAAC,IAAIoB,IAAI,CAAChC,OAAO,CAACqC,qBAAqB,CAAC,CAACH,kBAAkB,CAAC,CAAC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAhCEhB,OAAO,CAACoC,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCf,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA/OID,WAAW;EAAA,QAMSH,OAAO;AAAA;AAAAyE,EAAA,GAN3BtE,WAAW;AAiPjB,eAAeA,WAAW;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}