{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\Statistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { statisticsAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport Loading from '../components/Loading';\nimport './Statistics.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Statistics = () => {\n  _s();\n  const {\n    showError\n  } = useAlert();\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        console.log('Tentative de récupération des statistiques...');\n        const response = await statisticsAPI.getAll();\n        console.log('Réponse des statistiques reçue:', response);\n        if (response && response.data) {\n          console.log('Données des statistiques:', response.data);\n          setStatistics(response.data);\n        } else {\n          console.error('Réponse vide ou mal formatée');\n          showError('Les données statistiques sont vides ou mal formatées.');\n          // Définir des statistiques par défaut pour éviter les erreurs\n          setStatistics({\n            kpi: {\n              total_livres: 0,\n              valeur_totale: 0,\n              total_reservations: 0,\n              livres_retournes: 0,\n              livres_non_retournes: 0\n            },\n            top_livres: {\n              labels: [],\n              values: []\n            },\n            top_etudiants: {\n              labels: [],\n              retournes: [],\n              non_retournes: []\n            },\n            emprunts_par_jour: {\n              dates: [],\n              counts: []\n            },\n            stats_utilisateurs: []\n          });\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des statistiques:', error);\n        if (error.response) {\n          console.error('Détails de l\\'erreur:', {\n            status: error.response.status,\n            data: error.response.data,\n            headers: error.response.headers\n          });\n        }\n        showError('Impossible de charger les statistiques. Veuillez réessayer plus tard.');\n\n        // Définir des statistiques par défaut pour éviter les erreurs\n        setStatistics({\n          kpi: {\n            total_livres: 0,\n            valeur_totale: 0,\n            total_reservations: 0,\n            livres_retournes: 0,\n            livres_non_retournes: 0\n          },\n          top_livres: {\n            labels: [],\n            values: []\n          },\n          top_etudiants: {\n            labels: [],\n            retournes: [],\n            non_retournes: []\n          },\n          emprunts_par_jour: {\n            dates: [],\n            counts: []\n          },\n          stats_utilisateurs: []\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchStatistics();\n  }, [showError]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      fullScreen: true,\n      message: \"Chargement des statistiques...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 12\n    }, this);\n  }\n  if (!statistics) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"statistics-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Erreur de chargement\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Impossible de charger les statistiques. Veuillez r\\xE9essayer plus tard.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Vérification de la structure des données et préparation pour l'affichage\n  // Utilisation de valeurs par défaut si les données sont manquantes ou mal formatées\n  const topBooks = statistics.top_livres && Array.isArray(statistics.top_livres.labels) && Array.isArray(statistics.top_livres.values) && statistics.top_livres.labels.length > 0 ? statistics.top_livres.labels.map((label, index) => ({\n    titre: label || 'Titre inconnu',\n    count: statistics.top_livres.values[index] || 0\n  })) : [];\n  const topStudents = statistics.top_etudiants && Array.isArray(statistics.top_etudiants.labels) && Array.isArray(statistics.top_etudiants.retournes) && Array.isArray(statistics.top_etudiants.non_retournes) && statistics.top_etudiants.labels.length > 0 ? statistics.top_etudiants.labels.map((label, index) => ({\n    username: label || 'Utilisateur inconnu',\n    retournes: statistics.top_etudiants.retournes[index] || 0,\n    non_retournes: statistics.top_etudiants.non_retournes[index] || 0,\n    total: (statistics.top_etudiants.retournes[index] || 0) + (statistics.top_etudiants.non_retournes[index] || 0)\n  })) : [];\n  const loansPerDay = statistics.emprunts_par_jour && Array.isArray(statistics.emprunts_par_jour.dates) && Array.isArray(statistics.emprunts_par_jour.counts) && statistics.emprunts_par_jour.dates.length > 0 ? statistics.emprunts_par_jour.dates.map((date, index) => ({\n    date: date || 'Date inconnue',\n    count: statistics.emprunts_par_jour.counts[index] || 0\n  })) : [];\n\n  // Valeurs par défaut pour les KPI\n  const kpi = statistics.kpi || {\n    total_livres: 0,\n    valeur_totale: 0,\n    total_reservations: 0,\n    livres_retournes: 0,\n    livres_non_retournes: 0\n  };\n\n  // Valeurs par défaut pour les statistiques utilisateurs\n  const statsUtilisateurs = Array.isArray(statistics.stats_utilisateurs) ? statistics.stats_utilisateurs : [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"statistics-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"statistics-title\",\n      children: \"Statistiques de la Biblioth\\xE8que\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kpi-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-book\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total des Livres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: kpi.total_livres\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-money-bill-wave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Valeur Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: [typeof kpi.valeur_totale === 'number' ? kpi.valeur_totale.toFixed(2) : '0.00', \" MAD\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-calendar-check\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"R\\xE9servations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: kpi.total_reservations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exchange-alt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kpi-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Emprunts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-value\",\n            children: kpi.livres_retournes + kpi.livres_non_retournes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"kpi-subtext\",\n            children: [kpi.livres_retournes, \" retourn\\xE9s, \", kpi.livres_non_retournes, \" en cours\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Top 10 des Livres les Plus Emprunt\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"statistics-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Titre du livre\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Nombre d'emprunts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: topBooks.map((book, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: book.titre\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress-bar\",\n                      style: {\n                        width: `${book.count / Math.max(...topBooks.map(b => b.count)) * 100}%`,\n                        backgroundColor: '#4CAF50'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: book.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Top 10 des Utilisateurs les Plus Actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"statistics-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Utilisateur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Emprunts retourn\\xE9s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Emprunts en cours\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Total\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: topStudents.map((student, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: student.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: student.retournes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: student.non_retournes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: student.total\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Emprunts par Jour (30 derniers jours)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"statistics-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Nombre d'emprunts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: loansPerDay.map((day, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: day.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress-bar\",\n                      style: {\n                        width: `${day.count / Math.max(...loansPerDay.map(d => d.count || 1)) * 100}%`,\n                        backgroundColor: '#9C27B0'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: day.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Statistiques des Utilisateurs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-responsive\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"statistics-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emprunts totaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emprunts en cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: statsUtilisateurs.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.username || 'Utilisateur inconnu'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.type || 'Type inconnu'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.total_emprunts || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.emprunts_en_cours || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(Statistics, \"CCH/vsbx/uF9PATHBKqBrATFNcI=\", false, function () {\n  return [useAlert];\n});\n_c = Statistics;\nexport default Statistics;\nvar _c;\n$RefreshReg$(_c, \"Statistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "statisticsAPI", "useAlert", "Loading", "jsxDEV", "_jsxDEV", "Statistics", "_s", "showError", "statistics", "setStatistics", "loading", "setLoading", "fetchStatistics", "console", "log", "response", "getAll", "data", "error", "kpi", "total_livres", "valeur_totale", "total_reservations", "livres_retournes", "livres_non_retournes", "top_livres", "labels", "values", "top_etudiants", "retournes", "non_retournes", "emprunts_par_jour", "dates", "counts", "stats_utilisateurs", "status", "headers", "fullScreen", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "topBooks", "Array", "isArray", "length", "map", "label", "index", "titre", "count", "topStudents", "username", "total", "loansPerDay", "date", "statsUtilisateurs", "toFixed", "book", "style", "width", "Math", "max", "b", "backgroundColor", "student", "day", "d", "user", "type", "total_emprunts", "emprunts_en_cours", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/Statistics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { statisticsAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport Loading from '../components/Loading';\nimport './Statistics.css';\n\nconst Statistics = () => {\n  const { showError } = useAlert();\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        console.log('Tentative de récupération des statistiques...');\n\n        const response = await statisticsAPI.getAll();\n        console.log('Réponse des statistiques reçue:', response);\n\n        if (response && response.data) {\n          console.log('Données des statistiques:', response.data);\n          setStatistics(response.data);\n        } else {\n          console.error('Réponse vide ou mal formatée');\n          showError('Les données statistiques sont vides ou mal formatées.');\n          // Définir des statistiques par défaut pour éviter les erreurs\n          setStatistics({\n            kpi: {\n              total_livres: 0,\n              valeur_totale: 0,\n              total_reservations: 0,\n              livres_retournes: 0,\n              livres_non_retournes: 0\n            },\n            top_livres: { labels: [], values: [] },\n            top_etudiants: { labels: [], retournes: [], non_retournes: [] },\n            emprunts_par_jour: { dates: [], counts: [] },\n            stats_utilisateurs: []\n          });\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des statistiques:', error);\n\n        if (error.response) {\n          console.error('Détails de l\\'erreur:', {\n            status: error.response.status,\n            data: error.response.data,\n            headers: error.response.headers\n          });\n        }\n\n        showError('Impossible de charger les statistiques. Veuillez réessayer plus tard.');\n\n        // Définir des statistiques par défaut pour éviter les erreurs\n        setStatistics({\n          kpi: {\n            total_livres: 0,\n            valeur_totale: 0,\n            total_reservations: 0,\n            livres_retournes: 0,\n            livres_non_retournes: 0\n          },\n          top_livres: { labels: [], values: [] },\n          top_etudiants: { labels: [], retournes: [], non_retournes: [] },\n          emprunts_par_jour: { dates: [], counts: [] },\n          stats_utilisateurs: []\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStatistics();\n  }, [showError]);\n\n  if (loading) {\n    return <Loading fullScreen message=\"Chargement des statistiques...\" />;\n  }\n\n  if (!statistics) {\n    return (\n      <div className=\"statistics-error\">\n        <h2>Erreur de chargement</h2>\n        <p>Impossible de charger les statistiques. Veuillez réessayer plus tard.</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  // Vérification de la structure des données et préparation pour l'affichage\n  // Utilisation de valeurs par défaut si les données sont manquantes ou mal formatées\n  const topBooks = (statistics.top_livres &&\n                   Array.isArray(statistics.top_livres.labels) &&\n                   Array.isArray(statistics.top_livres.values) &&\n                   statistics.top_livres.labels.length > 0) ?\n    statistics.top_livres.labels.map((label, index) => ({\n      titre: label || 'Titre inconnu',\n      count: statistics.top_livres.values[index] || 0\n    })) : [];\n\n  const topStudents = (statistics.top_etudiants &&\n                      Array.isArray(statistics.top_etudiants.labels) &&\n                      Array.isArray(statistics.top_etudiants.retournes) &&\n                      Array.isArray(statistics.top_etudiants.non_retournes) &&\n                      statistics.top_etudiants.labels.length > 0) ?\n    statistics.top_etudiants.labels.map((label, index) => ({\n      username: label || 'Utilisateur inconnu',\n      retournes: statistics.top_etudiants.retournes[index] || 0,\n      non_retournes: statistics.top_etudiants.non_retournes[index] || 0,\n      total: (statistics.top_etudiants.retournes[index] || 0) + (statistics.top_etudiants.non_retournes[index] || 0)\n    })) : [];\n\n  const loansPerDay = (statistics.emprunts_par_jour &&\n                      Array.isArray(statistics.emprunts_par_jour.dates) &&\n                      Array.isArray(statistics.emprunts_par_jour.counts) &&\n                      statistics.emprunts_par_jour.dates.length > 0) ?\n    statistics.emprunts_par_jour.dates.map((date, index) => ({\n      date: date || 'Date inconnue',\n      count: statistics.emprunts_par_jour.counts[index] || 0\n    })) : [];\n\n  // Valeurs par défaut pour les KPI\n  const kpi = statistics.kpi || {\n    total_livres: 0,\n    valeur_totale: 0,\n    total_reservations: 0,\n    livres_retournes: 0,\n    livres_non_retournes: 0\n  };\n\n  // Valeurs par défaut pour les statistiques utilisateurs\n  const statsUtilisateurs = Array.isArray(statistics.stats_utilisateurs) ?\n    statistics.stats_utilisateurs : [];\n\n  return (\n    <div className=\"statistics-page\">\n      <h1 className=\"statistics-title\">Statistiques de la Bibliothèque</h1>\n\n      {/* KPI Cards */}\n      <div className=\"kpi-container\">\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-book\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Total des Livres</h3>\n            <p className=\"kpi-value\">{kpi.total_livres}</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Valeur Totale</h3>\n            <p className=\"kpi-value\">{typeof kpi.valeur_totale === 'number' ? kpi.valeur_totale.toFixed(2) : '0.00'} MAD</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-calendar-check\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Réservations</h3>\n            <p className=\"kpi-value\">{kpi.total_reservations}</p>\n          </div>\n        </div>\n\n        <div className=\"kpi-card\">\n          <div className=\"kpi-icon\">\n            <i className=\"fas fa-exchange-alt\"></i>\n          </div>\n          <div className=\"kpi-content\">\n            <h3>Emprunts</h3>\n            <p className=\"kpi-value\">{kpi.livres_retournes + kpi.livres_non_retournes}</p>\n            <p className=\"kpi-subtext\">\n              {kpi.livres_retournes} retournés, {kpi.livres_non_retournes} en cours\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Data Sections */}\n      <div className=\"charts-container\">\n        <div className=\"chart-section\">\n          <h2>Top 10 des Livres les Plus Empruntés</h2>\n          <div className=\"table-responsive\">\n            <table className=\"statistics-table\">\n              <thead>\n                <tr>\n                  <th>Titre du livre</th>\n                  <th>Nombre d'emprunts</th>\n                </tr>\n              </thead>\n              <tbody>\n                {topBooks.map((book, index) => (\n                  <tr key={index}>\n                    <td>{book.titre}</td>\n                    <td>\n                      <div className=\"progress-bar-container\">\n                        <div\n                          className=\"progress-bar\"\n                          style={{\n                            width: `${(book.count / Math.max(...topBooks.map(b => b.count))) * 100}%`,\n                            backgroundColor: '#4CAF50'\n                          }}\n                        ></div>\n                        <span>{book.count}</span>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <div className=\"chart-section\">\n          <h2>Top 10 des Utilisateurs les Plus Actifs</h2>\n          <div className=\"table-responsive\">\n            <table className=\"statistics-table\">\n              <thead>\n                <tr>\n                  <th>Utilisateur</th>\n                  <th>Emprunts retournés</th>\n                  <th>Emprunts en cours</th>\n                  <th>Total</th>\n                </tr>\n              </thead>\n              <tbody>\n                {topStudents.map((student, index) => (\n                  <tr key={index}>\n                    <td>{student.username}</td>\n                    <td>{student.retournes}</td>\n                    <td>{student.non_retournes}</td>\n                    <td>{student.total}</td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <div className=\"chart-section\">\n          <h2>Emprunts par Jour (30 derniers jours)</h2>\n          <div className=\"table-responsive\">\n            <table className=\"statistics-table\">\n              <thead>\n                <tr>\n                  <th>Date</th>\n                  <th>Nombre d'emprunts</th>\n                </tr>\n              </thead>\n              <tbody>\n                {loansPerDay.map((day, index) => (\n                  <tr key={index}>\n                    <td>{day.date}</td>\n                    <td>\n                      <div className=\"progress-bar-container\">\n                        <div\n                          className=\"progress-bar\"\n                          style={{\n                            width: `${(day.count / Math.max(...loansPerDay.map(d => d.count || 1))) * 100}%`,\n                            backgroundColor: '#9C27B0'\n                          }}\n                        ></div>\n                        <span>{day.count}</span>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      {/* User Statistics Table */}\n      <div className=\"table-container\">\n        <h2>Statistiques des Utilisateurs</h2>\n        <div className=\"table-responsive\">\n          <table className=\"statistics-table\">\n            <thead>\n              <tr>\n                <th>Utilisateur</th>\n                <th>Type</th>\n                <th>Emprunts totaux</th>\n                <th>Emprunts en cours</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statsUtilisateurs.map((user, index) => (\n                <tr key={index}>\n                  <td>{user.username || 'Utilisateur inconnu'}</td>\n                  <td>{user.type || 'Type inconnu'}</td>\n                  <td>{user.total_emprunts || 0}</td>\n                  <td>{user.emprunts_en_cours || 0}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Statistics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAU,CAAC,GAAGN,QAAQ,CAAC,CAAC;EAChC,MAAM,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChBE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAE5D,MAAMC,QAAQ,GAAG,MAAMf,aAAa,CAACgB,MAAM,CAAC,CAAC;QAC7CH,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;QAExD,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;UAC7BJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,QAAQ,CAACE,IAAI,CAAC;UACvDR,aAAa,CAACM,QAAQ,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLJ,OAAO,CAACK,KAAK,CAAC,8BAA8B,CAAC;UAC7CX,SAAS,CAAC,uDAAuD,CAAC;UAClE;UACAE,aAAa,CAAC;YACZU,GAAG,EAAE;cACHC,YAAY,EAAE,CAAC;cACfC,aAAa,EAAE,CAAC;cAChBC,kBAAkB,EAAE,CAAC;cACrBC,gBAAgB,EAAE,CAAC;cACnBC,oBAAoB,EAAE;YACxB,CAAC;YACDC,UAAU,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG,CAAC;YACtCC,aAAa,EAAE;cAAEF,MAAM,EAAE,EAAE;cAAEG,SAAS,EAAE,EAAE;cAAEC,aAAa,EAAE;YAAG,CAAC;YAC/DC,iBAAiB,EAAE;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG,CAAC;YAC5CC,kBAAkB,EAAE;UACtB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QAExE,IAAIA,KAAK,CAACH,QAAQ,EAAE;UAClBF,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAE;YACrCiB,MAAM,EAAEjB,KAAK,CAACH,QAAQ,CAACoB,MAAM;YAC7BlB,IAAI,EAAEC,KAAK,CAACH,QAAQ,CAACE,IAAI;YACzBmB,OAAO,EAAElB,KAAK,CAACH,QAAQ,CAACqB;UAC1B,CAAC,CAAC;QACJ;QAEA7B,SAAS,CAAC,uEAAuE,CAAC;;QAElF;QACAE,aAAa,CAAC;UACZU,GAAG,EAAE;YACHC,YAAY,EAAE,CAAC;YACfC,aAAa,EAAE,CAAC;YAChBC,kBAAkB,EAAE,CAAC;YACrBC,gBAAgB,EAAE,CAAC;YACnBC,oBAAoB,EAAE;UACxB,CAAC;UACDC,UAAU,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UACtCC,aAAa,EAAE;YAAEF,MAAM,EAAE,EAAE;YAAEG,SAAS,EAAE,EAAE;YAAEC,aAAa,EAAE;UAAG,CAAC;UAC/DC,iBAAiB,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAC5CC,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRvB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,IAAIG,OAAO,EAAE;IACX,oBAAON,OAAA,CAACF,OAAO;MAACmC,UAAU;MAACC,OAAO,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxE;EAEA,IAAI,CAAClC,UAAU,EAAE;IACf,oBACEJ,OAAA;MAAKuC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxC,OAAA;QAAAwC,QAAA,EAAI;MAAoB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BtC,OAAA;QAAAwC,QAAA,EAAG;MAAqE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5EtC,OAAA;QACEuC,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA;EACA,MAAMO,QAAQ,GAAIzC,UAAU,CAACiB,UAAU,IACtByB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACiB,UAAU,CAACC,MAAM,CAAC,IAC3CwB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACiB,UAAU,CAACE,MAAM,CAAC,IAC3CnB,UAAU,CAACiB,UAAU,CAACC,MAAM,CAAC0B,MAAM,GAAG,CAAC,GACtD5C,UAAU,CAACiB,UAAU,CAACC,MAAM,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IAClDC,KAAK,EAAEF,KAAK,IAAI,eAAe;IAC/BG,KAAK,EAAEjD,UAAU,CAACiB,UAAU,CAACE,MAAM,CAAC4B,KAAK,CAAC,IAAI;EAChD,CAAC,CAAC,CAAC,GAAG,EAAE;EAEV,MAAMG,WAAW,GAAIlD,UAAU,CAACoB,aAAa,IACzBsB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACoB,aAAa,CAACF,MAAM,CAAC,IAC9CwB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACoB,aAAa,CAACC,SAAS,CAAC,IACjDqB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACoB,aAAa,CAACE,aAAa,CAAC,IACrDtB,UAAU,CAACoB,aAAa,CAACF,MAAM,CAAC0B,MAAM,GAAG,CAAC,GAC5D5C,UAAU,CAACoB,aAAa,CAACF,MAAM,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IACrDI,QAAQ,EAAEL,KAAK,IAAI,qBAAqB;IACxCzB,SAAS,EAAErB,UAAU,CAACoB,aAAa,CAACC,SAAS,CAAC0B,KAAK,CAAC,IAAI,CAAC;IACzDzB,aAAa,EAAEtB,UAAU,CAACoB,aAAa,CAACE,aAAa,CAACyB,KAAK,CAAC,IAAI,CAAC;IACjEK,KAAK,EAAE,CAACpD,UAAU,CAACoB,aAAa,CAACC,SAAS,CAAC0B,KAAK,CAAC,IAAI,CAAC,KAAK/C,UAAU,CAACoB,aAAa,CAACE,aAAa,CAACyB,KAAK,CAAC,IAAI,CAAC;EAC/G,CAAC,CAAC,CAAC,GAAG,EAAE;EAEV,MAAMM,WAAW,GAAIrD,UAAU,CAACuB,iBAAiB,IAC7BmB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACuB,iBAAiB,CAACC,KAAK,CAAC,IACjDkB,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAACuB,iBAAiB,CAACE,MAAM,CAAC,IAClDzB,UAAU,CAACuB,iBAAiB,CAACC,KAAK,CAACoB,MAAM,GAAG,CAAC,GAC/D5C,UAAU,CAACuB,iBAAiB,CAACC,KAAK,CAACqB,GAAG,CAAC,CAACS,IAAI,EAAEP,KAAK,MAAM;IACvDO,IAAI,EAAEA,IAAI,IAAI,eAAe;IAC7BL,KAAK,EAAEjD,UAAU,CAACuB,iBAAiB,CAACE,MAAM,CAACsB,KAAK,CAAC,IAAI;EACvD,CAAC,CAAC,CAAC,GAAG,EAAE;;EAEV;EACA,MAAMpC,GAAG,GAAGX,UAAU,CAACW,GAAG,IAAI;IAC5BC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE,CAAC;IACnBC,oBAAoB,EAAE;EACxB,CAAC;;EAED;EACA,MAAMuC,iBAAiB,GAAGb,KAAK,CAACC,OAAO,CAAC3C,UAAU,CAAC0B,kBAAkB,CAAC,GACpE1B,UAAU,CAAC0B,kBAAkB,GAAG,EAAE;EAEpC,oBACE9B,OAAA;IAAKuC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BxC,OAAA;MAAIuC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGrEtC,OAAA;MAAKuC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxC,OAAA;YAAGuC,SAAS,EAAC;UAAa;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNtC,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxC,OAAA;YAAAwC,QAAA,EAAI;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBtC,OAAA;YAAGuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEzB,GAAG,CAACC;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxC,OAAA;YAAGuC,SAAS,EAAC;UAAwB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNtC,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxC,OAAA;YAAAwC,QAAA,EAAI;UAAa;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBtC,OAAA;YAAGuC,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAE,OAAOzB,GAAG,CAACE,aAAa,KAAK,QAAQ,GAAGF,GAAG,CAACE,aAAa,CAAC2C,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,EAAC,MAAI;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxC,OAAA;YAAGuC,SAAS,EAAC;UAAuB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACNtC,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxC,OAAA;YAAAwC,QAAA,EAAI;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBtC,OAAA;YAAGuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEzB,GAAG,CAACG;UAAkB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKuC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBxC,OAAA;UAAKuC,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBxC,OAAA;YAAGuC,SAAS,EAAC;UAAqB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNtC,OAAA;UAAKuC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxC,OAAA;YAAAwC,QAAA,EAAI;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBtC,OAAA;YAAGuC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEzB,GAAG,CAACI,gBAAgB,GAAGJ,GAAG,CAACK;UAAoB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EtC,OAAA;YAAGuC,SAAS,EAAC,aAAa;YAAAC,QAAA,GACvBzB,GAAG,CAACI,gBAAgB,EAAC,iBAAY,EAACJ,GAAG,CAACK,oBAAoB,EAAC,WAC9D;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKuC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxC,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxC,OAAA;UAAAwC,QAAA,EAAI;QAAoC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CtC,OAAA;UAAKuC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BxC,OAAA;YAAOuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjCxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAI;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvBtC,OAAA;kBAAAwC,QAAA,EAAI;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtC,OAAA;cAAAwC,QAAA,EACGK,QAAQ,CAACI,GAAG,CAAC,CAACY,IAAI,EAAEV,KAAK,kBACxBnD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAKqB,IAAI,CAACT;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrBtC,OAAA;kBAAAwC,QAAA,eACExC,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCxC,OAAA;sBACEuC,SAAS,EAAC,cAAc;sBACxBuB,KAAK,EAAE;wBACLC,KAAK,EAAE,GAAIF,IAAI,CAACR,KAAK,GAAGW,IAAI,CAACC,GAAG,CAAC,GAAGpB,QAAQ,CAACI,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACb,KAAK,CAAC,CAAC,GAAI,GAAG,GAAG;wBACzEc,eAAe,EAAE;sBACnB;oBAAE;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACPtC,OAAA;sBAAAwC,QAAA,EAAOqB,IAAI,CAACR;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAbEa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxC,OAAA;UAAAwC,QAAA,EAAI;QAAuC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDtC,OAAA;UAAKuC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BxC,OAAA;YAAOuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjCxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAI;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpBtC,OAAA;kBAAAwC,QAAA,EAAI;gBAAkB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3BtC,OAAA;kBAAAwC,QAAA,EAAI;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BtC,OAAA;kBAAAwC,QAAA,EAAI;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtC,OAAA;cAAAwC,QAAA,EACGc,WAAW,CAACL,GAAG,CAAC,CAACmB,OAAO,EAAEjB,KAAK,kBAC9BnD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAK4B,OAAO,CAACb;gBAAQ;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3BtC,OAAA;kBAAAwC,QAAA,EAAK4B,OAAO,CAAC3C;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BtC,OAAA;kBAAAwC,QAAA,EAAK4B,OAAO,CAAC1C;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChCtC,OAAA;kBAAAwC,QAAA,EAAK4B,OAAO,CAACZ;gBAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAJjBa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxC,OAAA;UAAAwC,QAAA,EAAI;QAAqC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CtC,OAAA;UAAKuC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BxC,OAAA;YAAOuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjCxC,OAAA;cAAAwC,QAAA,eACExC,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAI;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbtC,OAAA;kBAAAwC,QAAA,EAAI;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtC,OAAA;cAAAwC,QAAA,EACGiB,WAAW,CAACR,GAAG,CAAC,CAACoB,GAAG,EAAElB,KAAK,kBAC1BnD,OAAA;gBAAAwC,QAAA,gBACExC,OAAA;kBAAAwC,QAAA,EAAK6B,GAAG,CAACX;gBAAI;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBtC,OAAA;kBAAAwC,QAAA,eACExC,OAAA;oBAAKuC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCxC,OAAA;sBACEuC,SAAS,EAAC,cAAc;sBACxBuB,KAAK,EAAE;wBACLC,KAAK,EAAE,GAAIM,GAAG,CAAChB,KAAK,GAAGW,IAAI,CAACC,GAAG,CAAC,GAAGR,WAAW,CAACR,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACjB,KAAK,IAAI,CAAC,CAAC,CAAC,GAAI,GAAG,GAAG;wBAChFc,eAAe,EAAE;sBACnB;oBAAE;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACPtC,OAAA;sBAAAwC,QAAA,EAAO6B,GAAG,CAAChB;oBAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAbEa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKuC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxC,OAAA;QAAAwC,QAAA,EAAI;MAA6B;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCtC,OAAA;QAAKuC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BxC,OAAA;UAAOuC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACjCxC,OAAA;YAAAwC,QAAA,eACExC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAAwC,QAAA,EAAI;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBtC,OAAA;gBAAAwC,QAAA,EAAI;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbtC,OAAA;gBAAAwC,QAAA,EAAI;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBtC,OAAA;gBAAAwC,QAAA,EAAI;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRtC,OAAA;YAAAwC,QAAA,EACGmB,iBAAiB,CAACV,GAAG,CAAC,CAACsB,IAAI,EAAEpB,KAAK,kBACjCnD,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAAwC,QAAA,EAAK+B,IAAI,CAAChB,QAAQ,IAAI;cAAqB;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjDtC,OAAA;gBAAAwC,QAAA,EAAK+B,IAAI,CAACC,IAAI,IAAI;cAAc;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCtC,OAAA;gBAAAwC,QAAA,EAAK+B,IAAI,CAACE,cAAc,IAAI;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCtC,OAAA;gBAAAwC,QAAA,EAAK+B,IAAI,CAACG,iBAAiB,IAAI;cAAC;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAJ/Ba,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CAnTID,UAAU;EAAA,QACQJ,QAAQ;AAAA;AAAA8E,EAAA,GAD1B1E,UAAU;AAqThB,eAAeA,UAAU;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}