export = CommentRemover;
/** @param {import('../index.js').Options} options */
declare function CommentRemover(options: import('../index.js').Options): void;
declare class CommentRemover {
    /** @param {import('../index.js').Options} options */
    constructor(options: import('../index.js').Options);
    options: import("../index.js").Options;
    /**
     * @param {string} comment
     * @return {boolean | undefined}
     */
    canRemove(comment: string): boolean | undefined;
    _hasFirst: boolean | undefined;
}
