{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async token => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const userResponse = await api.get('/api/utilisateurs/users/me/');\n      let profileData = null;\n      try {\n        const profileResponse = await api.get('/api/utilisateurs/profiles/me/');\n        profileData = profileResponse.data;\n\n        // Traiter l'URL de la photo si elle existe\n        if (profileData && profileData.photo) {\n          // Utiliser config.getImageUrl pour normaliser l'URL de la photo\n          profileData.photo = config.getImageUrl(profileData.photo);\n          console.log('URL de la photo de profil normalisée:', profileData.photo);\n        }\n      } catch (profileError) {\n        console.warn('Erreur lors de la récupération du profil, utilisation d\\'un profil par défaut:', profileError);\n        // Utiliser un profil par défaut si le profil n'existe pas\n        profileData = {\n          user_type: 'etudiant',\n          email_verified: true\n        };\n      }\n      const userData = {\n        ...userResponse.data,\n        profile: profileData\n      };\n      setCurrentUser(userData);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n\n      // Si l'erreur est due à un token invalide ou expiré\n      if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n        setAuthError('Session expirée. Veuillez vous reconnecter.');\n      } else {\n        setAuthError('Impossible de récupérer les informations utilisateur');\n      }\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (token) {\n          // Configurer les en-têtes d'authentification pour toutes les requêtes\n          axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n          api.defaults.headers.common['Authorization'] = `Token ${token}`;\n          const success = await fetchUserInfo(token);\n          if (!success) {\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n            delete api.defaults.headers.common['Authorization'];\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        } else {\n          setIsAuthenticated(false);\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n        delete api.defaults.headers.common['Authorization'];\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, [fetchUserInfo]);\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n      console.log('Tentative de connexion pour:', username);\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n      console.log('Réponse du serveur:', response.data);\n      const {\n        user,\n        profile,\n        token\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n        console.log('Token stocké avec succès');\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n\n      // S'assurer que le profil est défini\n      let profileData = profile || {\n        user_type: 'etudiant',\n        email_verified: true\n      };\n\n      // Traiter l'URL de la photo si elle existe\n      if (profileData && profileData.photo) {\n        // Utiliser config.getImageUrl pour normaliser l'URL de la photo\n        profileData.photo = config.getImageUrl(profileData.photo);\n        console.log('URL de la photo de profil normalisée (login):', profileData.photo);\n      }\n      const userWithProfile = {\n        ...user,\n        profile: profileData\n      };\n      setCurrentUser(userWithProfile);\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Erreur de connexion:', error);\n\n      // Afficher plus de détails sur l'erreur pour le débogage\n      if (error.response) {\n        console.error('Détails de l\\'erreur:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n      }\n      setAuthError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Erreur de connexion');\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Erreur de connexion'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const {\n        token,\n        user\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response5, _error$response5$data;\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && _error$response4.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n      return {\n        success: false,\n        message: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || 'Erreur d\\'inscription'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour récupérer les notifications\n  const getNotifications = async () => {\n    try {\n      const response = await api.get('/api/utilisateurs/notifications/');\n      return response;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des notifications:', error);\n      throw error;\n    }\n  };\n\n  // Fonction pour marquer une notification comme lue\n  const markNotificationRead = async notificationId => {\n    try {\n      const response = await api.post(`/api/utilisateurs/notifications/${notificationId}/read/`);\n      return response;\n    } catch (error) {\n      console.error('Erreur lors du marquage de la notification:', error);\n      throw error;\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async userData => {\n    try {\n      setAuthError(null);\n      console.log('Mise à jour du profil avec les données:', userData);\n\n      // Créer un objet FormData si ce n'est pas déjà le cas\n      let formData = userData;\n      if (!(userData instanceof FormData)) {\n        formData = new FormData();\n\n        // Ajouter les données utilisateur\n        if (userData.user) {\n          formData.append('user', JSON.stringify(userData.user));\n        }\n\n        // Ajouter la photo si elle existe\n        if (userData.photo) {\n          formData.append('photo', userData.photo);\n        }\n      }\n\n      // Afficher le contenu du FormData\n      console.log('FormData à envoyer:');\n      for (let pair of formData.entries()) {\n        console.log(`${pair[0]}: ${pair[1]}`);\n      }\n\n      // Envoyer la requête avec les en-têtes appropriés\n      const response = await api.put('/api/utilisateurs/update-profile/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        timeout: 30000 // Timeout plus long pour l'upload des fichiers\n      });\n      console.log('Réponse de mise à jour du profil:', response.data);\n\n      // Traiter l'URL de la photo dans la réponse si elle existe\n      if (response.data && response.data.profile && response.data.profile.photo) {\n        response.data.profile.photo = config.getImageUrl(response.data.profile.photo);\n        console.log('URL de la photo de profil normalisée (update):', response.data.profile.photo);\n      }\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response6, _error$response7, _error$response7$data, _error$response8, _error$response8$data;\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      console.error('Détails de l\\'erreur:', (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data);\n      setAuthError(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile,\n    getNotifications,\n    markNotificationRead\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"MmdmBtoNyEXLJ8ErwOEOz7sUNs4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useCallback", "axios", "api", "config", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "authError", "setAuthError", "fetchUserInfo", "token", "userResponse", "get", "profileData", "profileResponse", "data", "photo", "getImageUrl", "console", "log", "profileError", "warn", "user_type", "email_verified", "userData", "profile", "error", "response", "status", "checkAuth", "localStorage", "getItem", "defaults", "headers", "common", "success", "removeItem", "login", "username", "password", "post", "user", "setItem", "message", "userWithProfile", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "register", "autoLogin", "_error$response3", "_error$response3$data", "_error$response4", "_error$response5", "_error$response5$data", "errorMessages", "field", "Array", "isArray", "push", "join", "length", "logout", "getNotifications", "markNotificationRead", "notificationId", "updateProfile", "formData", "FormData", "append", "JSON", "stringify", "pair", "entries", "put", "timeout", "_error$response6", "_error$response7", "_error$response7$data", "_error$response8", "_error$response8$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\nimport config from '../config';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async (token) => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const userResponse = await api.get('/api/utilisateurs/users/me/');\n\n      let profileData = null;\n      try {\n        const profileResponse = await api.get('/api/utilisateurs/profiles/me/');\n        profileData = profileResponse.data;\n\n        // Traiter l'URL de la photo si elle existe\n        if (profileData && profileData.photo) {\n          // Utiliser config.getImageUrl pour normaliser l'URL de la photo\n          profileData.photo = config.getImageUrl(profileData.photo);\n          console.log('URL de la photo de profil normalisée:', profileData.photo);\n        }\n      } catch (profileError) {\n        console.warn('Erreur lors de la récupération du profil, utilisation d\\'un profil par défaut:', profileError);\n        // Utiliser un profil par défaut si le profil n'existe pas\n        profileData = {\n          user_type: 'etudiant',\n          email_verified: true\n        };\n      }\n\n      const userData = {\n        ...userResponse.data,\n        profile: profileData\n      };\n\n      setCurrentUser(userData);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n\n      // Si l'erreur est due à un token invalide ou expiré\n      if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n        setAuthError('Session expirée. Veuillez vous reconnecter.');\n      } else {\n        setAuthError('Impossible de récupérer les informations utilisateur');\n      }\n\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (token) {\n          // Configurer les en-têtes d'authentification pour toutes les requêtes\n          axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n          api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n          const success = await fetchUserInfo(token);\n\n          if (!success) {\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n            delete api.defaults.headers.common['Authorization'];\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        } else {\n          setIsAuthenticated(false);\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n        delete api.defaults.headers.common['Authorization'];\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [fetchUserInfo]);\n\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n\n      console.log('Tentative de connexion pour:', username);\n\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n\n      console.log('Réponse du serveur:', response.data);\n\n      const { user, profile, token } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n        console.log('Token stocké avec succès');\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n\n      // S'assurer que le profil est défini\n      let profileData = profile || {\n        user_type: 'etudiant',\n        email_verified: true\n      };\n\n      // Traiter l'URL de la photo si elle existe\n      if (profileData && profileData.photo) {\n        // Utiliser config.getImageUrl pour normaliser l'URL de la photo\n        profileData.photo = config.getImageUrl(profileData.photo);\n        console.log('URL de la photo de profil normalisée (login):', profileData.photo);\n      }\n\n      const userWithProfile = {\n        ...user,\n        profile: profileData\n      };\n\n      setCurrentUser(userWithProfile);\n      setIsAuthenticated(true);\n\n      return { success: true };\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n\n      // Afficher plus de détails sur l'erreur pour le débogage\n      if (error.response) {\n        console.error('Détails de l\\'erreur:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n      }\n\n      setAuthError(error.response?.data?.detail || 'Erreur de connexion');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur de connexion'\n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const { token, user } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if (error.response?.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur d\\'inscription'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour récupérer les notifications\n  const getNotifications = async () => {\n    try {\n      const response = await api.get('/api/utilisateurs/notifications/');\n      return response;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des notifications:', error);\n      throw error;\n    }\n  };\n\n  // Fonction pour marquer une notification comme lue\n  const markNotificationRead = async (notificationId) => {\n    try {\n      const response = await api.post(`/api/utilisateurs/notifications/${notificationId}/read/`);\n      return response;\n    } catch (error) {\n      console.error('Erreur lors du marquage de la notification:', error);\n      throw error;\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async (userData) => {\n    try {\n      setAuthError(null);\n      console.log('Mise à jour du profil avec les données:', userData);\n\n      // Créer un objet FormData si ce n'est pas déjà le cas\n      let formData = userData;\n      if (!(userData instanceof FormData)) {\n        formData = new FormData();\n\n        // Ajouter les données utilisateur\n        if (userData.user) {\n          formData.append('user', JSON.stringify(userData.user));\n        }\n\n        // Ajouter la photo si elle existe\n        if (userData.photo) {\n          formData.append('photo', userData.photo);\n        }\n      }\n\n      // Afficher le contenu du FormData\n      console.log('FormData à envoyer:');\n      for (let pair of formData.entries()) {\n        console.log(`${pair[0]}: ${pair[1]}`);\n      }\n\n      // Envoyer la requête avec les en-têtes appropriés\n      const response = await api.put('/api/utilisateurs/update-profile/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        timeout: 30000, // Timeout plus long pour l'upload des fichiers\n      });\n\n      console.log('Réponse de mise à jour du profil:', response.data);\n\n      // Traiter l'URL de la photo dans la réponse si elle existe\n      if (response.data && response.data.profile && response.data.profile.photo) {\n        response.data.profile.photo = config.getImageUrl(response.data.profile.photo);\n        console.log('URL de la photo de profil normalisée (update):', response.data.profile.photo);\n      }\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n\n      return { success: true, data: response.data };\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      console.error('Détails de l\\'erreur:', error.response?.data);\n      setAuthError(error.response?.data?.detail || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile,\n    getNotifications,\n    markNotificationRead\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,WAAW,gBAAGV,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMW,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMV,UAAU,CAACQ,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMuB,aAAa,GAAGpB,WAAW,CAAC,MAAOqB,KAAK,IAAK;IACjD,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,MAAMpB,GAAG,CAACqB,GAAG,CAAC,6BAA6B,CAAC;MAEjE,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAI;QACF,MAAMC,eAAe,GAAG,MAAMvB,GAAG,CAACqB,GAAG,CAAC,gCAAgC,CAAC;QACvEC,WAAW,GAAGC,eAAe,CAACC,IAAI;;QAElC;QACA,IAAIF,WAAW,IAAIA,WAAW,CAACG,KAAK,EAAE;UACpC;UACAH,WAAW,CAACG,KAAK,GAAGxB,MAAM,CAACyB,WAAW,CAACJ,WAAW,CAACG,KAAK,CAAC;UACzDE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEN,WAAW,CAACG,KAAK,CAAC;QACzE;MACF,CAAC,CAAC,OAAOI,YAAY,EAAE;QACrBF,OAAO,CAACG,IAAI,CAAC,gFAAgF,EAAED,YAAY,CAAC;QAC5G;QACAP,WAAW,GAAG;UACZS,SAAS,EAAE,UAAU;UACrBC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,MAAMC,QAAQ,GAAG;QACf,GAAGb,YAAY,CAACI,IAAI;QACpBU,OAAO,EAAEZ;MACX,CAAC;MAEDX,cAAc,CAACsB,QAAQ,CAAC;MACxBpB,kBAAkB,CAAC,IAAI,CAAC;MACxBI,YAAY,CAAC,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;;MAEpF;MACA,IAAIA,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAIF,KAAK,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,CAAC,EAAE;QACtFpB,YAAY,CAAC,6CAA6C,CAAC;MAC7D,CAAC,MAAM;QACLA,YAAY,CAAC,sDAAsD,CAAC;MACtE;MAEA,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApB,SAAS,CAAC,MAAM;IACd,MAAMyC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFvB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,KAAK,GAAGoB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAIrB,KAAK,EAAE;UACT;UACApB,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;UACjEnB,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;UAE/D,MAAMyB,OAAO,GAAG,MAAM1B,aAAa,CAACC,KAAK,CAAC;UAE1C,IAAI,CAACyB,OAAO,EAAE;YACZL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;YAChC,OAAO9C,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;YACrD,OAAO3C,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;YACnD9B,kBAAkB,CAAC,KAAK,CAAC;YACzBF,cAAc,CAAC,IAAI,CAAC;UACtB;QACF,CAAC,MAAM;UACLE,kBAAkB,CAAC,KAAK,CAAC;UACzBF,cAAc,CAAC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDI,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChC,OAAO9C,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;QACrD,OAAO3C,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;QACnD9B,kBAAkB,CAAC,KAAK,CAAC;QACzBF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACpB,aAAa,CAAC,CAAC;EAEnB,MAAM4B,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF/B,YAAY,CAAC,IAAI,CAAC;MAElBU,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmB,QAAQ,CAAC;MAErD,MAAMX,QAAQ,GAAG,MAAMpC,GAAG,CAACiD,IAAI,CAAC,0BAA0B,EAAE;QAC1DF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEFrB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEQ,QAAQ,CAACZ,IAAI,CAAC;MAEjD,MAAM;QAAE0B,IAAI;QAAEhB,OAAO;QAAEf;MAAM,CAAC,GAAGiB,QAAQ,CAACZ,IAAI;MAE9C,IAAIL,KAAK,EAAE;QACToB,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEhC,KAAK,CAAC;QACpCpB,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;QACjEnB,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;QAE/DQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC,MAAM;QACLD,OAAO,CAACQ,KAAK,CAAC,8BAA8B,CAAC;QAC7ClB,YAAY,CAAC,+CAA+C,CAAC;QAC7D,OAAO;UACL2B,OAAO,EAAE,KAAK;UACdQ,OAAO,EAAE;QACX,CAAC;MACH;;MAEA;MACA,IAAI9B,WAAW,GAAGY,OAAO,IAAI;QAC3BH,SAAS,EAAE,UAAU;QACrBC,cAAc,EAAE;MAClB,CAAC;;MAED;MACA,IAAIV,WAAW,IAAIA,WAAW,CAACG,KAAK,EAAE;QACpC;QACAH,WAAW,CAACG,KAAK,GAAGxB,MAAM,CAACyB,WAAW,CAACJ,WAAW,CAACG,KAAK,CAAC;QACzDE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEN,WAAW,CAACG,KAAK,CAAC;MACjF;MAEA,MAAM4B,eAAe,GAAG;QACtB,GAAGH,IAAI;QACPhB,OAAO,EAAEZ;MACX,CAAC;MAEDX,cAAc,CAAC0C,eAAe,CAAC;MAC/BxC,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAE+B,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd9B,OAAO,CAACQ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClBT,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAE;UACrCE,MAAM,EAAEF,KAAK,CAACC,QAAQ,CAACC,MAAM;UAC7Bb,IAAI,EAAEW,KAAK,CAACC,QAAQ,CAACZ,IAAI;UACzBkB,OAAO,EAAEP,KAAK,CAACC,QAAQ,CAACM;QAC1B,CAAC,CAAC;MACJ;MAEAzB,YAAY,CAAC,EAAAqC,eAAA,GAAAnB,KAAK,CAACC,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB9B,IAAI,cAAA+B,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,qBAAqB,CAAC;MACnE,OAAO;QACLd,OAAO,EAAE,KAAK;QACdQ,OAAO,EAAE,EAAAI,gBAAA,GAAArB,KAAK,CAACC,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAO1B,QAAQ,IAAK;IACnC,IAAI;MACFhB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMmB,QAAQ,GAAG,MAAMpC,GAAG,CAACiD,IAAI,CAAC,6BAA6B,EAAEhB,QAAQ,CAAC;;MAExE;MACA,MAAM;QAAEd,KAAK;QAAE+B;MAAK,CAAC,GAAGd,QAAQ,CAACZ,IAAI;MAErC,IAAIL,KAAK,EAAE;QACToB,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEhC,KAAK,CAAC;QACpCpB,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;QACjEnB,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASxB,KAAK,EAAE;QAE/DR,cAAc,CAACuC,IAAI,CAAC;QACpBrC,kBAAkB,CAAC,IAAI,CAAC;MAC1B;MAEA,OAAO;QACL+B,OAAO,EAAE,IAAI;QACbQ,OAAO,EAAEhB,QAAQ,CAACZ,IAAI,CAACkC,MAAM;QAC7BE,SAAS,EAAE,CAAC,CAACzC,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,CAAC,OAAOgB,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdtC,OAAO,CAACQ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ClB,YAAY,CAAC,EAAA4C,gBAAA,GAAA1B,KAAK,CAACC,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,IAAI,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,uBAAuB,CAAC;;MAErE;MACA,IAAI,CAAAK,gBAAA,GAAA5B,KAAK,CAACC,QAAQ,cAAA2B,gBAAA,eAAdA,gBAAA,CAAgBvC,IAAI,IAAI,OAAOW,KAAK,CAACC,QAAQ,CAACZ,IAAI,KAAK,QAAQ,EAAE;QACnE,MAAM0C,aAAa,GAAG,EAAE;QACxB,KAAK,MAAMC,KAAK,IAAIhC,KAAK,CAACC,QAAQ,CAACZ,IAAI,EAAE;UACvC,IAAI4C,KAAK,CAACC,OAAO,CAAClC,KAAK,CAACC,QAAQ,CAACZ,IAAI,CAAC2C,KAAK,CAAC,CAAC,EAAE;YAC7CD,aAAa,CAACI,IAAI,CAAC,GAAGH,KAAK,KAAKhC,KAAK,CAACC,QAAQ,CAACZ,IAAI,CAAC2C,KAAK,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UAC1E;QACF;QAEA,IAAIL,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;UAC5B,OAAO;YACL5B,OAAO,EAAE,KAAK;YACdQ,OAAO,EAAEc,aAAa,CAACK,IAAI,CAAC,IAAI;UAClC,CAAC;QACH;MACF;MAEA,OAAO;QACL3B,OAAO,EAAE,KAAK;QACdQ,OAAO,EAAE,EAAAY,gBAAA,GAAA7B,KAAK,CAACC,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMe,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMzE,GAAG,CAACiD,IAAI,CAAC,2BAA2B,CAAC;IAC7C,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRxB,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,KAAK,CAAC;MACzBI,YAAY,CAAC,IAAI,CAAC;MAClBsB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO9C,KAAK,CAAC0C,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACrD,OAAO3C,GAAG,CAACyC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM+B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMtC,QAAQ,GAAG,MAAMpC,GAAG,CAACqB,GAAG,CAAC,kCAAkC,CAAC;MAClE,OAAOe,QAAQ;IACjB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMwC,oBAAoB,GAAG,MAAOC,cAAc,IAAK;IACrD,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMpC,GAAG,CAACiD,IAAI,CAAC,mCAAmC2B,cAAc,QAAQ,CAAC;MAC1F,OAAOxC,QAAQ;IACjB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0C,aAAa,GAAG,MAAO5C,QAAQ,IAAK;IACxC,IAAI;MACFhB,YAAY,CAAC,IAAI,CAAC;MAClBU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEK,QAAQ,CAAC;;MAEhE;MACA,IAAI6C,QAAQ,GAAG7C,QAAQ;MACvB,IAAI,EAAEA,QAAQ,YAAY8C,QAAQ,CAAC,EAAE;QACnCD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAEzB;QACA,IAAI9C,QAAQ,CAACiB,IAAI,EAAE;UACjB4B,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjD,QAAQ,CAACiB,IAAI,CAAC,CAAC;QACxD;;QAEA;QACA,IAAIjB,QAAQ,CAACR,KAAK,EAAE;UAClBqD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE/C,QAAQ,CAACR,KAAK,CAAC;QAC1C;MACF;;MAEA;MACAE,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,KAAK,IAAIuD,IAAI,IAAIL,QAAQ,CAACM,OAAO,CAAC,CAAC,EAAE;QACnCzD,OAAO,CAACC,GAAG,CAAC,GAAGuD,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MACvC;;MAEA;MACA,MAAM/C,QAAQ,GAAG,MAAMpC,GAAG,CAACqF,GAAG,CAAC,mCAAmC,EAAEP,QAAQ,EAAE;QAC5EpC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD4C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEF3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEQ,QAAQ,CAACZ,IAAI,CAAC;;MAE/D;MACA,IAAIY,QAAQ,CAACZ,IAAI,IAAIY,QAAQ,CAACZ,IAAI,CAACU,OAAO,IAAIE,QAAQ,CAACZ,IAAI,CAACU,OAAO,CAACT,KAAK,EAAE;QACzEW,QAAQ,CAACZ,IAAI,CAACU,OAAO,CAACT,KAAK,GAAGxB,MAAM,CAACyB,WAAW,CAACU,QAAQ,CAACZ,IAAI,CAACU,OAAO,CAACT,KAAK,CAAC;QAC7EE,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEQ,QAAQ,CAACZ,IAAI,CAACU,OAAO,CAACT,KAAK,CAAC;MAC5F;;MAEA;MACA,MAAMP,aAAa,CAACqB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;MAElD,OAAO;QAAEI,OAAO,EAAE,IAAI;QAAEpB,IAAI,EAAEY,QAAQ,CAACZ;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAoD,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhE,OAAO,CAACQ,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChER,OAAO,CAACQ,KAAK,CAAC,uBAAuB,GAAAoD,gBAAA,GAAEpD,KAAK,CAACC,QAAQ,cAAAmD,gBAAA,uBAAdA,gBAAA,CAAgB/D,IAAI,CAAC;MAC5DP,YAAY,CAAC,EAAAuE,gBAAA,GAAArD,KAAK,CAACC,QAAQ,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsB/B,MAAM,KAAI,yCAAyC,CAAC;MACvF,OAAO;QACLd,OAAO,EAAE,KAAK;QACdQ,OAAO,EAAE,EAAAsC,gBAAA,GAAAvD,KAAK,CAACC,QAAQ,cAAAsD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlE,IAAI,cAAAmE,qBAAA,uBAApBA,qBAAA,CAAsBjC,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMkC,KAAK,GAAG;IACZlF,WAAW;IACXE,eAAe;IACfE,OAAO;IACPE,SAAS;IACT8B,KAAK;IACLa,QAAQ;IACRc,MAAM;IACNI,aAAa;IACbH,gBAAgB;IAChBC;EACF,CAAC;EAED,oBACExE,OAAA,CAACC,WAAW,CAACyF,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApF,QAAA,EAChCA;EAAQ;IAAAsF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACxF,GAAA,CA3UWF,YAAY;AAAA2F,EAAA,GAAZ3F,YAAY;AAAA,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}