{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  // Axios le définira automatiquement avec la boundary correcte\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n\n    // Log pour le débogage\n    console.log('Envoi de FormData:', config.url);\n    console.log('Méthode:', config.method);\n    console.log('Headers:', config.headers);\n  }\n  return config;\n}, error => {\n  console.error('Erreur dans l\\'intercepteur de requête:', error);\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  // Log pour le débogage des réponses réussies\n  console.log(`Réponse réussie de ${response.config.url}:`, response.data);\n  return response;\n}, error => {\n  // Créer un objet d'erreur plus détaillé\n  const enhancedError = {\n    message: 'Une erreur est survenue',\n    originalError: error,\n    timestamp: new Date().toISOString()\n  };\n\n  // Gérer les erreurs réseau (pas de réponse du serveur)\n  if (!error.response) {\n    enhancedError.type = 'network';\n    enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n    console.error('Erreur réseau:', error);\n  }\n  // Gérer les erreurs avec réponse du serveur\n  else {\n    enhancedError.status = error.response.status;\n    enhancedError.data = error.response.data;\n\n    // Log détaillé de l'erreur\n    console.error(`Erreur HTTP ${error.response.status} pour ${error.config.url}:`, {\n      status: error.response.status,\n      statusText: error.response.statusText,\n      data: error.response.data,\n      headers: error.response.headers,\n      config: {\n        url: error.config.url,\n        method: error.config.method,\n        headers: error.config.headers,\n        data: error.config.data instanceof FormData ? 'FormData (non affichable)' : error.config.data\n      }\n    });\n\n    // Gérer les erreurs d'authentification (401)\n    if (error.response.status === 401) {\n      enhancedError.type = 'auth';\n      enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    // Gérer les erreurs de permission (403)\n    else if (error.response.status === 403) {\n      enhancedError.type = 'permission';\n      enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n    }\n    // Gérer les erreurs de validation (400)\n    else if (error.response.status === 400) {\n      enhancedError.type = 'validation';\n      enhancedError.message = 'Les données fournies sont invalides.';\n\n      // Construire un message d'erreur plus détaillé\n      if (typeof error.response.data === 'object') {\n        const errorMessages = [];\n        Object.entries(error.response.data).forEach(([field, messages]) => {\n          if (Array.isArray(messages)) {\n            errorMessages.push(`${field}: ${messages.join(', ')}`);\n          } else if (typeof messages === 'string') {\n            errorMessages.push(`${field}: ${messages}`);\n          } else {\n            errorMessages.push(`${field}: Erreur de validation`);\n          }\n        });\n        if (errorMessages.length > 0) {\n          enhancedError.message = `Erreurs de validation: ${errorMessages.join('; ')}`;\n        }\n      }\n    }\n    // Gérer les erreurs serveur (500)\n    else if (error.response.status >= 500) {\n      enhancedError.type = 'server';\n      enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n    }\n  }\n\n  // Enregistrer l'erreur dans la console avec plus de détails\n  console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n  return Promise.reject(enhancedError);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.LIVRES, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  getCategories: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  create: data => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour de livre:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 30000\n    });\n  },\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: params => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.EBOOKS, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  getCategories: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  create: data => {\n    console.log('Envoi de données ebook au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.EBOOKS, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour d\\'ebook:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 30000\n    });\n  },\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: id => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: id => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: data => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: id => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`)\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "console", "log", "url", "method", "error", "Promise", "reject", "response", "enhancedError", "message", "originalError", "timestamp", "Date", "toISOString", "type", "status", "statusText", "removeItem", "window", "location", "href", "errorMessages", "Object", "entries", "for<PERSON>ach", "field", "messages", "Array", "isArray", "push", "join", "length", "livresAPI", "getAll", "params", "get", "ENDPOINTS", "LIVRES", "getById", "id", "getCategories", "CATEGORIES", "post", "timeout", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "EBOOKS", "categoriesAPI", "empruntsAPI", "EMPRUNTS", "retourner", "reservationsAPI", "RESERVATIONS", "annuler", "utilisateursAPI", "getProfile", "UTILISATEURS", "updateProfile", "getNotifications", "markNotificationRead", "statisticsAPI", "STATISTICS"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    // Axios le définira automatiquement avec la boundary correcte\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n\n      // Log pour le débogage\n      console.log('Envoi de FormData:', config.url);\n      console.log('Méthode:', config.method);\n      console.log('Headers:', config.headers);\n    }\n\n    return config;\n  },\n  (error) => {\n    console.error('Erreur dans l\\'intercepteur de requête:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    // Log pour le débogage des réponses réussies\n    console.log(`Réponse réussie de ${response.config.url}:`, response.data);\n    return response;\n  },\n  (error) => {\n    // Créer un objet d'erreur plus détaillé\n    const enhancedError = {\n      message: 'Une erreur est survenue',\n      originalError: error,\n      timestamp: new Date().toISOString(),\n    };\n\n    // Gérer les erreurs réseau (pas de réponse du serveur)\n    if (!error.response) {\n      enhancedError.type = 'network';\n      enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n      console.error('Erreur réseau:', error);\n    }\n    // Gérer les erreurs avec réponse du serveur\n    else {\n      enhancedError.status = error.response.status;\n      enhancedError.data = error.response.data;\n\n      // Log détaillé de l'erreur\n      console.error(`Erreur HTTP ${error.response.status} pour ${error.config.url}:`, {\n        status: error.response.status,\n        statusText: error.response.statusText,\n        data: error.response.data,\n        headers: error.response.headers,\n        config: {\n          url: error.config.url,\n          method: error.config.method,\n          headers: error.config.headers,\n          data: error.config.data instanceof FormData ? 'FormData (non affichable)' : error.config.data,\n        }\n      });\n\n      // Gérer les erreurs d'authentification (401)\n      if (error.response.status === 401) {\n        enhancedError.type = 'auth';\n        enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n        // Supprimer le token et rediriger vers la page de connexion\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n      // Gérer les erreurs de permission (403)\n      else if (error.response.status === 403) {\n        enhancedError.type = 'permission';\n        enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n      }\n      // Gérer les erreurs de validation (400)\n      else if (error.response.status === 400) {\n        enhancedError.type = 'validation';\n        enhancedError.message = 'Les données fournies sont invalides.';\n\n        // Construire un message d'erreur plus détaillé\n        if (typeof error.response.data === 'object') {\n          const errorMessages = [];\n          Object.entries(error.response.data).forEach(([field, messages]) => {\n            if (Array.isArray(messages)) {\n              errorMessages.push(`${field}: ${messages.join(', ')}`);\n            } else if (typeof messages === 'string') {\n              errorMessages.push(`${field}: ${messages}`);\n            } else {\n              errorMessages.push(`${field}: Erreur de validation`);\n            }\n          });\n\n          if (errorMessages.length > 0) {\n            enhancedError.message = `Erreurs de validation: ${errorMessages.join('; ')}`;\n          }\n        }\n      }\n      // Gérer les erreurs serveur (500)\n      else if (error.response.status >= 500) {\n        enhancedError.type = 'server';\n        enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n      }\n    }\n\n    // Enregistrer l'erreur dans la console avec plus de détails\n    console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n\n    return Promise.reject(enhancedError);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  getCategories: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  create: (data) => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000,\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour de livre:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 30000,\n    });\n  },\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: (params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, { params }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.EBOOKS, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  getCategories: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  create: (data) => {\n    console.log('Envoi de données ebook au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.EBOOKS, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000,\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour d\\'ebook:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 30000,\n    });\n  },\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: (id) => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: (id) => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: (data) => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;;IAErC;IACAW,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,MAAM,CAACQ,GAAG,CAAC;IAC7CF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,MAAM,CAACS,MAAM,CAAC;IACtCH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,MAAM,CAACL,OAAO,CAAC;EACzC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAU,KAAK,IAAK;EACTJ,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;EAC/D,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAnB,GAAG,CAACM,YAAY,CAACgB,QAAQ,CAACd,GAAG,CAC1Bc,QAAQ,IAAK;EACZ;EACAP,OAAO,CAACC,GAAG,CAAC,sBAAsBM,QAAQ,CAACb,MAAM,CAACQ,GAAG,GAAG,EAAEK,QAAQ,CAACT,IAAI,CAAC;EACxE,OAAOS,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,MAAMI,aAAa,GAAG;IACpBC,OAAO,EAAE,yBAAyB;IAClCC,aAAa,EAAEN,KAAK;IACpBO,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,IAAI,CAACT,KAAK,CAACG,QAAQ,EAAE;IACnBC,aAAa,CAACM,IAAI,GAAG,SAAS;IAC9BN,aAAa,CAACC,OAAO,GAAG,oEAAoE;IAC5FT,OAAO,CAACI,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACxC;EACA;EAAA,KACK;IACHI,aAAa,CAACO,MAAM,GAAGX,KAAK,CAACG,QAAQ,CAACQ,MAAM;IAC5CP,aAAa,CAACV,IAAI,GAAGM,KAAK,CAACG,QAAQ,CAACT,IAAI;;IAExC;IACAE,OAAO,CAACI,KAAK,CAAC,eAAeA,KAAK,CAACG,QAAQ,CAACQ,MAAM,SAASX,KAAK,CAACV,MAAM,CAACQ,GAAG,GAAG,EAAE;MAC9Ea,MAAM,EAAEX,KAAK,CAACG,QAAQ,CAACQ,MAAM;MAC7BC,UAAU,EAAEZ,KAAK,CAACG,QAAQ,CAACS,UAAU;MACrClB,IAAI,EAAEM,KAAK,CAACG,QAAQ,CAACT,IAAI;MACzBT,OAAO,EAAEe,KAAK,CAACG,QAAQ,CAAClB,OAAO;MAC/BK,MAAM,EAAE;QACNQ,GAAG,EAAEE,KAAK,CAACV,MAAM,CAACQ,GAAG;QACrBC,MAAM,EAAEC,KAAK,CAACV,MAAM,CAACS,MAAM;QAC3Bd,OAAO,EAAEe,KAAK,CAACV,MAAM,CAACL,OAAO;QAC7BS,IAAI,EAAEM,KAAK,CAACV,MAAM,CAACI,IAAI,YAAYC,QAAQ,GAAG,2BAA2B,GAAGK,KAAK,CAACV,MAAM,CAACI;MAC3F;IACF,CAAC,CAAC;;IAEF;IACA,IAAIM,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACjCP,aAAa,CAACM,IAAI,GAAG,MAAM;MAC3BN,aAAa,CAACC,OAAO,GAAG,6CAA6C;MACrE;MACAb,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IACA;IAAA,KACK,IAAIhB,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACtCP,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,iEAAiE;IAC3F;IACA;IAAA,KACK,IAAIL,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACtCP,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,sCAAsC;;MAE9D;MACA,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC3C,MAAMuB,aAAa,GAAG,EAAE;QACxBC,MAAM,CAACC,OAAO,CAACnB,KAAK,CAACG,QAAQ,CAACT,IAAI,CAAC,CAAC0B,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;UACjE,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;YAC3BL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,KAAKC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UACxD,CAAC,MAAM,IAAI,OAAOJ,QAAQ,KAAK,QAAQ,EAAE;YACvCL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,KAAKC,QAAQ,EAAE,CAAC;UAC7C,CAAC,MAAM;YACLL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,wBAAwB,CAAC;UACtD;QACF,CAAC,CAAC;QAEF,IAAIJ,aAAa,CAACU,MAAM,GAAG,CAAC,EAAE;UAC5BvB,aAAa,CAACC,OAAO,GAAG,0BAA0BY,aAAa,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9E;MACF;IACF;IACA;IAAA,KACK,IAAI1B,KAAK,CAACG,QAAQ,CAACQ,MAAM,IAAI,GAAG,EAAE;MACrCP,aAAa,CAACM,IAAI,GAAG,QAAQ;MAC7BN,aAAa,CAACC,OAAO,GAAG,uEAAuE;IACjG;EACF;;EAEA;EACAT,OAAO,CAACI,KAAK,CAAC,eAAeI,aAAa,CAACC,OAAO,EAAE,EAAED,aAAa,CAAC;EAEpE,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;AACtC,CACF,CAAC;;AAED;AACA,OAAO,MAAMwB,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACC,MAAM,EAAE;IAAEH;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAChEC,aAAa,EAAEA,CAAA,KAAMvD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;EAC7DvD,MAAM,EAAGY,IAAI,IAAK;IAChBE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEH,IAAI,CAAC;IACjD;IACA,OAAOb,GAAG,CAACyD,IAAI,CAAC1D,UAAU,CAACoD,SAAS,CAACC,MAAM,EAAEvC,IAAI,EAAE;MACjDT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD;MACAsD,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,EAAEA,CAACL,EAAE,EAAEzC,IAAI,KAAK;IACpBE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,EAAE,EAAEzC,IAAI,CAAC;IAC9C,OAAOb,GAAG,CAAC4D,GAAG,CAAC,GAAG7D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,EAAEzC,IAAI,EAAE;MAC3DT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDsD,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDG,MAAM,EAAGP,EAAE,IAAKtD,GAAG,CAAC6D,MAAM,CAAC,GAAG9D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAClEQ,SAAS,EAAGR,EAAE,IAAKtD,GAAG,CAACyD,IAAI,CAAC,GAAG1D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,aAAa,CAAC;EAC7ES,QAAQ,EAAGT,EAAE,IAAKtD,GAAG,CAACyD,IAAI,CAAC,GAAG1D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,YAAY,CAAC;EAC3EU,kBAAkB,EAAGf,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,kBAAkB,EAAE;IAAEH;EAAO,CAAC,CAAC;EACrGgB,UAAU,EAAEA,CAACX,EAAE,EAAEL,MAAM,KAAKjD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,WAAW,EAAE;IAAEL;EAAO,CAAC;AAChG,CAAC;;AAED;AACA,OAAO,MAAMiB,SAAS,GAAG;EACvBlB,MAAM,EAAGC,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACgB,MAAM,EAAE;IAAElB;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACgB,MAAM,GAAGb,EAAE,GAAG,CAAC;EAChEC,aAAa,EAAEA,CAAA,KAAMvD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;EAC7DvD,MAAM,EAAGY,IAAI,IAAK;IAChBE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEH,IAAI,CAAC;IACvD;IACA,OAAOb,GAAG,CAACyD,IAAI,CAAC1D,UAAU,CAACoD,SAAS,CAACgB,MAAM,EAAEtD,IAAI,EAAE;MACjDT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD;MACAsD,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,EAAEA,CAACL,EAAE,EAAEzC,IAAI,KAAK;IACpBE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,EAAE,EAAEzC,IAAI,CAAC;IAC9C,OAAOb,GAAG,CAAC4D,GAAG,CAAC,GAAG7D,UAAU,CAACoD,SAAS,CAACgB,MAAM,GAAGb,EAAE,GAAG,EAAEzC,IAAI,EAAE;MAC3DT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDsD,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDG,MAAM,EAAGP,EAAE,IAAKtD,GAAG,CAAC6D,MAAM,CAAC,GAAG9D,UAAU,CAACoD,SAAS,CAACgB,MAAM,GAAGb,EAAE,GAAG;AACnE,CAAC;;AAED;AACA,OAAO,MAAMc,aAAa,GAAG;EAC3BpB,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;EACtDH,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG,CAAC;EACpErD,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACyD,IAAI,CAAC1D,UAAU,CAACoD,SAAS,CAACK,UAAU,EAAE3C,IAAI,CAAC;EACjE8C,MAAM,EAAEA,CAACL,EAAE,EAAEzC,IAAI,KAAKb,GAAG,CAAC4D,GAAG,CAAC,GAAG7D,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG,EAAEzC,IAAI,CAAC;EAC/EgD,MAAM,EAAGP,EAAE,IAAKtD,GAAG,CAAC6D,MAAM,CAAC,GAAG9D,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG;AACvE,CAAC;;AAED;AACA,OAAO,MAAMe,WAAW,GAAG;EACzBrB,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACmB,QAAQ,CAAC;EACpDjB,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACmB,QAAQ,GAAGhB,EAAE,GAAG,CAAC;EAClEiB,SAAS,EAAGjB,EAAE,IAAKtD,GAAG,CAACyD,IAAI,CAAC,GAAG1D,UAAU,CAACoD,SAAS,CAACmB,QAAQ,GAAGhB,EAAE,aAAa;AAChF,CAAC;;AAED;AACA,OAAO,MAAMkB,eAAe,GAAG;EAC7BxB,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACsB,YAAY,CAAC;EACxDpB,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACsB,YAAY,GAAGnB,EAAE,GAAG,CAAC;EACtEoB,OAAO,EAAGpB,EAAE,IAAKtD,GAAG,CAAC6D,MAAM,CAAC,GAAG9D,UAAU,CAACoD,SAAS,CAACsB,YAAY,GAAGnB,EAAE,GAAG;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMqB,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAM5E,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAAC0B,YAAY,cAAc,CAAC;EAC7EC,aAAa,EAAGjE,IAAI,IAAKb,GAAG,CAAC4D,GAAG,CAAC,GAAG7D,UAAU,CAACoD,SAAS,CAAC0B,YAAY,cAAc,EAAEhE,IAAI,CAAC;EAC1FkE,gBAAgB,EAAEA,CAAA,KAAM/E,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAAC0B,YAAY,gBAAgB,CAAC;EACrFG,oBAAoB,EAAG1B,EAAE,IAAKtD,GAAG,CAACyD,IAAI,CAAC,GAAG1D,UAAU,CAACoD,SAAS,CAAC0B,YAAY,iBAAiBvB,EAAE,QAAQ;AACxG,CAAC;;AAED;AACA,OAAO,MAAM2B,aAAa,GAAG;EAC3BjC,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAAC+B,UAAU;AACvD,CAAC;AAED,eAAelF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}