{"ast": null, "code": "// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias en mode développement Django\n  DJANGO_MEDIA_URL: 'http://localhost:8000/media/',\n  // URL pour les images statiques\n  STATIC_URL: 'http://localhost:8000/static/',\n  // Image par défaut pour les livres et ebooks\n  DEFAULT_BOOK_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png',\n  // Image par défaut pour les profils\n  DEFAULT_PROFILE_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png',\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: imagePath => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      console.warn(\"getImageUrl: imagePath est vide ou null\");\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      console.log(`getImageUrl: URL complète détectée: ${imagePath}`);\n      return imagePath;\n    }\n\n    // Si l'image est un chemin statique (pour les images stockées dans /static/)\n    if (imagePath.startsWith('/static/') || imagePath.includes('static_image_url')) {\n      const staticPath = imagePath.replace('/static/', '').replace('static_image_url:', '');\n      const fullStaticUrl = `${config.STATIC_URL}${staticPath}`;\n      console.log(`getImageUrl: URL statique générée: ${fullStaticUrl}`);\n      return fullStaticUrl;\n    }\n\n    // Simplification du traitement des chemins pour les images media\n    let cleanPath = imagePath;\n\n    // Supprimer tout préfixe /media/ existant pour éviter les doublons\n    cleanPath = cleanPath.replace(/^\\/?(media\\/)+/, '');\n\n    // Récupérer le timestamp pour éviter la mise en cache\n    const timestamp = localStorage.getItem('imageTimestamp') || new Date().getTime();\n\n    // Construire l'URL complète avec le timestamp\n    const fullUrl = `${config.DJANGO_MEDIA_URL}${cleanPath}?t=${timestamp}`;\n\n    // Log pour le débogage\n    console.log(`getImageUrl: URL média générée: ${fullUrl} (original: ${imagePath})`);\n    return fullUrl;\n  },\n  // Fonction spécifique pour les images de profil\n  getProfileImageUrl: imagePath => {\n    if (!imagePath) {\n      return config.DEFAULT_PROFILE_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  },\n  // Fonction spécifique pour les images de livres\n  getBookImageUrl: imagePath => {\n    if (!imagePath) {\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Essayer d'abord le chemin statique pour les livres\n    const bookId = imagePath.match(/\\/(\\d+)\\.jpg$/);\n    if (bookId) {\n      const staticPath = `images/livres/book_${bookId[1]}.jpg`;\n      const fullStaticUrl = `${config.STATIC_URL}${staticPath}`;\n      console.log(`getBookImageUrl: URL statique générée: ${fullStaticUrl}`);\n\n      // Vérifier si l'image existe\n      const img = new Image();\n      img.onerror = () => {\n        console.error(`Image statique non trouvée: ${fullStaticUrl}, utilisation du chemin media`);\n      };\n      img.src = fullStaticUrl;\n      return fullStaticUrl;\n    }\n    return config.getImageUrl(imagePath);\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_BASE_URL", "MEDIA_BASE_URL", "DJANGO_MEDIA_URL", "STATIC_URL", "DEFAULT_BOOK_IMAGE", "DEFAULT_PROFILE_IMAGE", "getImageUrl", "imagePath", "console", "warn", "startsWith", "log", "includes", "staticPath", "replace", "fullStaticUrl", "cleanPath", "timestamp", "localStorage", "getItem", "Date", "getTime", "fullUrl", "getProfileImageUrl", "getBookImageUrl", "bookId", "match", "img", "Image", "onerror", "error", "src"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/config.js"], "sourcesContent": ["// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias en mode développement Django\n  DJANGO_MEDIA_URL: 'http://localhost:8000/media/',\n\n  // URL pour les images statiques\n  STATIC_URL: 'http://localhost:8000/static/',\n\n  // Image par défaut pour les livres et ebooks\n  DEFAULT_BOOK_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png',\n\n  // Image par défaut pour les profils\n  DEFAULT_PROFILE_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png',\n\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: (imagePath) => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      console.warn(\"getImageUrl: imagePath est vide ou null\");\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      console.log(`getImageUrl: URL complète détectée: ${imagePath}`);\n      return imagePath;\n    }\n\n    // Si l'image est un chemin statique (pour les images stockées dans /static/)\n    if (imagePath.startsWith('/static/') || imagePath.includes('static_image_url')) {\n      const staticPath = imagePath.replace('/static/', '').replace('static_image_url:', '');\n      const fullStaticUrl = `${config.STATIC_URL}${staticPath}`;\n      console.log(`getImageUrl: URL statique générée: ${fullStaticUrl}`);\n      return fullStaticUrl;\n    }\n\n    // Simplification du traitement des chemins pour les images media\n    let cleanPath = imagePath;\n\n    // Supprimer tout préfixe /media/ existant pour éviter les doublons\n    cleanPath = cleanPath.replace(/^\\/?(media\\/)+/, '');\n\n    // Récupérer le timestamp pour éviter la mise en cache\n    const timestamp = localStorage.getItem('imageTimestamp') || new Date().getTime();\n\n    // Construire l'URL complète avec le timestamp\n    const fullUrl = `${config.DJANGO_MEDIA_URL}${cleanPath}?t=${timestamp}`;\n\n    // Log pour le débogage\n    console.log(`getImageUrl: URL média générée: ${fullUrl} (original: ${imagePath})`);\n\n    return fullUrl;\n  },\n\n  // Fonction spécifique pour les images de profil\n  getProfileImageUrl: (imagePath) => {\n    if (!imagePath) {\n      return config.DEFAULT_PROFILE_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  },\n\n  // Fonction spécifique pour les images de livres\n  getBookImageUrl: (imagePath) => {\n    if (!imagePath) {\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Essayer d'abord le chemin statique pour les livres\n    const bookId = imagePath.match(/\\/(\\d+)\\.jpg$/);\n    if (bookId) {\n      const staticPath = `images/livres/book_${bookId[1]}.jpg`;\n      const fullStaticUrl = `${config.STATIC_URL}${staticPath}`;\n      console.log(`getBookImageUrl: URL statique générée: ${fullStaticUrl}`);\n\n      // Vérifier si l'image existe\n      const img = new Image();\n      img.onerror = () => {\n        console.error(`Image statique non trouvée: ${fullStaticUrl}, utilisation du chemin media`);\n      };\n      img.src = fullStaticUrl;\n\n      return fullStaticUrl;\n    }\n\n    return config.getImageUrl(imagePath);\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,YAAY,EAAE,uBAAuB;EAErC;EACAC,cAAc,EAAE,uBAAuB;EAEvC;EACAC,gBAAgB,EAAE,8BAA8B;EAEhD;EACAC,UAAU,EAAE,+BAA+B;EAE3C;EACAC,kBAAkB,EAAE,wHAAwH;EAE5I;EACAC,qBAAqB,EAAE,uGAAuG;EAE9H;EACAC,WAAW,EAAGC,SAAS,IAAK;IAC1B;IACA,IAAI,CAACA,SAAS,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAC;MACvD,OAAOV,MAAM,CAACK,kBAAkB;IAClC;;IAEA;IACA,IAAIG,SAAS,CAACG,UAAU,CAAC,MAAM,CAAC,EAAE;MAChCF,OAAO,CAACG,GAAG,CAAC,uCAAuCJ,SAAS,EAAE,CAAC;MAC/D,OAAOA,SAAS;IAClB;;IAEA;IACA,IAAIA,SAAS,CAACG,UAAU,CAAC,UAAU,CAAC,IAAIH,SAAS,CAACK,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC9E,MAAMC,UAAU,GAAGN,SAAS,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;MACrF,MAAMC,aAAa,GAAG,GAAGhB,MAAM,CAACI,UAAU,GAAGU,UAAU,EAAE;MACzDL,OAAO,CAACG,GAAG,CAAC,sCAAsCI,aAAa,EAAE,CAAC;MAClE,OAAOA,aAAa;IACtB;;IAEA;IACA,IAAIC,SAAS,GAAGT,SAAS;;IAEzB;IACAS,SAAS,GAAGA,SAAS,CAACF,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;;IAEnD;IACA,MAAMG,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;IAEhF;IACA,MAAMC,OAAO,GAAG,GAAGvB,MAAM,CAACG,gBAAgB,GAAGc,SAAS,MAAMC,SAAS,EAAE;;IAEvE;IACAT,OAAO,CAACG,GAAG,CAAC,mCAAmCW,OAAO,eAAef,SAAS,GAAG,CAAC;IAElF,OAAOe,OAAO;EAChB,CAAC;EAED;EACAC,kBAAkB,EAAGhB,SAAS,IAAK;IACjC,IAAI,CAACA,SAAS,EAAE;MACd,OAAOR,MAAM,CAACM,qBAAqB;IACrC;IACA,OAAON,MAAM,CAACO,WAAW,CAACC,SAAS,CAAC;EACtC,CAAC;EAED;EACAiB,eAAe,EAAGjB,SAAS,IAAK;IAC9B,IAAI,CAACA,SAAS,EAAE;MACd,OAAOR,MAAM,CAACK,kBAAkB;IAClC;;IAEA;IACA,MAAMqB,MAAM,GAAGlB,SAAS,CAACmB,KAAK,CAAC,eAAe,CAAC;IAC/C,IAAID,MAAM,EAAE;MACV,MAAMZ,UAAU,GAAG,sBAAsBY,MAAM,CAAC,CAAC,CAAC,MAAM;MACxD,MAAMV,aAAa,GAAG,GAAGhB,MAAM,CAACI,UAAU,GAAGU,UAAU,EAAE;MACzDL,OAAO,CAACG,GAAG,CAAC,0CAA0CI,aAAa,EAAE,CAAC;;MAEtE;MACA,MAAMY,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,OAAO,GAAG,MAAM;QAClBrB,OAAO,CAACsB,KAAK,CAAC,+BAA+Bf,aAAa,+BAA+B,CAAC;MAC5F,CAAC;MACDY,GAAG,CAACI,GAAG,GAAGhB,aAAa;MAEvB,OAAOA,aAAa;IACtB;IAEA,OAAOhB,MAAM,CAACO,WAAW,CAACC,SAAS,CAAC;EACtC;AACF,CAAC;AAED,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}