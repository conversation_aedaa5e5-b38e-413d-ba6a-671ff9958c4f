{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\BookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookCard = ({\n  book\n}) => {\n  // Fonction pour gérer l'affichage des images\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Débogage des URLs d'images\n  // Ajouter un timestamp pour éviter la mise en cache du navigateur\n  const timestamp = new Date().getTime();\n  const imageUrl = book.image ? `${config.getBookImageUrl(book.image)}?t=${timestamp}` : null;\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n  console.log(`BookCard - Image originale: ${book.image}`);\n  console.log(`BookCard - URL générée: ${imageUrl}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/books/${book.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [book.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: book.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            console.error(`Image originale: ${book.image}`);\n            console.error(`Erreur détaillée:`, e);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `book-card-status ${getStatusColor()}`,\n          children: book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", book.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: book.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [book.quantitie_Dispo, \" / \", book.quantitie_Total, \" disponibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = BookCard;\nexport default BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "BookCard", "book", "getStatusColor", "quantitie_Dispo", "timestamp", "Date", "getTime", "imageUrl", "image", "getBookImageUrl", "console", "log", "id", "titre", "className", "children", "to", "src", "alt", "onError", "e", "error", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "autheur", "category_name", "quantitie_Total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/BookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\n\nconst BookCard = ({ book }) => {\n  // Fonction pour gérer l'affichage des images\n\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Débogage des URLs d'images\n  // Ajouter un timestamp pour éviter la mise en cache du navigateur\n  const timestamp = new Date().getTime();\n  const imageUrl = book.image ? `${config.getBookImageUrl(book.image)}?t=${timestamp}` : null;\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n  console.log(`BookCard - Image originale: ${book.image}`);\n  console.log(`BookCard - URL générée: ${imageUrl}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/books/${book.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {book.image ? (\n            <img\n              src={imageUrl}\n              alt={book.titre}\n              onError={(e) => {\n                console.error(`Erreur de chargement d'image: ${e.target.src}`);\n                console.error(`Image originale: ${book.image}`);\n                console.error(`Erreur détaillée:`, e);\n                e.target.onerror = null;\n                e.target.src = config.DEFAULT_BOOK_IMAGE;\n              }}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className={`book-card-status ${getStatusColor()}`}>\n            {book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{book.titre}</h3>\n          <p className=\"book-card-author\">Par {book.autheur}</p>\n          <p className=\"book-card-category\">{book.category_name}</p>\n          <p className=\"book-card-availability\">\n            {book.quantitie_Dispo} / {book.quantitie_Total} disponibles\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default BookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAC7B;;EAGA;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAID,IAAI,CAACE,eAAe,IAAI,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIF,IAAI,CAACE,eAAe,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC/C,OAAO,OAAO;EAChB,CAAC;;EAED;EACA;EACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACtC,MAAMC,QAAQ,GAAGN,IAAI,CAACO,KAAK,GAAG,GAAGX,MAAM,CAACY,eAAe,CAACR,IAAI,CAACO,KAAK,CAAC,MAAMJ,SAAS,EAAE,GAAG,IAAI;EAC3FM,OAAO,CAACC,GAAG,CAAC,wBAAwBV,IAAI,CAACW,EAAE,YAAYX,IAAI,CAACY,KAAK,EAAE,CAAC;EACpEH,OAAO,CAACC,GAAG,CAAC,+BAA+BV,IAAI,CAACO,KAAK,EAAE,CAAC;EACxDE,OAAO,CAACC,GAAG,CAAC,2BAA2BJ,QAAQ,EAAE,CAAC;EAElD,oBACER,OAAA;IAAKe,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBhB,OAAA,CAACH,IAAI;MAACoB,EAAE,EAAE,UAAUf,IAAI,CAACW,EAAE,EAAG;MAACE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACvDhB,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7Bd,IAAI,CAACO,KAAK,gBACTT,OAAA;UACEkB,GAAG,EAAEV,QAAS;UACdW,GAAG,EAAEjB,IAAI,CAACY,KAAM;UAChBM,OAAO,EAAGC,CAAC,IAAK;YACdV,OAAO,CAACW,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACL,GAAG,EAAE,CAAC;YAC9DP,OAAO,CAACW,KAAK,CAAC,oBAAoBpB,IAAI,CAACO,KAAK,EAAE,CAAC;YAC/CE,OAAO,CAACW,KAAK,CAAC,mBAAmB,EAAED,CAAC,CAAC;YACrCA,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACL,GAAG,GAAGpB,MAAM,CAAC2B,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF7B,OAAA;UAAKe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjChB,OAAA;YAAAgB,QAAA,EAAM;UAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACD7B,OAAA;UAAKe,SAAS,EAAE,oBAAoBZ,cAAc,CAAC,CAAC,EAAG;UAAAa,QAAA,EACpDd,IAAI,CAACE,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG;QAAc;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7B,OAAA;QAAKe,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChB,OAAA;UAAIe,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEd,IAAI,CAACY;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjD7B,OAAA;UAAGe,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACd,IAAI,CAAC4B,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD7B,OAAA;UAAGe,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEd,IAAI,CAAC6B;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1D7B,OAAA;UAAGe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAClCd,IAAI,CAACE,eAAe,EAAC,KAAG,EAACF,IAAI,CAAC8B,eAAe,EAAC,cACjD;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACI,EAAA,GAvDIhC,QAAQ;AAyDd,eAAeA,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}