{"name": "raf", "version": "3.4.1", "description": "requestAnimationFrame polyfill for node and the browser", "main": "index.js", "scripts": {"testling": "browserify test.js | testling", "test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/chris<PERSON><PERSON>on/raf.git"}, "keywords": ["requestAnimationFrame", "polyfill"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "devDependencies": {"testling": "~1.6.1", "browserify": "~4.1.2", "tape": "^4.0.0"}, "dependencies": {"performance-now": "^2.1.0"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}}