## [v0.3.2]
> 2016-05-13

- **Bugfix:** Emit each deprecation warning message only once ([#24])

[v0.3.2]: https://github.com/react-bootstrap/react-prop-types/compare/v0.3.1...v0.3.2
[#24]: https://github.com/react-bootstrap/react-prop-types/pull/24


## [v0.3.1]
> 2016-05-09

- **Bugfix:** Support `isRequired` on `all` ([#22])

[v0.3.1]: https://github.com/react-bootstrap/react-prop-types/compare/v0.3.0...v0.3.1
[#22]: https://github.com/react-bootstrap/react-prop-types/pull/22


v0.3.0 - Thu, 10 Sep 2015 19:23:24 GMT
--------------------------------------

- [3784da4](../../commit/3784da4) [changed] Use es6 rest for parameters of all and singlePropFrom
- [447c243](../../commit/447c243) [fixed] 'isRequireForA11y' undefined/null checking



v0.2.2 - Fri, 21 Aug 2015 19:22:51 GMT
--------------------------------------

- [7d79e8d](../../commit/7d79e8d) [added] 'deprecated' wrapper for property deprecation warning



v0.2.1 - Sun, 12 Jul 2015 16:04:58 GMT
--------------------------------------





v0.2.0 - Sun, 12 Jul 2015 16:01:47 GMT
--------------------------------------


