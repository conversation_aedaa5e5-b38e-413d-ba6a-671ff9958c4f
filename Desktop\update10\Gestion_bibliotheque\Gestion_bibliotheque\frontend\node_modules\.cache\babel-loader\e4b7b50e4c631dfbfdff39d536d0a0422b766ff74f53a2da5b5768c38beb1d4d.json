{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\BookCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport './BookCard.css';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookCard = ({\n  book\n}) => {\n  _s();\n  var _currentUser$profile;\n  // Récupérer les informations de l'utilisateur connecté\n  const {\n    currentUser\n  } = useAuth();\n\n  // Vérifier si l'utilisateur est un administrateur\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL de l'image\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/books/${book.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [book.image || book.static_image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: book.static_image_url ? book.static_image_url : config.getBookImageUrl(book.image),\n          alt: book.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `book-card-status ${getStatusColor()}`,\n          children: book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", book.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: book.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [book.quantitie_Dispo, \" / \", book.quantitie_Total, \" disponibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(BookCard, \"byfRDkJ+t3MKhIwXgwbXYFaZtD0=\", false, function () {\n  return [useAuth];\n});\n_c = BookCard;\nexport default BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");", "map": {"version": 3, "names": ["React", "Link", "useAuth", "config", "jsxDEV", "_jsxDEV", "BookCard", "book", "_s", "_currentUser$profile", "currentUser", "isAdmin", "profile", "user_type", "is_superuser", "getStatusColor", "quantitie_Dispo", "console", "log", "id", "titre", "className", "children", "to", "image", "static_image_url", "src", "getBookImageUrl", "alt", "onError", "e", "error", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "autheur", "category_name", "quantitie_Total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/BookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport './BookCard.css';\nimport config from '../config';\n\nconst BookCard = ({ book }) => {\n  // Récupérer les informations de l'utilisateur connecté\n  const { currentUser } = useAuth();\n\n  // Vérifier si l'utilisateur est un administrateur\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL de l'image\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/books/${book.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {book.image || book.static_image_url ? (\n            <img\n              src={book.static_image_url ? book.static_image_url : config.getBookImageUrl(book.image)}\n              alt={book.titre}\n              onError={(e) => {\n                console.error(`Erreur de chargement d'image: ${e.target.src}`);\n                e.target.onerror = null;\n                e.target.src = config.DEFAULT_BOOK_IMAGE;\n              }}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className={`book-card-status ${getStatusColor()}`}>\n            {book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{book.titre}</h3>\n          <p className=\"book-card-author\">Par {book.autheur}</p>\n          <p className=\"book-card-category\">{book.category_name}</p>\n          {isAdmin && (\n            <p className=\"book-card-availability\">\n              {book.quantitie_Dispo} / {book.quantitie_Total} disponibles\n            </p>\n          )}\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default BookCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC7B;EACA,MAAM;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAMS,OAAO,GAAG,CAAAD,WAAW,aAAXA,WAAW,wBAAAD,oBAAA,GAAXC,WAAW,CAAEE,OAAO,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,SAAS,MAAK,OAAO,KAAIH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,YAAY;;EAExF;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIR,IAAI,CAACS,eAAe,IAAI,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIT,IAAI,CAACS,eAAe,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC/C,OAAO,OAAO;EAChB,CAAC;;EAED;EACAC,OAAO,CAACC,GAAG,CAAC,wBAAwBX,IAAI,CAACY,EAAE,YAAYZ,IAAI,CAACa,KAAK,EAAE,CAAC;EAEpE,oBACEf,OAAA;IAAKgB,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBjB,OAAA,CAACJ,IAAI;MAACsB,EAAE,EAAE,UAAUhB,IAAI,CAACY,EAAE,EAAG;MAACE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACvDjB,OAAA;QAAKgB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7Bf,IAAI,CAACiB,KAAK,IAAIjB,IAAI,CAACkB,gBAAgB,gBAClCpB,OAAA;UACEqB,GAAG,EAAEnB,IAAI,CAACkB,gBAAgB,GAAGlB,IAAI,CAACkB,gBAAgB,GAAGtB,MAAM,CAACwB,eAAe,CAACpB,IAAI,CAACiB,KAAK,CAAE;UACxFI,GAAG,EAAErB,IAAI,CAACa,KAAM;UAChBS,OAAO,EAAGC,CAAC,IAAK;YACdb,OAAO,CAACc,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACN,GAAG,EAAE,CAAC;YAC9DI,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACN,GAAG,GAAGvB,MAAM,CAAC+B,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFjC,OAAA;UAAKgB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjB,OAAA;YAAAiB,QAAA,EAAM;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACDjC,OAAA;UAAKgB,SAAS,EAAE,oBAAoBN,cAAc,CAAC,CAAC,EAAG;UAAAO,QAAA,EACpDf,IAAI,CAACS,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjC,OAAA;QAAKgB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCjB,OAAA;UAAIgB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEf,IAAI,CAACa;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDjC,OAAA;UAAGgB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACf,IAAI,CAACgC,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDjC,OAAA;UAAGgB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEf,IAAI,CAACiC;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzD3B,OAAO,iBACNN,OAAA;UAAGgB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAClCf,IAAI,CAACS,eAAe,EAAC,KAAG,EAACT,IAAI,CAACkC,eAAe,EAAC,cACjD;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9B,EAAA,CArDIF,QAAQ;EAAA,QAEYJ,OAAO;AAAA;AAAAwC,EAAA,GAF3BpC,QAAQ;AAuDd,eAAeA,QAAQ;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}