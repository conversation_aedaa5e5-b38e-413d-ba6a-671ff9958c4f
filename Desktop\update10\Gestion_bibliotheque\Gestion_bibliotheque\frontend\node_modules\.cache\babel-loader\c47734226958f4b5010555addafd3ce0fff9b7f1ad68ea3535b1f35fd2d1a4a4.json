{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Créer une instance axios avec la configuration de base\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true\n});\n\n// Intercepteur pour ajouter le token d'authentification à chaque requête\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Token ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Service pour les emprunts admin\nconst empruntsAdminService = {\n  // Récupérer tous les emprunts (pour les administrateurs)\n  getAllEmprunts: async () => {\n    try {\n      console.log('Tentative de récupération des emprunts admin via le service dédié');\n      const url = `${API_CONFIG.ENDPOINTS.EMPRUNTS}admin/`;\n      console.log('URL complète:', API_CONFIG.BASE_URL + url);\n      const response = await api.get(url);\n      console.log('Réponse du service dédié:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur dans le service dédié:', error);\n      if (error.response) {\n        console.error('Détails de la réponse:', error.response.data);\n        console.error('Statut HTTP:', error.response.status);\n      }\n      throw error;\n    }\n  },\n  // Marquer un emprunt comme retourné\n  retournerLivre: async empruntId => {\n    try {\n      console.log(`Tentative de retour du livre (emprunt ID: ${empruntId})`);\n      const url = `${API_CONFIG.ENDPOINTS.EMPRUNTS}${empruntId}/retourner/`;\n      console.log('URL complète:', API_CONFIG.BASE_URL + url);\n      const response = await api.post(url);\n      console.log('Réponse du service de retour:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors du retour du livre:', error);\n      if (error.response) {\n        console.error('Détails de la réponse:', error.response.data);\n        console.error('Statut HTTP:', error.response.status);\n      }\n      throw error;\n    }\n  }\n};\nexport default empruntsAdminService;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "empruntsAdminService", "getAllEmprunts", "console", "log", "url", "ENDPOINTS", "EMPRUNTS", "response", "get", "data", "status", "retournerLivre", "empruntId", "post"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/services/empruntsAdminService.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Créer une instance axios avec la configuration de base\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true,\n});\n\n// Intercepteur pour ajouter le token d'authentification à chaque requête\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Token ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Service pour les emprunts admin\nconst empruntsAdminService = {\n  // Récupérer tous les emprunts (pour les administrateurs)\n  getAllEmprunts: async () => {\n    try {\n      console.log('Tentative de récupération des emprunts admin via le service dédié');\n      const url = `${API_CONFIG.ENDPOINTS.EMPRUNTS}admin/`;\n      console.log('URL complète:', API_CONFIG.BASE_URL + url);\n      \n      const response = await api.get(url);\n      console.log('Réponse du service dédié:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur dans le service dédié:', error);\n      if (error.response) {\n        console.error('Détails de la réponse:', error.response.data);\n        console.error('Statut HTTP:', error.response.status);\n      }\n      throw error;\n    }\n  },\n  \n  // Marquer un emprunt comme retourné\n  retournerLivre: async (empruntId) => {\n    try {\n      console.log(`Tentative de retour du livre (emprunt ID: ${empruntId})`);\n      const url = `${API_CONFIG.ENDPOINTS.EMPRUNTS}${empruntId}/retourner/`;\n      console.log('URL complète:', API_CONFIG.BASE_URL + url);\n      \n      const response = await api.post(url);\n      console.log('Réponse du service de retour:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors du retour du livre:', error);\n      if (error.response) {\n        console.error('Détails de la réponse:', error.response.data);\n        console.error('Statut HTTP:', error.response.status);\n      }\n      throw error;\n    }\n  }\n};\n\nexport default empruntsAdminService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE;AACnB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAACS,aAAa,GAAG,SAASH,KAAK,EAAE;EACjD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMG,oBAAoB,GAAG;EAC3B;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAChF,MAAMC,GAAG,GAAG,GAAGtB,UAAU,CAACuB,SAAS,CAACC,QAAQ,QAAQ;MACpDJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErB,UAAU,CAACI,QAAQ,GAAGkB,GAAG,CAAC;MAEvD,MAAMG,QAAQ,GAAG,MAAMxB,GAAG,CAACyB,GAAG,CAACJ,GAAG,CAAC;MACnCF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,QAAQ,CAAC;MAClD,OAAOA,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIA,KAAK,CAACU,QAAQ,EAAE;QAClBL,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACU,QAAQ,CAACE,IAAI,CAAC;QAC5DP,OAAO,CAACL,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACU,QAAQ,CAACG,MAAM,CAAC;MACtD;MACA,MAAMb,KAAK;IACb;EACF,CAAC;EAED;EACAc,cAAc,EAAE,MAAOC,SAAS,IAAK;IACnC,IAAI;MACFV,OAAO,CAACC,GAAG,CAAC,6CAA6CS,SAAS,GAAG,CAAC;MACtE,MAAMR,GAAG,GAAG,GAAGtB,UAAU,CAACuB,SAAS,CAACC,QAAQ,GAAGM,SAAS,aAAa;MACrEV,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErB,UAAU,CAACI,QAAQ,GAAGkB,GAAG,CAAC;MAEvD,MAAMG,QAAQ,GAAG,MAAMxB,GAAG,CAAC8B,IAAI,CAACT,GAAG,CAAC;MACpCF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEI,QAAQ,CAAC;MACtD,OAAOA,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAIA,KAAK,CAACU,QAAQ,EAAE;QAClBL,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACU,QAAQ,CAACE,IAAI,CAAC;QAC5DP,OAAO,CAACL,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACU,QAAQ,CAACG,MAAM,CAAC;MACtD;MACA,MAAMb,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}