{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { FaMoon, FaSun } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  darkMode,\n  toggleDarkMode\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    isAuthenticated\n  } = useAuth();\n\n  // Fonction pour déterminer le titre de la page en fonction de l'URL\n  const getPageTitle = () => {\n    const path = location.pathname;\n    if (path === '/') return 'BiblioDesk';\n    if (path === '/books') return 'Livres';\n    if (path.startsWith('/books/')) return 'Détail du livre';\n    if (path === '/ebooks') return 'E-books';\n    if (path.startsWith('/ebooks/')) return 'Détail de l\\'e-book';\n    if (path === '/profile') return 'Profil';\n    if (path === '/login') return 'Connexion';\n    if (path === '/register') return 'Inscription';\n    if (path === '/statistics') return 'Statistiques';\n    return 'BiblioDesk';\n  };\n\n  //header \n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-content-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"app-content-headerText\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        style: {\n          textDecoration: 'none',\n          color: 'inherit'\n        },\n        children: getPageTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"mode-switch\",\n        title: \"Switch Theme\",\n        onClick: toggleDarkMode,\n        children: darkMode ? /*#__PURE__*/_jsxDEV(FaSun, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(FaMoon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 35\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '10px'\n        },\n        children: [location.pathname !== '/login' && /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"app-content-headerButton\",\n            children: \"Connexion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 15\n        }, this), location.pathname !== '/register' && /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"app-content-headerButton\",\n            children: \"Inscription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"svGYYSmKvbPdl7WaH7Xw/GWz+Bk=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "FaMoon", "FaSun", "useAuth", "jsxDEV", "_jsxDEV", "Header", "darkMode", "toggleDarkMode", "_s", "location", "isAuthenticated", "getPageTitle", "path", "pathname", "startsWith", "className", "children", "to", "style", "textDecoration", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "title", "onClick", "gap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Fa<PERSON>oon, FaSun } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst Header = ({ darkMode, toggleDarkMode }) => {\n  const location = useLocation();\n  const { isAuthenticated } = useAuth();\n\n  // Fonction pour déterminer le titre de la page en fonction de l'URL\n  const getPageTitle = () => {\n    const path = location.pathname;\n    \n    if (path === '/') return 'BiblioDesk';\n    if (path === '/books') return 'Livres';\n    if (path.startsWith('/books/')) return 'Détail du livre';\n    if (path === '/ebooks') return 'E-books';\n    if (path.startsWith('/ebooks/')) return 'Détail de l\\'e-book';\n    if (path === '/profile') return 'Profil';\n    if (path === '/login') return 'Connexion';\n    if (path === '/register') return 'Inscription';\n    if (path === '/statistics') return 'Statistiques';\n    \n    return 'BiblioDesk';\n  };\n\n  //header \n  return (\n    <div className=\"app-content-header\">\n    {/* logo de BiblioDesk */}\n      <h1 className=\"app-content-headerText\">\n        <Link to=\"/\" style={{ textDecoration: 'none', color: 'inherit' }}>\n          {getPageTitle()}\n        </Link>\n      </h1>\n      {/* bouton de changement de thème */}\n      <div style={{ display: 'flex', alignItems: 'center' }}>\n        <button className=\"mode-switch\" title=\"Switch Theme\" onClick={toggleDarkMode}>\n          {darkMode ? <FaSun /> : <FaMoon />}\n        </button>\n        \n        {!isAuthenticated && (\n          <div style={{ display: 'flex', gap: '10px' }}>\n            {location.pathname !== '/login' && (\n              <Link to=\"/login\">\n                <button className=\"app-content-headerButton\">Connexion</button>\n              </Link>\n            )}\n            {location.pathname !== '/register' && (\n              <Link to=\"/register\">\n                <button className=\"app-content-headerButton\">Inscription</button>\n              </Link>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAErC;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAGH,QAAQ,CAACI,QAAQ;IAE9B,IAAID,IAAI,KAAK,GAAG,EAAE,OAAO,YAAY;IACrC,IAAIA,IAAI,KAAK,QAAQ,EAAE,OAAO,QAAQ;IACtC,IAAIA,IAAI,CAACE,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,iBAAiB;IACxD,IAAIF,IAAI,KAAK,SAAS,EAAE,OAAO,SAAS;IACxC,IAAIA,IAAI,CAACE,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,qBAAqB;IAC7D,IAAIF,IAAI,KAAK,UAAU,EAAE,OAAO,QAAQ;IACxC,IAAIA,IAAI,KAAK,QAAQ,EAAE,OAAO,WAAW;IACzC,IAAIA,IAAI,KAAK,WAAW,EAAE,OAAO,aAAa;IAC9C,IAAIA,IAAI,KAAK,aAAa,EAAE,OAAO,cAAc;IAEjD,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,oBACER,OAAA;IAAKW,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjCZ,OAAA;MAAIW,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACpCZ,OAAA,CAACN,IAAI;QAACmB,EAAE,EAAC,GAAG;QAACC,KAAK,EAAE;UAAEC,cAAc,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAJ,QAAA,EAC9DL,YAAY,CAAC;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAELpB,OAAA;MAAKc,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAV,QAAA,gBACpDZ,OAAA;QAAQW,SAAS,EAAC,aAAa;QAACY,KAAK,EAAC,cAAc;QAACC,OAAO,EAAErB,cAAe;QAAAS,QAAA,EAC1EV,QAAQ,gBAAGF,OAAA,CAACH,KAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACJ,MAAM;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAER,CAACd,eAAe,iBACfN,OAAA;QAAKc,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAb,QAAA,GAC1CP,QAAQ,CAACI,QAAQ,KAAK,QAAQ,iBAC7BT,OAAA,CAACN,IAAI;UAACmB,EAAE,EAAC,QAAQ;UAAAD,QAAA,eACfZ,OAAA;YAAQW,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACP,EACAf,QAAQ,CAACI,QAAQ,KAAK,WAAW,iBAChCT,OAAA,CAACN,IAAI;UAACmB,EAAE,EAAC,WAAW;UAAAD,QAAA,eAClBZ,OAAA;YAAQW,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CArDIH,MAAM;EAAA,QACON,WAAW,EACAG,OAAO;AAAA;AAAA4B,EAAA,GAF/BzB,MAAM;AAuDZ,eAAeA,MAAM;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}