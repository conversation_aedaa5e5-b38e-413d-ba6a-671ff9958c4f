{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projetMAI\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useAuth } from './context/AuthContext';\nimport { AlertProvider, useAlert } from './context/AlertContext';\n\n// Pages\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Books from './pages/Books';\nimport BookDetail from './pages/BookDetail';\nimport Ebooks from './pages/Ebooks';\nimport EbookDetail from './pages/EbookDetail';\nimport Profile from './pages/Profile';\nimport NotFound from './pages/NotFound';\n\n// Components\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Loading from './components/Loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const {\n    isAuthenticated,\n    loading,\n    authError\n  } = useAuth();\n  const {\n    showError\n  } = useAlert();\n  const [darkMode, setDarkMode] = useState(true);\n  useEffect(() => {\n    const savedMode = localStorage.getItem('darkMode');\n    if (savedMode !== null) {\n      setDarkMode(savedMode === 'true');\n    }\n  }, []);\n  useEffect(() => {\n    if (authError) {\n      showError(authError);\n    }\n  }, [authError, showError]);\n  const toggleDarkMode = () => {\n    const newMode = !darkMode;\n    setDarkMode(newMode);\n    localStorage.setItem('darkMode', newMode.toString());\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      fullScreen: true,\n      message: \"Chargement de l'application...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Protected route component\n  const ProtectedRoute = ({\n    children\n  }) => {\n    if (!isAuthenticated) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 14\n      }, this);\n    }\n    return children;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `app-container ${darkMode ? '' : 'light'}`,\n    children: [isAuthenticated && /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app-content\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        darkMode: darkMode,\n        toggleDarkMode: toggleDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/books\",\n          element: /*#__PURE__*/_jsxDEV(Books, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/books/:id\",\n          element: /*#__PURE__*/_jsxDEV(BookDetail, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/ebooks\",\n          element: /*#__PURE__*/_jsxDEV(Ebooks, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/ebooks/:id\",\n          element: /*#__PURE__*/_jsxDEV(EbookDetail, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/profile\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n\n// Composant principal qui enveloppe l'application avec les fournisseurs de contexte\n_s(AppContent, \"OM7idtMKE7qW2IYD9egvbfqCs+M=\", false, function () {\n  return [useAuth, useAlert];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AlertProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Routes", "Route", "Navigate", "useAuth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useAlert", "Home", "<PERSON><PERSON>", "Register", "Books", "BookDetail", "Ebooks", "EbookDetail", "Profile", "NotFound", "Sidebar", "Header", "Loading", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "isAuthenticated", "loading", "authError", "showError", "darkMode", "setDarkMode", "savedMode", "localStorage", "getItem", "toggleDarkMode", "newMode", "setItem", "toString", "fullScreen", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ProtectedRoute", "children", "to", "className", "path", "element", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projetMAI/Gestion_bibliotheque/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useAuth } from './context/AuthContext';\nimport { AlertProvider, useAlert } from './context/AlertContext';\n\n// Pages\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Books from './pages/Books';\nimport BookDetail from './pages/BookDetail';\nimport Ebooks from './pages/Ebooks';\nimport EbookDetail from './pages/EbookDetail';\nimport Profile from './pages/Profile';\nimport NotFound from './pages/NotFound';\n\n// Components\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Loading from './components/Loading';\n\nfunction AppContent() {\n  const { isAuthenticated, loading, authError } = useAuth();\n  const { showError } = useAlert();\n  const [darkMode, setDarkMode] = useState(true);\n\n  useEffect(() => {\n    const savedMode = localStorage.getItem('darkMode');\n    if (savedMode !== null) {\n      setDarkMode(savedMode === 'true');\n    }\n  }, []);\n\n  useEffect(() => {\n    if (authError) {\n      showError(authError);\n    }\n  }, [authError, showError]);\n\n  const toggleDarkMode = () => {\n    const newMode = !darkMode;\n    setDarkMode(newMode);\n    localStorage.setItem('darkMode', newMode.toString());\n  };\n\n  if (loading) {\n    return <Loading fullScreen message=\"Chargement de l'application...\" />;\n  }\n\n  // Protected route component\n  const ProtectedRoute = ({ children }) => {\n    if (!isAuthenticated) {\n      return <Navigate to=\"/login\" />;\n    }\n    return children;\n  };\n\n  return (\n    <div className={`app-container ${darkMode ? '' : 'light'}`}>\n      {isAuthenticated && <Sidebar />}\n      <div className=\"app-content\">\n        <Header darkMode={darkMode} toggleDarkMode={toggleDarkMode} />\n\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/register\" element={<Register />} />\n          <Route path=\"/books\" element={<Books />} />\n          <Route path=\"/books/:id\" element={<BookDetail />} />\n          <Route path=\"/ebooks\" element={<Ebooks />} />\n          <Route path=\"/ebooks/:id\" element={<EbookDetail />} />\n          <Route\n            path=\"/profile\"\n            element={\n              <ProtectedRoute>\n                <Profile />\n              </ProtectedRoute>\n            }\n          />\n          <Route path=\"*\" element={<NotFound />} />\n        </Routes>\n      </div>\n    </div>\n  );\n}\n\n// Composant principal qui enveloppe l'application avec les fournisseurs de contexte\nfunction App() {\n  return (\n    <AlertProvider>\n      <AppContent />\n    </AlertProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,aAAa,EAAEC,QAAQ,QAAQ,wBAAwB;;AAEhE;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;;AAEvC;AACA,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAU,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACzD,MAAM;IAAEsB;EAAU,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAChC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd,MAAM6B,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAClD,IAAIF,SAAS,KAAK,IAAI,EAAE;MACtBD,WAAW,CAACC,SAAS,KAAK,MAAM,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN7B,SAAS,CAAC,MAAM;IACd,IAAIyB,SAAS,EAAE;MACbC,SAAS,CAACD,SAAS,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,SAAS,EAAEC,SAAS,CAAC,CAAC;EAE1B,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAG,CAACN,QAAQ;IACzBC,WAAW,CAACK,OAAO,CAAC;IACpBH,YAAY,CAACI,OAAO,CAAC,UAAU,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;EACtD,CAAC;EAED,IAAIX,OAAO,EAAE;IACX,oBAAOJ,OAAA,CAACF,OAAO;MAACkB,UAAU;MAACC,OAAO,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxE;;EAEA;EACA,MAAMC,cAAc,GAAGA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACvC,IAAI,CAACpB,eAAe,EAAE;MACpB,oBAAOH,OAAA,CAACjB,QAAQ;QAACyC,EAAE,EAAC;MAAQ;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjC;IACA,OAAOE,QAAQ;EACjB,CAAC;EAED,oBACEvB,OAAA;IAAKyB,SAAS,EAAE,iBAAiBlB,QAAQ,GAAG,EAAE,GAAG,OAAO,EAAG;IAAAgB,QAAA,GACxDpB,eAAe,iBAAIH,OAAA,CAACJ,OAAO;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/BrB,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAF,QAAA,gBAC1BvB,OAAA,CAACH,MAAM;QAACU,QAAQ,EAAEA,QAAS;QAACK,cAAc,EAAEA;MAAe;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE9DrB,OAAA,CAACnB,MAAM;QAAA0C,QAAA,gBACLvB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE3B,OAAA,CAACb,IAAI;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE3B,OAAA,CAACZ,KAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,WAAW;UAACC,OAAO,eAAE3B,OAAA,CAACX,QAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE3B,OAAA,CAACV,KAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,YAAY;UAACC,OAAO,eAAE3B,OAAA,CAACT,UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,SAAS;UAACC,OAAO,eAAE3B,OAAA,CAACR,MAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,aAAa;UAACC,OAAO,eAAE3B,OAAA,CAACP,WAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDrB,OAAA,CAAClB,KAAK;UACJ4C,IAAI,EAAC,UAAU;UACfC,OAAO,eACL3B,OAAA,CAACsB,cAAc;YAAAC,QAAA,eACbvB,OAAA,CAACN,OAAO;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFrB,OAAA,CAAClB,KAAK;UAAC4C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE3B,OAAA,CAACL,QAAQ;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAnB,EAAA,CAjESD,UAAU;EAAA,QAC+BjB,OAAO,EACjCE,QAAQ;AAAA;AAAA0C,EAAA,GAFvB3B,UAAU;AAkEnB,SAAS4B,GAAGA,CAAA,EAAG;EACb,oBACE7B,OAAA,CAACf,aAAa;IAAAsC,QAAA,eACZvB,OAAA,CAACC,UAAU;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACS,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}