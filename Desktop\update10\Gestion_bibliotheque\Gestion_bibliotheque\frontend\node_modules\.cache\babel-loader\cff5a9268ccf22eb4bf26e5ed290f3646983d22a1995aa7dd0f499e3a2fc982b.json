{"ast": null, "code": "// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: imagePath => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      return imagePath;\n    }\n\n    // Nettoyer le chemin de l'image\n    let cleanPath = imagePath;\n\n    // Supprimer tous les préfixes /media/ ou media/ pour éviter les doublons\n    while (cleanPath.includes('/media/media/') || cleanPath.includes('media/media/')) {\n      cleanPath = cleanPath.replace('/media/media/', '/media/');\n      cleanPath = cleanPath.replace('media/media/', 'media/');\n    }\n\n    // Assurer que le chemin commence par /media/\n    if (!cleanPath.startsWith('/media/') && !cleanPath.startsWith('media/')) {\n      cleanPath = `/media/${cleanPath}`;\n    }\n\n    // Assurer que le chemin commence par /\n    if (!cleanPath.startsWith('/')) {\n      cleanPath = `/${cleanPath}`;\n    }\n\n    // Construire l'URL complète\n    return `${config.MEDIA_BASE_URL}${cleanPath}`;\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_BASE_URL", "MEDIA_BASE_URL", "getImageUrl", "imagePath", "startsWith", "cleanPath", "includes", "replace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/config.js"], "sourcesContent": ["// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: (imagePath) => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      return imagePath;\n    }\n\n    // Nettoyer le chemin de l'image\n    let cleanPath = imagePath;\n\n    // Supprimer tous les préfixes /media/ ou media/ pour éviter les doublons\n    while (cleanPath.includes('/media/media/') || cleanPath.includes('media/media/')) {\n      cleanPath = cleanPath.replace('/media/media/', '/media/');\n      cleanPath = cleanPath.replace('media/media/', 'media/');\n    }\n\n    // Assurer que le chemin commence par /media/\n    if (!cleanPath.startsWith('/media/') && !cleanPath.startsWith('media/')) {\n      cleanPath = `/media/${cleanPath}`;\n    }\n\n    // Assurer que le chemin commence par /\n    if (!cleanPath.startsWith('/')) {\n      cleanPath = `/${cleanPath}`;\n    }\n\n    // Construire l'URL complète\n    return `${config.MEDIA_BASE_URL}${cleanPath}`;\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,YAAY,EAAE,uBAAuB;EAErC;EACAC,cAAc,EAAE,uBAAuB;EAEvC;EACAC,WAAW,EAAGC,SAAS,IAAK;IAC1B;IACA,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,wHAAwH;IACjI;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAChC,OAAOD,SAAS;IAClB;;IAEA;IACA,IAAIE,SAAS,GAAGF,SAAS;;IAEzB;IACA,OAAOE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAID,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;MAChFD,SAAS,GAAGA,SAAS,CAACE,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC;MACzDF,SAAS,GAAGA,SAAS,CAACE,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC;IACzD;;IAEA;IACA,IAAI,CAACF,SAAS,CAACD,UAAU,CAAC,SAAS,CAAC,IAAI,CAACC,SAAS,CAACD,UAAU,CAAC,QAAQ,CAAC,EAAE;MACvEC,SAAS,GAAG,UAAUA,SAAS,EAAE;IACnC;;IAEA;IACA,IAAI,CAACA,SAAS,CAACD,UAAU,CAAC,GAAG,CAAC,EAAE;MAC9BC,SAAS,GAAG,IAAIA,SAAS,EAAE;IAC7B;;IAEA;IACA,OAAO,GAAGN,MAAM,CAACE,cAAc,GAAGI,SAAS,EAAE;EAC/C;AACF,CAAC;AAED,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}