{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\EditBook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditBook = () => {\n  _s();\n  var _currentUser$profile;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    quantitie_Dispo: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    url: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    if (!isAdmin) {\n      navigate('/books');\n      return;\n    }\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les catégories\n        const categoriesResponse = await livresAPI.getCategories();\n        setCategories(categoriesResponse.data);\n\n        // Récupérer les données du livre\n        const bookResponse = await livresAPI.getById(id);\n        const book = bookResponse.data;\n\n        // Formater la date pour l'input date\n        const datePublication = book.date_publication ? new Date(book.date_publication).toISOString().split('T')[0] : '';\n        setFormData({\n          titre: book.titre || '',\n          autheur: book.autheur || '',\n          category: book.category || '',\n          price: book.price || '',\n          quantitie_Total: book.quantitie_Total || '',\n          quantitie_Dispo: book.quantitie_Dispo || '',\n          desc: book.desc || '',\n          date_publication: datePublication,\n          isbn: book.isbn || '',\n          url: book.url || '',\n          image: null\n        });\n\n        // Définir l'aperçu de l'image si elle existe\n        if (book.image_url) {\n          setImagePreview(book.image_url);\n        } else if (book.image) {\n          setImagePreview(`http://localhost:8000/media/book_${id}.jpg`);\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      files\n    } = e.target;\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n\n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n\n      // Envoyer les données au serveur\n      await livresAPI.update(id, data);\n\n      // Rediriger vers la page de détail du livre\n      navigate(`/books/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification du livre:', err);\n      setError('Erreur lors de la modification du livre. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      message: \"Chargement des donn\\xE9es...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-book-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"add-book-title\",\n      children: \"Modifier un livre\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-book-card\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"add-book-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"titre\",\n              children: [\"Titre \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"titre\",\n              name: \"titre\",\n              value: formData.titre,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"autheur\",\n              children: [\"Auteur \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"autheur\",\n              name: \"autheur\",\n              value: formData.autheur,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: [\"Cat\\xE9gorie \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"S\\xE9lectionner une cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isbn\",\n              children: [\"ISBN \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"isbn\",\n              name: \"isbn\",\n              value: formData.isbn,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              children: [\"Prix \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"price\",\n              name: \"price\",\n              value: formData.price,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"date_publication\",\n              children: [\"Date de publication \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 69\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"date_publication\",\n              name: \"date_publication\",\n              value: formData.date_publication,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantitie_Total\",\n              children: [\"Quantit\\xE9 totale \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 64\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"quantitie_Total\",\n              name: \"quantitie_Total\",\n              value: formData.quantitie_Total,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantitie_Dispo\",\n              children: [\"Quantit\\xE9 disponible \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 68\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"quantitie_Dispo\",\n              name: \"quantitie_Dispo\",\n              value: formData.quantitie_Dispo,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"url\",\n            children: \"URL (optionnel)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            id: \"url\",\n            name: \"url\",\n            value: formData.url,\n            onChange: handleChange,\n            placeholder: \"https://example.com\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"desc\",\n            children: [\"Description \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 47\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"desc\",\n            name: \"desc\",\n            value: formData.desc,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: \"Image (optionnel)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            name: \"image\",\n            onChange: handleChange,\n            accept: \"image/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Aper\\xE7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: `/books/${id}`,\n            className: \"cancel-button\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            children: \"Enregistrer les modifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(EditBook, \"plfNkfsjPnYx6VpfBBZYY10hoDg=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = EditBook;\nexport default EditBook;\nvar _c;\n$RefreshReg$(_c, \"EditBook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "livresAPI", "useAuth", "Loading", "jsxDEV", "_jsxDEV", "EditBook", "_s", "_currentUser$profile", "id", "navigate", "isAuthenticated", "currentUser", "loading", "setLoading", "error", "setError", "categories", "setCategories", "imagePreview", "setImagePreview", "formData", "setFormData", "titre", "autheur", "category", "price", "quantitie_Total", "quantitie_Dispo", "desc", "date_publication", "isbn", "url", "image", "isAdmin", "profile", "user_type", "is_superuser", "fetchData", "categoriesResponse", "getCategories", "data", "bookResponse", "getById", "book", "datePublication", "Date", "toISOString", "split", "image_url", "err", "console", "handleChange", "e", "name", "value", "type", "files", "target", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "update", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "onSubmit", "htmlFor", "onChange", "required", "map", "placeholder", "accept", "src", "alt", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/EditBook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\n\nconst EditBook = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { isAuthenticated, currentUser } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    quantitie_Dispo: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    url: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    if (!isAdmin) {\n      navigate('/books');\n      return;\n    }\n\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        \n        // Récupérer les catégories\n        const categoriesResponse = await livresAPI.getCategories();\n        setCategories(categoriesResponse.data);\n        \n        // Récupérer les données du livre\n        const bookResponse = await livresAPI.getById(id);\n        const book = bookResponse.data;\n        \n        // Formater la date pour l'input date\n        const datePublication = book.date_publication \n          ? new Date(book.date_publication).toISOString().split('T')[0]\n          : '';\n        \n        setFormData({\n          titre: book.titre || '',\n          autheur: book.autheur || '',\n          category: book.category || '',\n          price: book.price || '',\n          quantitie_Total: book.quantitie_Total || '',\n          quantitie_Dispo: book.quantitie_Dispo || '',\n          desc: book.desc || '',\n          date_publication: datePublication,\n          isbn: book.isbn || '',\n          url: book.url || '',\n          image: null\n        });\n        \n        // Définir l'aperçu de l'image si elle existe\n        if (book.image_url) {\n          setImagePreview(book.image_url);\n        } else if (book.image) {\n          setImagePreview(`http://localhost:8000/media/book_${id}.jpg`);\n        }\n        \n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n\n  const handleChange = (e) => {\n    const { name, value, type, files } = e.target;\n    \n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n      \n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      setLoading(true);\n      \n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n      \n      // Envoyer les données au serveur\n      await livresAPI.update(id, data);\n      \n      // Rediriger vers la page de détail du livre\n      navigate(`/books/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification du livre:', err);\n      setError('Erreur lors de la modification du livre. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <Loading message=\"Chargement des données...\" />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">{error}</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"add-book-container\">\n      <h1 className=\"add-book-title\">Modifier un livre</h1>\n      \n      <div className=\"add-book-card\">\n        <form className=\"add-book-form\" onSubmit={handleSubmit}>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"titre\">Titre <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"titre\"\n                name=\"titre\"\n                value={formData.titre}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"autheur\">Auteur <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"autheur\"\n                name=\"autheur\"\n                value={formData.autheur}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n          \n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"category\">Catégorie <span className=\"required\">*</span></label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"\">Sélectionner une catégorie</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"isbn\">ISBN <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"isbn\"\n                name=\"isbn\"\n                value={formData.isbn}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n          \n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"price\">Prix <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"date_publication\">Date de publication <span className=\"required\">*</span></label>\n              <input\n                type=\"date\"\n                id=\"date_publication\"\n                name=\"date_publication\"\n                value={formData.date_publication}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n          \n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"quantitie_Total\">Quantité totale <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"quantitie_Total\"\n                name=\"quantitie_Total\"\n                value={formData.quantitie_Total}\n                onChange={handleChange}\n                required\n              />\n            </div>\n            \n            <div className=\"form-group\">\n              <label htmlFor=\"quantitie_Dispo\">Quantité disponible <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"quantitie_Dispo\"\n                name=\"quantitie_Dispo\"\n                value={formData.quantitie_Dispo}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n          \n          <div className=\"form-group\">\n            <label htmlFor=\"url\">URL (optionnel)</label>\n            <input\n              type=\"url\"\n              id=\"url\"\n              name=\"url\"\n              value={formData.url}\n              onChange={handleChange}\n              placeholder=\"https://example.com\"\n            />\n          </div>\n          \n          <div className=\"form-group\">\n            <label htmlFor=\"desc\">Description <span className=\"required\">*</span></label>\n            <textarea\n              id=\"desc\"\n              name=\"desc\"\n              value={formData.desc}\n              onChange={handleChange}\n              required\n            ></textarea>\n          </div>\n          \n          <div className=\"form-group\">\n            <label htmlFor=\"image\">Image (optionnel)</label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              name=\"image\"\n              onChange={handleChange}\n              accept=\"image/*\"\n            />\n            {imagePreview && (\n              <div className=\"image-preview\">\n                <img src={imagePreview} alt=\"Aperçu\" />\n              </div>\n            )}\n          </div>\n          \n          <div className=\"form-actions\">\n            <Link to={`/books/${id}`} className=\"cancel-button\">Annuler</Link>\n            <button type=\"submit\" className=\"submit-button\">Enregistrer les modifications</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default EditBook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,eAAe;IAAEC;EAAY,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAAtB,WAAW,aAAXA,WAAW,wBAAAJ,oBAAA,GAAXI,WAAW,CAAEuB,OAAO,cAAA3B,oBAAA,uBAApBA,oBAAA,CAAsB4B,SAAS,MAAK,OAAO,KAAIxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,YAAY;EAExFxC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACc,eAAe,EAAE;MACpBD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI,CAACwB,OAAO,EAAE;MACZxB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,MAAM4B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFxB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMyB,kBAAkB,GAAG,MAAMtC,SAAS,CAACuC,aAAa,CAAC,CAAC;QAC1DtB,aAAa,CAACqB,kBAAkB,CAACE,IAAI,CAAC;;QAEtC;QACA,MAAMC,YAAY,GAAG,MAAMzC,SAAS,CAAC0C,OAAO,CAAClC,EAAE,CAAC;QAChD,MAAMmC,IAAI,GAAGF,YAAY,CAACD,IAAI;;QAE9B;QACA,MAAMI,eAAe,GAAGD,IAAI,CAACd,gBAAgB,GACzC,IAAIgB,IAAI,CAACF,IAAI,CAACd,gBAAgB,CAAC,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3D,EAAE;QAEN1B,WAAW,CAAC;UACVC,KAAK,EAAEqB,IAAI,CAACrB,KAAK,IAAI,EAAE;UACvBC,OAAO,EAAEoB,IAAI,CAACpB,OAAO,IAAI,EAAE;UAC3BC,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ,IAAI,EAAE;UAC7BC,KAAK,EAAEkB,IAAI,CAAClB,KAAK,IAAI,EAAE;UACvBC,eAAe,EAAEiB,IAAI,CAACjB,eAAe,IAAI,EAAE;UAC3CC,eAAe,EAAEgB,IAAI,CAAChB,eAAe,IAAI,EAAE;UAC3CC,IAAI,EAAEe,IAAI,CAACf,IAAI,IAAI,EAAE;UACrBC,gBAAgB,EAAEe,eAAe;UACjCd,IAAI,EAAEa,IAAI,CAACb,IAAI,IAAI,EAAE;UACrBC,GAAG,EAAEY,IAAI,CAACZ,GAAG,IAAI,EAAE;UACnBC,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACA,IAAIW,IAAI,CAACK,SAAS,EAAE;UAClB7B,eAAe,CAACwB,IAAI,CAACK,SAAS,CAAC;QACjC,CAAC,MAAM,IAAIL,IAAI,CAACX,KAAK,EAAE;UACrBb,eAAe,CAAC,oCAAoCX,EAAE,MAAM,CAAC;QAC/D;QAEAK,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOoC,GAAG,EAAE;QACZC,OAAO,CAACpC,KAAK,CAAC,wCAAwC,EAAEmC,GAAG,CAAC;QAC5DlC,QAAQ,CAAC,sEAAsE,CAAC;QAChFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,EAAE,EAAEE,eAAe,EAAEuB,OAAO,EAAExB,QAAQ,CAAC,CAAC;EAE5C,MAAM0C,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAE7C,IAAIF,IAAI,KAAK,MAAM,EAAE;MACnBlC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACiC,IAAI,GAAGG,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACA,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACZ,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvBzC,eAAe,CAACuC,MAAM,CAACG,MAAM,CAAC;QAChC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACLnC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACiC,IAAI,GAAGC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI;MACFnD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM2B,IAAI,GAAG,IAAIyB,QAAQ,CAAC,CAAC;MAC3BC,MAAM,CAACC,IAAI,CAAC/C,QAAQ,CAAC,CAACgD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIjD,QAAQ,CAACiD,GAAG,CAAC,KAAK,IAAI,EAAE;UAC1B7B,IAAI,CAAC8B,MAAM,CAACD,GAAG,EAAEjD,QAAQ,CAACiD,GAAG,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMrE,SAAS,CAACuE,MAAM,CAAC/D,EAAE,EAAEgC,IAAI,CAAC;;MAEhC;MACA/B,QAAQ,CAAC,UAAUD,EAAE,EAAE,CAAC;IAC1B,CAAC,CAAC,OAAOyC,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,0CAA0C,EAAEmC,GAAG,CAAC;MAC9DlC,QAAQ,CAAC,8DAA8D,CAAC;MACxEF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACF,OAAO;MAACsE,OAAO,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxD;EAEA,IAAI9D,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKyE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1E,OAAA;QAAGyE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEhE;MAAK;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxCxE,OAAA;QACEyE,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACExE,OAAA;IAAKyE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC1E,OAAA;MAAIyE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAiB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErDxE,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1E,OAAA;QAAMyE,SAAS,EAAC,eAAe;QAACM,QAAQ,EAAEpB,YAAa;QAAAe,QAAA,gBACrD1E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,QAAM,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxExE,OAAA;cACEmD,IAAI,EAAC,MAAM;cACX/C,EAAE,EAAC,OAAO;cACV6C,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElC,QAAQ,CAACE,KAAM;cACtB+D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxE,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,SAAS;cAAAN,QAAA,GAAC,SAAO,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3ExE,OAAA;cACEmD,IAAI,EAAC,MAAM;cACX/C,EAAE,EAAC,SAAS;cACZ6C,IAAI,EAAC,SAAS;cACdC,KAAK,EAAElC,QAAQ,CAACG,OAAQ;cACxB8D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,UAAU;cAAAN,QAAA,GAAC,eAAU,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/ExE,OAAA;cACEI,EAAE,EAAC,UAAU;cACb6C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAElC,QAAQ,CAACI,QAAS;cACzB6D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;cAAAR,QAAA,gBAER1E,OAAA;gBAAQkD,KAAK,EAAC,EAAE;gBAAAwB,QAAA,EAAC;cAA0B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnD5D,UAAU,CAACuE,GAAG,CAAC/D,QAAQ,iBACtBpB,OAAA;gBAA0BkD,KAAK,EAAE9B,QAAQ,CAAChB,EAAG;gBAAAsE,QAAA,EAC1CtD,QAAQ,CAAC6B;cAAI,GADH7B,QAAQ,CAAChB,EAAE;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxE,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtExE,OAAA;cACEmD,IAAI,EAAC,MAAM;cACX/C,EAAE,EAAC,MAAM;cACT6C,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElC,QAAQ,CAACU,IAAK;cACrBuD,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,OAAK,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvExE,OAAA;cACEmD,IAAI,EAAC,QAAQ;cACb/C,EAAE,EAAC,OAAO;cACV6C,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElC,QAAQ,CAACK,KAAM;cACtB4D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxE,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,kBAAkB;cAAAN,QAAA,GAAC,sBAAoB,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjGxE,OAAA;cACEmD,IAAI,EAAC,MAAM;cACX/C,EAAE,EAAC,kBAAkB;cACrB6C,IAAI,EAAC,kBAAkB;cACvBC,KAAK,EAAElC,QAAQ,CAACS,gBAAiB;cACjCwD,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,iBAAiB;cAAAN,QAAA,GAAC,qBAAgB,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5FxE,OAAA;cACEmD,IAAI,EAAC,QAAQ;cACb/C,EAAE,EAAC,iBAAiB;cACpB6C,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAElC,QAAQ,CAACM,eAAgB;cAChC2D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxE,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,iBAAiB;cAAAN,QAAA,GAAC,yBAAoB,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChGxE,OAAA;cACEmD,IAAI,EAAC,QAAQ;cACb/C,EAAE,EAAC,iBAAiB;cACpB6C,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAElC,QAAQ,CAACO,eAAgB;cAChC0D,QAAQ,EAAElC,YAAa;cACvBmC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1E,OAAA;YAAOgF,OAAO,EAAC,KAAK;YAAAN,QAAA,EAAC;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxE,OAAA;YACEmD,IAAI,EAAC,KAAK;YACV/C,EAAE,EAAC,KAAK;YACR6C,IAAI,EAAC,KAAK;YACVC,KAAK,EAAElC,QAAQ,CAACW,GAAI;YACpBsD,QAAQ,EAAElC,YAAa;YACvBqC,WAAW,EAAC;UAAqB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1E,OAAA;YAAOgF,OAAO,EAAC,MAAM;YAAAN,QAAA,GAAC,cAAY,eAAA1E,OAAA;cAAMyE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ExE,OAAA;YACEI,EAAE,EAAC,MAAM;YACT6C,IAAI,EAAC,MAAM;YACXC,KAAK,EAAElC,QAAQ,CAACQ,IAAK;YACrByD,QAAQ,EAAElC,YAAa;YACvBmC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1E,OAAA;YAAOgF,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDxE,OAAA;YACEmD,IAAI,EAAC,MAAM;YACX/C,EAAE,EAAC,OAAO;YACV6C,IAAI,EAAC,OAAO;YACZgC,QAAQ,EAAElC,YAAa;YACvBsC,MAAM,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EACD1D,YAAY,iBACXd,OAAA;YAAKyE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1E,OAAA;cAAKsF,GAAG,EAAExE,YAAa;cAACyE,GAAG,EAAC;YAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxE,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1E,OAAA,CAACL,IAAI;YAAC6F,EAAE,EAAE,UAAUpF,EAAE,EAAG;YAACqE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClExE,OAAA;YAAQmD,IAAI,EAAC,QAAQ;YAACsB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA6B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA9TID,QAAQ;EAAA,QACGR,SAAS,EACPC,WAAW,EACaG,OAAO;AAAA;AAAA4F,EAAA,GAH5CxF,QAAQ;AAgUd,eAAeA,QAAQ;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}