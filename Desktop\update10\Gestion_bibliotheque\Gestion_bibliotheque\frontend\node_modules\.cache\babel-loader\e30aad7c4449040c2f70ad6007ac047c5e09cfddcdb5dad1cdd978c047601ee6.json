{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\EbookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookCard = ({\n  ebook\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/ebooks/${ebook.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [ebook.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: ebook.image.startsWith('http') ? ebook.image : `http://localhost:8000${ebook.image}`,\n          alt: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-status green\",\n          children: ebook.format.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", ebook.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: ebook.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [\"Format: \", ebook.format.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = EbookCard;\nexport default EbookCard;\nvar _c;\n$RefreshReg$(_c, \"EbookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "EbookCard", "ebook", "className", "children", "to", "id", "image", "src", "startsWith", "alt", "titre", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "format", "toUpperCase", "autheur", "category_name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/EbookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\n\nconst EbookCard = ({ ebook }) => {\n  return (\n    <div className=\"book-card\">\n      <Link to={`/ebooks/${ebook.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {ebook.image ? (\n            <img\n              src={ebook.image.startsWith('http') ? ebook.image : `http://localhost:8000${ebook.image}`}\n              alt={ebook.titre}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className=\"book-card-status green\">\n            {ebook.format.toUpperCase()}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{ebook.titre}</h3>\n          <p className=\"book-card-author\">Par {ebook.autheur}</p>\n          <p className=\"book-card-category\">{ebook.category_name}</p>\n          <p className=\"book-card-availability\">\n            Format: {ebook.format.toUpperCase()}\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default EbookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB,CAAC,CAAC;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B,oBACEF,OAAA;IAAKG,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBJ,OAAA,CAACH,IAAI;MAACQ,EAAE,EAAE,WAAWH,KAAK,CAACI,EAAE,EAAG;MAACH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACzDJ,OAAA;QAAKG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7BF,KAAK,CAACK,KAAK,gBACVP,OAAA;UACEQ,GAAG,EAAEN,KAAK,CAACK,KAAK,CAACE,UAAU,CAAC,MAAM,CAAC,GAAGP,KAAK,CAACK,KAAK,GAAG,wBAAwBL,KAAK,CAACK,KAAK,EAAG;UAC1FG,GAAG,EAAER,KAAK,CAACS;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,gBAEFf,OAAA;UAAKG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCJ,OAAA;YAAAI,QAAA,EAAM;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACDf,OAAA;UAAKG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCF,KAAK,CAACc,MAAM,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNf,OAAA;QAAKG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCJ,OAAA;UAAIG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEF,KAAK,CAACS;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDf,OAAA;UAAGG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACF,KAAK,CAACgB,OAAO;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDf,OAAA;UAAGG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEF,KAAK,CAACiB;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3Df,OAAA;UAAGG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,UAC5B,EAACF,KAAK,CAACc,MAAM,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GA9BInB,SAAS;AAgCf,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}