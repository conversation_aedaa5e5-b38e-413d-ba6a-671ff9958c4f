{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\BookCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport './BookCard.css';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookCard = ({\n  book\n}) => {\n  _s();\n  var _currentUser$profile;\n  // Récupérer les informations de l'utilisateur connecté\n  const {\n    currentUser\n  } = useAuth();\n\n  // Vérifier si l'utilisateur est un administrateur\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Log des informations du livre\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL correcte\n  let imageUrl;\n\n  // Essayer d'abord l'URL statique directe si elle existe\n  if (book.static_image_url) {\n    imageUrl = config.STATIC_URL + book.static_image_url;\n    console.log(`BookCard - Utilisation de l'URL statique: ${imageUrl}`);\n  }\n  // Sinon, essayer l'URL de l'image via la fonction getBookImageUrl\n  else if (book.image) {\n    imageUrl = config.getBookImageUrl(book.image);\n    console.log(`BookCard - Utilisation de l'URL d'image: ${imageUrl}`);\n  }\n  // En dernier recours, utiliser une URL statique basée sur l'ID\n  else {\n    imageUrl = config.STATIC_URL + `images/livres/book_${book.id}.jpg`;\n    console.log(`BookCard - Utilisation de l'URL basée sur l'ID: ${imageUrl}`);\n  }\n  console.log(`BookCard - URL d'image: ${imageUrl}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/books/${book.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: book.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `book-card-status ${getStatusColor()}`,\n          children: book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", book.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: book.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [book.quantitie_Dispo, \" / \", book.quantitie_Total, \" disponibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(BookCard, \"byfRDkJ+t3MKhIwXgwbXYFaZtD0=\", false, function () {\n  return [useAuth];\n});\n_c = BookCard;\nexport default BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");", "map": {"version": 3, "names": ["React", "Link", "useAuth", "config", "jsxDEV", "_jsxDEV", "BookCard", "book", "_s", "_currentUser$profile", "currentUser", "isAdmin", "profile", "user_type", "is_superuser", "getStatusColor", "quantitie_Dispo", "console", "log", "id", "titre", "imageUrl", "static_image_url", "STATIC_URL", "image", "getBookImageUrl", "className", "children", "to", "src", "alt", "onError", "e", "error", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "autheur", "category_name", "quantitie_Total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/BookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport './BookCard.css';\nimport config from '../config';\n\nconst BookCard = ({ book }) => {\n  // Récupérer les informations de l'utilisateur connecté\n  const { currentUser } = useAuth();\n\n  // Vérifier si l'utilisateur est un administrateur\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  // Log des informations du livre\n  console.log(`BookCard - Livre ID: ${book.id}, Titre: ${book.titre}`);\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL correcte\n  let imageUrl;\n\n  // Essayer d'abord l'URL statique directe si elle existe\n  if (book.static_image_url) {\n    imageUrl = config.STATIC_URL + book.static_image_url;\n    console.log(`BookCard - Utilisation de l'URL statique: ${imageUrl}`);\n  }\n  // Sinon, essayer l'URL de l'image via la fonction getBookImageUrl\n  else if (book.image) {\n    imageUrl = config.getBookImageUrl(book.image);\n    console.log(`BookCard - Utilisation de l'URL d'image: ${imageUrl}`);\n  }\n  // En dernier recours, utiliser une URL statique basée sur l'ID\n  else {\n    imageUrl = config.STATIC_URL + `images/livres/book_${book.id}.jpg`;\n    console.log(`BookCard - Utilisation de l'URL basée sur l'ID: ${imageUrl}`);\n  }\n\n  console.log(`BookCard - URL d'image: ${imageUrl}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/books/${book.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          <img\n            src={imageUrl}\n            alt={book.titre}\n            onError={(e) => {\n              console.error(`Erreur de chargement d'image: ${e.target.src}`);\n              e.target.onerror = null;\n              e.target.src = config.DEFAULT_BOOK_IMAGE;\n            }}\n          />\n          <div className={`book-card-status ${getStatusColor()}`}>\n            {book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{book.titre}</h3>\n          <p className=\"book-card-author\">Par {book.autheur}</p>\n          <p className=\"book-card-category\">{book.category_name}</p>\n          {isAdmin && (\n            <p className=\"book-card-availability\">\n              {book.quantitie_Dispo} / {book.quantitie_Total} disponibles\n            </p>\n          )}\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default BookCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC7B;EACA,MAAM;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAMS,OAAO,GAAG,CAAAD,WAAW,aAAXA,WAAW,wBAAAD,oBAAA,GAAXC,WAAW,CAAEE,OAAO,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,SAAS,MAAK,OAAO,KAAIH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,YAAY;;EAExF;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIR,IAAI,CAACS,eAAe,IAAI,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIT,IAAI,CAACS,eAAe,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC/C,OAAO,OAAO;EAChB,CAAC;;EAED;EACAC,OAAO,CAACC,GAAG,CAAC,wBAAwBX,IAAI,CAACY,EAAE,YAAYZ,IAAI,CAACa,KAAK,EAAE,CAAC;;EAEpE;EACA,IAAIC,QAAQ;;EAEZ;EACA,IAAId,IAAI,CAACe,gBAAgB,EAAE;IACzBD,QAAQ,GAAGlB,MAAM,CAACoB,UAAU,GAAGhB,IAAI,CAACe,gBAAgB;IACpDL,OAAO,CAACC,GAAG,CAAC,6CAA6CG,QAAQ,EAAE,CAAC;EACtE;EACA;EAAA,KACK,IAAId,IAAI,CAACiB,KAAK,EAAE;IACnBH,QAAQ,GAAGlB,MAAM,CAACsB,eAAe,CAAClB,IAAI,CAACiB,KAAK,CAAC;IAC7CP,OAAO,CAACC,GAAG,CAAC,4CAA4CG,QAAQ,EAAE,CAAC;EACrE;EACA;EAAA,KACK;IACHA,QAAQ,GAAGlB,MAAM,CAACoB,UAAU,GAAG,sBAAsBhB,IAAI,CAACY,EAAE,MAAM;IAClEF,OAAO,CAACC,GAAG,CAAC,mDAAmDG,QAAQ,EAAE,CAAC;EAC5E;EAEAJ,OAAO,CAACC,GAAG,CAAC,2BAA2BG,QAAQ,EAAE,CAAC;EAElD,oBACEhB,OAAA;IAAKqB,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBtB,OAAA,CAACJ,IAAI;MAAC2B,EAAE,EAAE,UAAUrB,IAAI,CAACY,EAAE,EAAG;MAACO,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACvDtB,OAAA;QAAKqB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BtB,OAAA;UACEwB,GAAG,EAAER,QAAS;UACdS,GAAG,EAAEvB,IAAI,CAACa,KAAM;UAChBW,OAAO,EAAGC,CAAC,IAAK;YACdf,OAAO,CAACgB,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACL,GAAG,EAAE,CAAC;YAC9DG,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACL,GAAG,GAAG1B,MAAM,CAACiC,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFnC,OAAA;UAAKqB,SAAS,EAAE,oBAAoBX,cAAc,CAAC,CAAC,EAAG;UAAAY,QAAA,EACpDpB,IAAI,CAACS,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG;QAAc;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtB,OAAA;UAAIqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEpB,IAAI,CAACa;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDnC,OAAA;UAAGqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACpB,IAAI,CAACkC,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDnC,OAAA;UAAGqB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEpB,IAAI,CAACmC;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzD7B,OAAO,iBACNN,OAAA;UAAGqB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAClCpB,IAAI,CAACS,eAAe,EAAC,KAAG,EAACT,IAAI,CAACoC,eAAe,EAAC,cACjD;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChC,EAAA,CApEIF,QAAQ;EAAA,QAEYJ,OAAO;AAAA;AAAA0C,EAAA,GAF3BtC,QAAQ;AAsEd,eAAeA,QAAQ;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}