<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport PFA - BiblioDesk</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            margin: 2cm;
            color: #333;
            font-size: 12pt;
        }
        
        .cover-page {
            text-align: center;
            page-break-after: always;
            margin-top: 5cm;
        }
        
        .cover-page h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 2cm 0;
            color: #2c3e50;
            text-transform: uppercase;
        }
        
        .cover-page h2 {
            font-size: 18pt;
            color: #34495e;
            margin: 1cm 0;
        }
        
        .cover-page .info {
            font-size: 14pt;
            margin: 0.5cm 0;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 18pt;
            font-weight: bold;
            margin-top: 2cm;
            margin-bottom: 1cm;
            page-break-before: always;
        }
        
        h2 {
            color: #34495e;
            font-size: 16pt;
            font-weight: bold;
            margin-top: 1.5cm;
            margin-bottom: 0.8cm;
        }
        
        h3 {
            color: #2c3e50;
            font-size: 14pt;
            font-weight: bold;
            margin-top: 1cm;
            margin-bottom: 0.5cm;
        }
        
        h4 {
            color: #34495e;
            font-size: 12pt;
            font-weight: bold;
            margin-top: 0.8cm;
            margin-bottom: 0.4cm;
        }
        
        p {
            text-align: justify;
            margin-bottom: 0.5cm;
        }
        
        .toc {
            page-break-after: always;
        }
        
        .toc h1 {
            page-break-before: auto;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.3cm 0;
            padding-left: 1cm;
        }
        
        .toc .level1 {
            font-weight: bold;
            padding-left: 0;
        }
        
        .toc .level2 {
            padding-left: 1cm;
        }
        
        .toc .level3 {
            padding-left: 2cm;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1cm 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1cm;
            margin: 0.5cm 0;
            font-family: 'Courier New', monospace;
            font-size: 10pt;
            overflow-x: auto;
        }
        
        .diagram {
            text-align: center;
            margin: 1cm 0;
            padding: 0.5cm;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 0.5cm;
            border-left: 4px solid #ffc107;
            margin: 0.5cm 0;
        }
        
        .success {
            background-color: #d4edda;
            padding: 0.5cm;
            border-left: 4px solid #28a745;
            margin: 0.5cm 0;
        }
        
        .info-box {
            background-color: #d1ecf1;
            padding: 0.5cm;
            border-left: 4px solid #17a2b8;
            margin: 0.5cm 0;
        }
        
        ul, ol {
            margin-left: 1cm;
        }
        
        li {
            margin-bottom: 0.2cm;
        }
        
        .page-number {
            position: fixed;
            bottom: 1cm;
            right: 1cm;
            font-size: 10pt;
        }
        
        @media print {
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>

<!-- Page de couverture -->
<div class="cover-page">
    <h1>RAPPORT DE PROJET DE FIN D'ANNÉE (PFA)</h1>
    <h2>SYSTÈME DE GESTION DE BIBLIOTHÈQUE</h2>
    <h2>BiblioDesk</h2>
    
    <div style="margin-top: 3cm;">
        <div class="info"><strong>Présenté par :</strong> [Nom de l'étudiant]</div>
        <div class="info"><strong>Encadré par :</strong> [Nom de l'encadrant]</div>
        <div class="info"><strong>Établissement :</strong> [Nom de l'établissement]</div>
        <div class="info"><strong>Filière :</strong> Génie Informatique / Développement Web</div>
        <div class="info"><strong>Année universitaire :</strong> 2024-2025</div>
    </div>
    
    <div style="margin-top: 4cm;">
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMmMzZTUwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5CaWJsaW9EZXNrPC90ZXh0Pgo8L3N2Zz4K" alt="Logo BiblioDesk" style="width: 200px; height: 200px;">
    </div>
</div>

<!-- Table des matières -->
<div class="toc">
    <h1>TABLE DES MATIÈRES</h1>
    <ul>
        <li class="level1">1. INTRODUCTION GÉNÉRALE .................................................... 4</li>
        <li class="level2">1.1 Contexte du projet</li>
        <li class="level2">1.2 Problématique</li>
        <li class="level2">1.3 Objectifs du projet</li>
        <li class="level2">1.4 Méthodologie adoptée</li>
        
        <li class="level1">2. PRÉSENTATION GÉNÉRALE DU PROJET ........................................ 5</li>
        <li class="level2">2.1 Description du projet</li>
        <li class="level2">2.2 Fonctionnalités principales</li>
        <li class="level2">2.3 Architecture technique</li>
        <li class="level2">2.4 Acteurs du système</li>
        <li class="level2">2.5 Contraintes et exigences</li>
        
        <li class="level1">3. ANALYSE ET CONCEPTION .................................................... 8</li>
        <li class="level2">3.1 Analyse des besoins</li>
        <li class="level2">3.2 Diagrammes de cas d'utilisation</li>
        <li class="level2">3.3 Modélisation UML</li>
        <li class="level2">3.4 Modélisation de la base de données</li>
        <li class="level2">3.5 Architecture logicielle</li>
        
        <li class="level1">4. RÉALISATION ET IMPLÉMENTATION ........................................... 15</li>
        <li class="level2">4.1 Architecture technique détaillée</li>
        <li class="level2">4.2 Implémentation du Backend (Django)</li>
        <li class="level2">4.3 Implémentation du Frontend (React)</li>
        <li class="level2">4.4 Fonctionnalités avancées implémentées</li>
        
        <li class="level1">5. TESTS ET VALIDATION ...................................................... 22</li>
        <li class="level2">5.1 Stratégie de tests</li>
        <li class="level2">5.2 Résultats des tests</li>
        <li class="level2">5.3 Tests utilisateurs</li>
        
        <li class="level1">6. DÉPLOIEMENT ET MISE EN PRODUCTION ....................................... 24</li>
        <li class="level2">6.1 Architecture de déploiement</li>
        <li class="level2">6.2 Scripts de déploiement</li>
        <li class="level2">6.3 Monitoring et maintenance</li>
        
        <li class="level1">7. CONCLUSION ET PERSPECTIVES .............................................. 26</li>
        <li class="level2">7.1 Bilan du projet</li>
        <li class="level2">7.2 Difficultés rencontrées et solutions apportées</li>
        <li class="level2">7.3 Améliorations futures</li>
        <li class="level2">7.4 Impact et retour sur investissement</li>
        <li class="level2">7.5 Conclusion générale</li>
        
        <li class="level1">8. ANNEXES ................................................................. 28</li>
    </ul>
</div>

<!-- Contenu principal -->
<h1>1. INTRODUCTION GÉNÉRALE</h1>

<h2>1.1 Contexte du projet</h2>

<p>Dans l'ère numérique actuelle, la gestion traditionnelle des bibliothèques fait face à de nombreux défis. Les méthodes manuelles de catalogage, d'emprunt et de suivi des livres deviennent obsolètes et inefficaces. Les utilisateurs modernes s'attendent à des services numériques rapides, intuitifs et accessibles 24h/24.</p>

<p>Ce projet de fin d'année s'inscrit dans cette démarche de modernisation des services bibliothécaires. BiblioDesk représente une solution complète de gestion de bibliothèque, alliant les technologies web modernes à une approche centrée utilisateur.</p>

<h2>1.2 Problématique</h2>

<p>Les bibliothèques traditionnelles rencontrent plusieurs problèmes majeurs :</p>

<ul>
    <li><strong>Gestion manuelle inefficace :</strong> Processus d'emprunt et de retour chronophages</li>
    <li><strong>Absence de traçabilité :</strong> Difficulté de suivi des emprunts et des retards</li>
    <li><strong>Communication limitée :</strong> Pas de système de notification automatique</li>
    <li><strong>Accès restreint :</strong> Consultation du catalogue limitée aux heures d'ouverture</li>
    <li><strong>Gestion des ressources numériques :</strong> Absence d'intégration des e-books</li>
    <li><strong>Statistiques inexistantes :</strong> Manque d'outils d'analyse et de reporting</li>
</ul>

<h2>1.3 Objectifs du projet</h2>

<h3>Objectifs principaux :</h3>
<ul>
    <li>Développer une application web moderne de gestion de bibliothèque</li>
    <li>Automatiser les processus d'emprunt, de retour et de réservation</li>
    <li>Créer un système de notification intelligent</li>
    <li>Fournir une interface utilisateur intuitive et responsive</li>
    <li>Intégrer la gestion des livres physiques et numériques</li>
</ul>

<h3>Objectifs secondaires :</h3>
<ul>
    <li>Implémenter un système de statistiques avancées</li>
    <li>Optimiser l'expérience utilisateur</li>
    <li>Assurer la sécurité et la confidentialité des données</li>
    <li>Faciliter l'administration du système</li>
</ul>

<h2>1.4 Méthodologie adoptée</h2>

<p>Le développement de BiblioDesk suit une approche agile avec les phases suivantes :</p>

<ol>
    <li><strong>Analyse des besoins :</strong> Étude des exigences fonctionnelles et techniques</li>
    <li><strong>Conception :</strong> Modélisation UML et architecture système</li>
    <li><strong>Développement itératif :</strong> Implémentation par modules fonctionnels</li>
    <li><strong>Tests continus :</strong> Validation à chaque étape</li>
    <li><strong>Déploiement :</strong> Mise en production et documentation</li>
</ol>

<h1>2. PRÉSENTATION GÉNÉRALE DU PROJET</h1>

<h2>2.1 Description du projet</h2>

<p>BiblioDesk est une application web complète de gestion de bibliothèque développée avec Django (backend) et React (frontend). Elle offre une solution moderne pour la gestion des ressources documentaires, des utilisateurs et des services bibliothécaires.</p>

<div class="info-box">
    <strong>Technologies principales :</strong>
    <ul>
        <li><strong>Backend :</strong> Django 4.2, Django REST Framework 3.14.0</li>
        <li><strong>Frontend :</strong> React 18.2.0, Bootstrap 5.3.2</li>
        <li><strong>Base de données :</strong> SQLite (développement), PostgreSQL (production)</li>
        <li><strong>Services :</strong> SendGrid (emails), Celery (tâches asynchrones)</li>
    </ul>
</div>

<h2>2.2 Fonctionnalités principales</h2>

<h3>2.2.1 Gestion du catalogue</h3>
<ul>
    <li><strong>Livres physiques :</strong> Ajout, modification, suppression avec gestion des stocks</li>
    <li><strong>E-books :</strong> Catalogue numérique avec lecture en ligne</li>
    <li><strong>Catégorisation :</strong> Organisation par catégories et sous-catégories</li>
    <li><strong>Recherche avancée :</strong> Filtres multiples (titre, auteur, ISBN, catégorie)</li>
</ul>

<h3>2.2.2 Système d'emprunt et de réservation</h3>
<ul>
    <li><strong>Emprunt automatisé :</strong> Processus simplifié avec validation des disponibilités</li>
    <li><strong>Réservation intelligente :</strong> File d'attente automatique pour les livres indisponibles</li>
    <li><strong>Gestion des retours :</strong> Suivi automatique des dates de retour</li>
    <li><strong>Historique complet :</strong> Traçabilité de tous les emprunts</li>
</ul>

<h3>2.2.3 Gestion des utilisateurs</h3>
<ul>
    <li><strong>Authentification sécurisée :</strong> Système de login avec vérification email</li>
    <li><strong>Profils utilisateurs :</strong> Gestion des informations personnelles</li>
    <li><strong>Rôles et permissions :</strong> Distinction administrateur/étudiant</li>
    <li><strong>Tableau de bord personnel :</strong> Vue d'ensemble des emprunts en cours</li>
</ul>

<h3>2.2.4 Système de notifications</h3>
<ul>
    <li><strong>Notifications in-app :</strong> Alertes en temps réel dans l'interface</li>
    <li><strong>Emails automatiques :</strong> Confirmations, rappels, alertes de retard</li>
    <li><strong>Types de notifications :</strong>
        <ul>
            <li>Confirmation d'emprunt</li>
            <li>Rappels de retour (2 jours avant échéance)</li>
            <li>Alertes de retard</li>
            <li>Disponibilité de livres réservés</li>
            <li>Nouveaux livres ajoutés au catalogue</li>
        </ul>
    </li>
</ul>

</body>
</html>
