{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async token => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const userResponse = await api.get('/api/utilisateurs/users/me/');\n      let profileData = null;\n      try {\n        const profileResponse = await api.get('/api/utilisateurs/profiles/me/');\n        profileData = profileResponse.data;\n      } catch (profileError) {\n        console.warn('Erreur lors de la récupération du profil, utilisation d\\'un profil par défaut:', profileError);\n        // Utiliser un profil par défaut si le profil n'existe pas\n        profileData = {\n          user_type: 'etudiant',\n          email_verified: true\n        };\n      }\n      const userData = {\n        ...userResponse.data,\n        profile: profileData\n      };\n      setCurrentUser(userData);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n\n      // Si l'erreur est due à un token invalide ou expiré\n      if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n        setAuthError('Session expirée. Veuillez vous reconnecter.');\n      } else {\n        setAuthError('Impossible de récupérer les informations utilisateur');\n      }\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (token) {\n          // Configurer les en-têtes d'authentification pour toutes les requêtes\n          axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n          api.defaults.headers.common['Authorization'] = `Token ${token}`;\n          const success = await fetchUserInfo(token);\n          if (!success) {\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n            delete api.defaults.headers.common['Authorization'];\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        } else {\n          setIsAuthenticated(false);\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n        delete api.defaults.headers.common['Authorization'];\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, [fetchUserInfo]);\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n      console.log('Tentative de connexion pour:', username);\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n      console.log('Réponse du serveur:', response.data);\n      const {\n        user,\n        profile,\n        token\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n        console.log('Token stocké avec succès');\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n\n      // S'assurer que le profil est défini\n      const userWithProfile = {\n        ...user,\n        profile: profile || {\n          user_type: 'etudiant',\n          email_verified: true\n        }\n      };\n      setCurrentUser(userWithProfile);\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Erreur de connexion:', error);\n\n      // Afficher plus de détails sur l'erreur pour le débogage\n      if (error.response) {\n        console.error('Détails de l\\'erreur:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n      }\n      setAuthError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Erreur de connexion');\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Erreur de connexion'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const {\n        token,\n        user\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response5, _error$response5$data;\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && _error$response4.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n      return {\n        success: false,\n        message: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || 'Erreur d\\'inscription'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data, _error$response7, _error$response7$data;\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"MmdmBtoNyEXLJ8ErwOEOz7sUNs4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useCallback", "axios", "api", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "authError", "setAuthError", "fetchUserInfo", "token", "userResponse", "get", "profileData", "profileResponse", "data", "profileError", "console", "warn", "user_type", "email_verified", "userData", "profile", "error", "response", "status", "checkAuth", "localStorage", "getItem", "defaults", "headers", "common", "success", "removeItem", "login", "username", "password", "log", "post", "user", "setItem", "message", "userWithProfile", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "register", "autoLogin", "_error$response3", "_error$response3$data", "_error$response4", "_error$response5", "_error$response5$data", "errorMessages", "field", "Array", "isArray", "push", "join", "length", "logout", "updateProfile", "put", "_error$response6", "_error$response6$data", "_error$response7", "_error$response7$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async (token) => {\n    try {\n      // Utiliser l'instance API configurée avec les intercepteurs\n      const userResponse = await api.get('/api/utilisateurs/users/me/');\n\n      let profileData = null;\n      try {\n        const profileResponse = await api.get('/api/utilisateurs/profiles/me/');\n        profileData = profileResponse.data;\n      } catch (profileError) {\n        console.warn('Erreur lors de la récupération du profil, utilisation d\\'un profil par défaut:', profileError);\n        // Utiliser un profil par défaut si le profil n'existe pas\n        profileData = {\n          user_type: 'etudiant',\n          email_verified: true\n        };\n      }\n\n      const userData = {\n        ...userResponse.data,\n        profile: profileData\n      };\n\n      setCurrentUser(userData);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n\n      // Si l'erreur est due à un token invalide ou expiré\n      if (error.response && (error.response.status === 401 || error.response.status === 403)) {\n        setAuthError('Session expirée. Veuillez vous reconnecter.');\n      } else {\n        setAuthError('Impossible de récupérer les informations utilisateur');\n      }\n\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (token) {\n          // Configurer les en-têtes d'authentification pour toutes les requêtes\n          axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n          api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n          const success = await fetchUserInfo(token);\n\n          if (!success) {\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n            delete api.defaults.headers.common['Authorization'];\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        } else {\n          setIsAuthenticated(false);\n          setCurrentUser(null);\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n        delete api.defaults.headers.common['Authorization'];\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [fetchUserInfo]);\n\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n\n      console.log('Tentative de connexion pour:', username);\n\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n\n      console.log('Réponse du serveur:', response.data);\n\n      const { user, profile, token } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n        console.log('Token stocké avec succès');\n      } else {\n        console.error('Pas de token reçu du serveur');\n        setAuthError('Erreur d\\'authentification: pas de token reçu');\n        return {\n          success: false,\n          message: 'Erreur d\\'authentification: pas de token reçu'\n        };\n      }\n\n      // S'assurer que le profil est défini\n      const userWithProfile = {\n        ...user,\n        profile: profile || {\n          user_type: 'etudiant',\n          email_verified: true\n        }\n      };\n\n      setCurrentUser(userWithProfile);\n      setIsAuthenticated(true);\n\n      return { success: true };\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n\n      // Afficher plus de détails sur l'erreur pour le débogage\n      if (error.response) {\n        console.error('Détails de l\\'erreur:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n      }\n\n      setAuthError(error.response?.data?.detail || 'Erreur de connexion');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur de connexion'\n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n\n      // Si l'inscription réussit et qu'un token est renvoyé, connecter l'utilisateur\n      const { token, user } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      }\n\n      return {\n        success: true,\n        message: response.data.detail,\n        autoLogin: !!token // Indique si l'utilisateur a été automatiquement connecté\n      };\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if (error.response?.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur d\\'inscription'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n\n      return { success: true, data: response.data };\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMU,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMT,UAAU,CAACO,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMsB,aAAa,GAAGnB,WAAW,CAAC,MAAOoB,KAAK,IAAK;IACjD,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,MAAMnB,GAAG,CAACoB,GAAG,CAAC,6BAA6B,CAAC;MAEjE,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAI;QACF,MAAMC,eAAe,GAAG,MAAMtB,GAAG,CAACoB,GAAG,CAAC,gCAAgC,CAAC;QACvEC,WAAW,GAAGC,eAAe,CAACC,IAAI;MACpC,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBC,OAAO,CAACC,IAAI,CAAC,gFAAgF,EAAEF,YAAY,CAAC;QAC5G;QACAH,WAAW,GAAG;UACZM,SAAS,EAAE,UAAU;UACrBC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,MAAMC,QAAQ,GAAG;QACf,GAAGV,YAAY,CAACI,IAAI;QACpBO,OAAO,EAAET;MACX,CAAC;MAEDX,cAAc,CAACmB,QAAQ,CAAC;MACxBjB,kBAAkB,CAAC,IAAI,CAAC;MACxBI,YAAY,CAAC,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;;MAEpF;MACA,IAAIA,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAIF,KAAK,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,CAAC,EAAE;QACtFjB,YAAY,CAAC,6CAA6C,CAAC;MAC7D,CAAC,MAAM;QACLA,YAAY,CAAC,sDAAsD,CAAC;MACtE;MAEA,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMqC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,KAAK,GAAGiB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAIlB,KAAK,EAAE;UACT;UACAnB,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;UACjElB,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;UAE/D,MAAMsB,OAAO,GAAG,MAAMvB,aAAa,CAACC,KAAK,CAAC;UAE1C,IAAI,CAACsB,OAAO,EAAE;YACZL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;YAChC,OAAO1C,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;YACrD,OAAOvC,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;YACnD3B,kBAAkB,CAAC,KAAK,CAAC;YACzBF,cAAc,CAAC,IAAI,CAAC;UACtB;QACF,CAAC,MAAM;UACLE,kBAAkB,CAAC,KAAK,CAAC;UACzBF,cAAc,CAAC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDI,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChC,OAAO1C,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;QACrD,OAAOvC,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;QACnD3B,kBAAkB,CAAC,KAAK,CAAC;QACzBF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;EAEnB,MAAMyB,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF5B,YAAY,CAAC,IAAI,CAAC;MAElBS,OAAO,CAACoB,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;MAErD,MAAMX,QAAQ,GAAG,MAAMhC,GAAG,CAAC8C,IAAI,CAAC,0BAA0B,EAAE;QAC1DH,QAAQ;QACRC;MACF,CAAC,CAAC;MAEFnB,OAAO,CAACoB,GAAG,CAAC,qBAAqB,EAAEb,QAAQ,CAACT,IAAI,CAAC;MAEjD,MAAM;QAAEwB,IAAI;QAAEjB,OAAO;QAAEZ;MAAM,CAAC,GAAGc,QAAQ,CAACT,IAAI;MAE9C,IAAIL,KAAK,EAAE;QACTiB,YAAY,CAACa,OAAO,CAAC,OAAO,EAAE9B,KAAK,CAAC;QACpCnB,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;QACjElB,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;QAE/DO,OAAO,CAACoB,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC,MAAM;QACLpB,OAAO,CAACM,KAAK,CAAC,8BAA8B,CAAC;QAC7Cf,YAAY,CAAC,+CAA+C,CAAC;QAC7D,OAAO;UACLwB,OAAO,EAAE,KAAK;UACdS,OAAO,EAAE;QACX,CAAC;MACH;;MAEA;MACA,MAAMC,eAAe,GAAG;QACtB,GAAGH,IAAI;QACPjB,OAAO,EAAEA,OAAO,IAAI;UAClBH,SAAS,EAAE,UAAU;UACrBC,cAAc,EAAE;QAClB;MACF,CAAC;MAEDlB,cAAc,CAACwC,eAAe,CAAC;MAC/BtC,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAE4B,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClBP,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAE;UACrCE,MAAM,EAAEF,KAAK,CAACC,QAAQ,CAACC,MAAM;UAC7BV,IAAI,EAAEQ,KAAK,CAACC,QAAQ,CAACT,IAAI;UACzBe,OAAO,EAAEP,KAAK,CAACC,QAAQ,CAACM;QAC1B,CAAC,CAAC;MACJ;MAEAtB,YAAY,CAAC,EAAAmC,eAAA,GAAApB,KAAK,CAACC,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,qBAAqB,CAAC;MACnE,OAAO;QACLf,OAAO,EAAE,KAAK;QACdS,OAAO,EAAE,EAAAI,gBAAA,GAAAtB,KAAK,CAACC,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAO3B,QAAQ,IAAK;IACnC,IAAI;MACFb,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMgB,QAAQ,GAAG,MAAMhC,GAAG,CAAC8C,IAAI,CAAC,6BAA6B,EAAEjB,QAAQ,CAAC;;MAExE;MACA,MAAM;QAAEX,KAAK;QAAE6B;MAAK,CAAC,GAAGf,QAAQ,CAACT,IAAI;MAErC,IAAIL,KAAK,EAAE;QACTiB,YAAY,CAACa,OAAO,CAAC,OAAO,EAAE9B,KAAK,CAAC;QACpCnB,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;QACjElB,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,SAASrB,KAAK,EAAE;QAE/DR,cAAc,CAACqC,IAAI,CAAC;QACpBnC,kBAAkB,CAAC,IAAI,CAAC;MAC1B;MAEA,OAAO;QACL4B,OAAO,EAAE,IAAI;QACbS,OAAO,EAAEjB,QAAQ,CAACT,IAAI,CAACgC,MAAM;QAC7BE,SAAS,EAAE,CAAC,CAACvC,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,CAAC,OAAOa,KAAK,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdrC,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9Cf,YAAY,CAAC,EAAA0C,gBAAA,GAAA3B,KAAK,CAACC,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,uBAAuB,CAAC;;MAErE;MACA,IAAI,CAAAK,gBAAA,GAAA7B,KAAK,CAACC,QAAQ,cAAA4B,gBAAA,eAAdA,gBAAA,CAAgBrC,IAAI,IAAI,OAAOQ,KAAK,CAACC,QAAQ,CAACT,IAAI,KAAK,QAAQ,EAAE;QACnE,MAAMwC,aAAa,GAAG,EAAE;QACxB,KAAK,MAAMC,KAAK,IAAIjC,KAAK,CAACC,QAAQ,CAACT,IAAI,EAAE;UACvC,IAAI0C,KAAK,CAACC,OAAO,CAACnC,KAAK,CAACC,QAAQ,CAACT,IAAI,CAACyC,KAAK,CAAC,CAAC,EAAE;YAC7CD,aAAa,CAACI,IAAI,CAAC,GAAGH,KAAK,KAAKjC,KAAK,CAACC,QAAQ,CAACT,IAAI,CAACyC,KAAK,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UAC1E;QACF;QAEA,IAAIL,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;UAC5B,OAAO;YACL7B,OAAO,EAAE,KAAK;YACdS,OAAO,EAAEc,aAAa,CAACK,IAAI,CAAC,IAAI;UAClC,CAAC;QACH;MACF;MAEA,OAAO;QACL5B,OAAO,EAAE,KAAK;QACdS,OAAO,EAAE,EAAAY,gBAAA,GAAA9B,KAAK,CAACC,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtC,IAAI,cAAAuC,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMe,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMtE,GAAG,CAAC8C,IAAI,CAAC,2BAA2B,CAAC;IAC7C,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRrB,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,KAAK,CAAC;MACzBI,YAAY,CAAC,IAAI,CAAC;MAClBmB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO1C,KAAK,CAACsC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACrD,OAAOvC,GAAG,CAACqC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMgC,aAAa,GAAG,MAAO1C,QAAQ,IAAK;IACxC,IAAI;MACFb,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMgB,QAAQ,GAAG,MAAMhC,GAAG,CAACwE,GAAG,CAAC,4BAA4B,EAAE3C,QAAQ,CAAC;;MAEtE;MACA,MAAMZ,aAAa,CAACkB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;MAElD,OAAO;QAAEI,OAAO,EAAE,IAAI;QAAEjB,IAAI,EAAES,QAAQ,CAACT;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdnD,OAAO,CAACM,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEf,YAAY,CAAC,EAAAyD,gBAAA,GAAA1C,KAAK,CAACC,QAAQ,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsBnB,MAAM,KAAI,yCAAyC,CAAC;MACvF,OAAO;QACLf,OAAO,EAAE,KAAK;QACdS,OAAO,EAAE,EAAA0B,gBAAA,GAAA5C,KAAK,CAACC,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpD,IAAI,cAAAqD,qBAAA,uBAApBA,qBAAA,CAAsBrB,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMsB,KAAK,GAAG;IACZpE,WAAW;IACXE,eAAe;IACfE,OAAO;IACPE,SAAS;IACT2B,KAAK;IACLc,QAAQ;IACRc,MAAM;IACNC;EACF,CAAC;EAED,oBACErE,OAAA,CAACC,WAAW,CAAC2E,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtE,QAAA,EAChCA;EAAQ;IAAAwE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC1E,GAAA,CA5PWF,YAAY;AAAA6E,EAAA,GAAZ7E,YAAY;AAAA,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}