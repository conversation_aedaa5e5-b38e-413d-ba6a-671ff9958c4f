{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Gérer les erreurs d'authentification (401)\n  if (error.response && error.response.status === 401) {\n    // Supprimer le token et rediriger vers la page de connexion\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n\n  // Gérer les erreurs de permission (403)\n  if (error.response && error.response.status === 403) {\n    console.error('Erreur de permission:', error.response.data);\n  }\n  return Promise.reject(error);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.LIVRES, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.LIVRES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: params => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.EBOOKS, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: id => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: id => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: data => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: id => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href", "console", "livresAPI", "getAll", "params", "get", "ENDPOINTS", "LIVRES", "getById", "id", "post", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "EBOOKS", "categoriesAPI", "CATEGORIES", "empruntsAPI", "EMPRUNTS", "retourner", "reservationsAPI", "RESERVATIONS", "annuler", "utilisateursAPI", "getProfile", "UTILISATEURS", "updateProfile", "getNotifications", "markNotificationRead"], "sources": ["C:/Users/<USER>/Desktop/projetMAI/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Gérer les erreurs d'authentification (401)\n    if (error.response && error.response.status === 401) {\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n\n    // Gérer les erreurs de permission (403)\n    if (error.response && error.response.status === 403) {\n      console.error('Erreur de permission:', error.response.data);\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.LIVRES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: (params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, { params }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.EBOOKS, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: (id) => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: (id) => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: (data) => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;EACvC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnD;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;;EAEA;EACA,IAAIR,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnDK,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;EAC7D;EAEA,OAAOG,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC7B,UAAU,CAAC8B,SAAS,CAACC,MAAM,EAAE;IAAEH;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKhC,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAChE/B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACiC,IAAI,CAAClC,UAAU,CAAC8B,SAAS,CAACC,MAAM,EAAEjB,IAAI,CAAC;EAC7DqB,MAAM,EAAEA,CAACF,EAAE,EAAEnB,IAAI,KAAKb,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,EAAEnB,IAAI,CAAC;EAC3EuB,MAAM,EAAGJ,EAAE,IAAKhC,GAAG,CAACoC,MAAM,CAAC,GAAGrC,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAClEK,SAAS,EAAGL,EAAE,IAAKhC,GAAG,CAACiC,IAAI,CAAC,GAAGlC,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,aAAa,CAAC;EAC7EM,QAAQ,EAAGN,EAAE,IAAKhC,GAAG,CAACiC,IAAI,CAAC,GAAGlC,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,YAAY,CAAC;EAC3EO,kBAAkB,EAAGZ,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACC,MAAM,kBAAkB,EAAE;IAAEH;EAAO,CAAC,CAAC;EACrGa,UAAU,EAAEA,CAACR,EAAE,EAAEL,MAAM,KAAK3B,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAGE,EAAE,WAAW,EAAE;IAAEL;EAAO,CAAC;AAChG,CAAC;;AAED;AACA,OAAO,MAAMc,SAAS,GAAG;EACvBf,MAAM,EAAGC,MAAM,IAAK3B,GAAG,CAAC4B,GAAG,CAAC7B,UAAU,CAAC8B,SAAS,CAACa,MAAM,EAAE;IAAEf;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKhC,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG,CAAC;EAChE/B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACiC,IAAI,CAAClC,UAAU,CAAC8B,SAAS,CAACa,MAAM,EAAE7B,IAAI,CAAC;EAC7DqB,MAAM,EAAEA,CAACF,EAAE,EAAEnB,IAAI,KAAKb,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAAC8B,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG,EAAEnB,IAAI,CAAC;EAC3EuB,MAAM,EAAGJ,EAAE,IAAKhC,GAAG,CAACoC,MAAM,CAAC,GAAGrC,UAAU,CAAC8B,SAAS,CAACa,MAAM,GAAGV,EAAE,GAAG;AACnE,CAAC;;AAED;AACA,OAAO,MAAMW,aAAa,GAAG;EAC3BjB,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC7B,UAAU,CAAC8B,SAAS,CAACe,UAAU,CAAC;EACtDb,OAAO,EAAGC,EAAE,IAAKhC,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG,CAAC;EACpE/B,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACiC,IAAI,CAAClC,UAAU,CAAC8B,SAAS,CAACe,UAAU,EAAE/B,IAAI,CAAC;EACjEqB,MAAM,EAAEA,CAACF,EAAE,EAAEnB,IAAI,KAAKb,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAAC8B,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG,EAAEnB,IAAI,CAAC;EAC/EuB,MAAM,EAAGJ,EAAE,IAAKhC,GAAG,CAACoC,MAAM,CAAC,GAAGrC,UAAU,CAAC8B,SAAS,CAACe,UAAU,GAAGZ,EAAE,GAAG;AACvE,CAAC;;AAED;AACA,OAAO,MAAMa,WAAW,GAAG;EACzBnB,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC7B,UAAU,CAAC8B,SAAS,CAACiB,QAAQ,CAAC;EACpDf,OAAO,EAAGC,EAAE,IAAKhC,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACiB,QAAQ,GAAGd,EAAE,GAAG,CAAC;EAClEe,SAAS,EAAGf,EAAE,IAAKhC,GAAG,CAACiC,IAAI,CAAC,GAAGlC,UAAU,CAAC8B,SAAS,CAACiB,QAAQ,GAAGd,EAAE,aAAa;AAChF,CAAC;;AAED;AACA,OAAO,MAAMgB,eAAe,GAAG;EAC7BtB,MAAM,EAAEA,CAAA,KAAM1B,GAAG,CAAC4B,GAAG,CAAC7B,UAAU,CAAC8B,SAAS,CAACoB,YAAY,CAAC;EACxDlB,OAAO,EAAGC,EAAE,IAAKhC,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACoB,YAAY,GAAGjB,EAAE,GAAG,CAAC;EACtEkB,OAAO,EAAGlB,EAAE,IAAKhC,GAAG,CAACoC,MAAM,CAAC,GAAGrC,UAAU,CAAC8B,SAAS,CAACoB,YAAY,GAAGjB,EAAE,GAAG;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMmB,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAMpD,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACwB,YAAY,cAAc,CAAC;EAC7EC,aAAa,EAAGzC,IAAI,IAAKb,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAAC8B,SAAS,CAACwB,YAAY,cAAc,EAAExC,IAAI,CAAC;EAC1F0C,gBAAgB,EAAEA,CAAA,KAAMvD,GAAG,CAAC4B,GAAG,CAAC,GAAG7B,UAAU,CAAC8B,SAAS,CAACwB,YAAY,gBAAgB,CAAC;EACrFG,oBAAoB,EAAGxB,EAAE,IAAKhC,GAAG,CAACiC,IAAI,CAAC,GAAGlC,UAAU,CAAC8B,SAAS,CAACwB,YAAY,iBAAiBrB,EAAE,QAAQ;AACxG,CAAC;AAED,eAAehC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}