{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\BookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookCard = ({\n  book\n}) => {\n  // Fonction pour gérer l'affichage des images\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/books/${book.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [book.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: config.getBookImageUrl(book.image),\n          alt: book.titre,\n          onError: e => {\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `book-card-status ${getStatusColor()}`,\n          children: book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", book.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: book.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [book.quantitie_Dispo, \" / \", book.quantitie_Total, \" disponibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = BookCard;\nexport default BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "BookCard", "book", "getStatusColor", "quantitie_Dispo", "className", "children", "to", "id", "image", "src", "getBookImageUrl", "alt", "titre", "onError", "e", "target", "onerror", "DEFAULT_BOOK_IMAGE", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "autheur", "category_name", "quantitie_Total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/BookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\n\nconst BookCard = ({ book }) => {\n  // Fonction pour gérer l'affichage des images\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/books/${book.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {book.image ? (\n            <img\n              src={config.getBookImageUrl(book.image)}\n              alt={book.titre}\n              onError={(e) => {\n                e.target.onerror = null;\n                e.target.src = config.DEFAULT_BOOK_IMAGE;\n              }}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className={`book-card-status ${getStatusColor()}`}>\n            {book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{book.titre}</h3>\n          <p className=\"book-card-author\">Par {book.autheur}</p>\n          <p className=\"book-card-category\">{book.category_name}</p>\n          <p className=\"book-card-availability\">\n            {book.quantitie_Dispo} / {book.quantitie_Total} disponibles\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default BookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAC7B;;EAEA;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAID,IAAI,CAACE,eAAe,IAAI,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIF,IAAI,CAACE,eAAe,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC/C,OAAO,OAAO;EAChB,CAAC;EAED,oBACEJ,OAAA;IAAKK,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBN,OAAA,CAACH,IAAI;MAACU,EAAE,EAAE,UAAUL,IAAI,CAACM,EAAE,EAAG;MAACH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACvDN,OAAA;QAAKK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7BJ,IAAI,CAACO,KAAK,gBACTT,OAAA;UACEU,GAAG,EAAEZ,MAAM,CAACa,eAAe,CAACT,IAAI,CAACO,KAAK,CAAE;UACxCG,GAAG,EAAEV,IAAI,CAACW,KAAM;UAChBC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,GAAGZ,MAAM,CAACoB,kBAAkB;UAC1C;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFtB,OAAA;UAAKK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCN,OAAA;YAAAM,QAAA,EAAM;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACDtB,OAAA;UAAKK,SAAS,EAAE,oBAAoBF,cAAc,CAAC,CAAC,EAAG;UAAAG,QAAA,EACpDJ,IAAI,CAACE,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG;QAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtB,OAAA;QAAKK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCN,OAAA;UAAIK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEJ,IAAI,CAACW;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDtB,OAAA;UAAGK,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACJ,IAAI,CAACqB,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDtB,OAAA;UAAGK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEJ,IAAI,CAACsB;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DtB,OAAA;UAAGK,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAClCJ,IAAI,CAACE,eAAe,EAAC,KAAG,EAACF,IAAI,CAACuB,eAAe,EAAC,cACjD;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACI,EAAA,GA3CIzB,QAAQ;AA6Cd,eAAeA,QAAQ;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}