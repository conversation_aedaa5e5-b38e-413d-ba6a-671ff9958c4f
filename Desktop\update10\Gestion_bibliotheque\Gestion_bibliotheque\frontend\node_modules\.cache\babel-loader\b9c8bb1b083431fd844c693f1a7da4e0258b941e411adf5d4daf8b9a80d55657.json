{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\EbookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookCard = ({\n  ebook\n}) => {\n  // Afficher les informations de l'ebook pour le débogage\n  console.log('Données de l\\'ebook dans EbookCard:', ebook);\n  console.log('URL de l\\'image dans EbookCard:', ebook.image);\n  console.log('URL de l\\'image traitée:', config.getImageUrl(ebook.image));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/ebooks/${ebook.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [ebook.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: ebook.image_url || config.getImageUrl(ebook.image) || `http://localhost:8000/media/ebooks/${ebook.id}.jpg`,\n          alt: ebook.titre,\n          onError: e => {\n            console.log('Erreur de chargement de l\\'image:', e);\n            e.target.onerror = null;\n            e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-status green\",\n          children: ebook.format.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", ebook.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: ebook.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [\"Format: \", ebook.format.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = EbookCard;\nexport default EbookCard;\nvar _c;\n$RefreshReg$(_c, \"EbookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "EbookCard", "ebook", "console", "log", "image", "getImageUrl", "className", "children", "to", "id", "src", "image_url", "alt", "titre", "onError", "e", "target", "onerror", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "format", "toUpperCase", "autheur", "category_name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/EbookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\n\nconst EbookCard = ({ ebook }) => {\n  // Afficher les informations de l'ebook pour le débogage\n  console.log('Données de l\\'ebook dans EbookCard:', ebook);\n  console.log('URL de l\\'image dans EbookCard:', ebook.image);\n  console.log('URL de l\\'image traitée:', config.getImageUrl(ebook.image));\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/ebooks/${ebook.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          {ebook.image ? (\n            <img\n              src={ebook.image_url || config.getImageUrl(ebook.image) || `http://localhost:8000/media/ebooks/${ebook.id}.jpg`}\n              alt={ebook.titre}\n              onError={(e) => {\n                console.log('Erreur de chargement de l\\'image:', e);\n                e.target.onerror = null;\n                e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n              }}\n            />\n          ) : (\n            <div className=\"book-card-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n          <div className=\"book-card-status green\">\n            {ebook.format.toUpperCase()}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{ebook.titre}</h3>\n          <p className=\"book-card-author\">Par {ebook.autheur}</p>\n          <p className=\"book-card-category\">{ebook.category_name}</p>\n          <p className=\"book-card-availability\">\n            Format: {ebook.format.toUpperCase()}\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default EbookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB,CAAC,CAAC;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B;EACAC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,KAAK,CAAC;EACzDC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,KAAK,CAACG,KAAK,CAAC;EAC3DF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,MAAM,CAACQ,WAAW,CAACJ,KAAK,CAACG,KAAK,CAAC,CAAC;EAExE,oBACEL,OAAA;IAAKO,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBR,OAAA,CAACH,IAAI;MAACY,EAAE,EAAE,WAAWP,KAAK,CAACQ,EAAE,EAAG;MAACH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACzDR,OAAA;QAAKO,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC7BN,KAAK,CAACG,KAAK,gBACVL,OAAA;UACEW,GAAG,EAAET,KAAK,CAACU,SAAS,IAAId,MAAM,CAACQ,WAAW,CAACJ,KAAK,CAACG,KAAK,CAAC,IAAI,sCAAsCH,KAAK,CAACQ,EAAE,MAAO;UAChHG,GAAG,EAAEX,KAAK,CAACY,KAAM;UACjBC,OAAO,EAAGC,CAAC,IAAK;YACdb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEY,CAAC,CAAC;YACnDA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBF,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wHAAwH;UACzI;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEFtB,OAAA;UAAKO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCR,OAAA;YAAAQ,QAAA,EAAM;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,eACDtB,OAAA;UAAKO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCN,KAAK,CAACqB,MAAM,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtB,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA;UAAIO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEN,KAAK,CAACY;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDtB,OAAA;UAAGO,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACN,KAAK,CAACuB,OAAO;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDtB,OAAA;UAAGO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEN,KAAK,CAACwB;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DtB,OAAA;UAAGO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,UAC5B,EAACN,KAAK,CAACqB,MAAM,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GAxCI1B,SAAS;AA0Cf,eAAeA,SAAS;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}