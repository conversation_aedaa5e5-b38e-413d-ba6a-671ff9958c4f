{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\BookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookCard = ({\n  book\n}) => {\n  // Afficher les informations du livre pour le débogage\n  console.log('Données du livre dans BookCard:', book);\n  console.log('URL de l\\'image dans BookCard:', book.image);\n  console.log('URL de l\\'image traitée:', config.getImageUrl(book.image));\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/books/${book.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: `http://localhost:8000/media/book_${book.id}.jpg`,\n          alt: book.titre,\n          onError: e => {\n            e.target.onerror = null;\n            e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `book-card-status ${getStatusColor()}`,\n          children: book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", book.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: book.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [book.quantitie_Dispo, \" / \", book.quantitie_Total, \" disponibles\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = BookCard;\nexport default BookCard;\nvar _c;\n$RefreshReg$(_c, \"BookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "BookCard", "book", "console", "log", "image", "getImageUrl", "getStatusColor", "quantitie_Dispo", "className", "children", "to", "id", "src", "alt", "titre", "onError", "e", "target", "onerror", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "autheur", "category_name", "quantitie_Total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/BookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css';\nimport config from '../config';\n\nconst BookCard = ({ book }) => {\n  // Afficher les informations du livre pour le débogage\n  console.log('Données du livre dans BookCard:', book);\n  console.log('URL de l\\'image dans BookCard:', book.image);\n  console.log('URL de l\\'image traitée:', config.getImageUrl(book.image));\n\n  // Fonction pour déterminer la couleur du statut\n  const getStatusColor = () => {\n    if (book.quantitie_Dispo <= 0) return 'red';\n    if (book.quantitie_Dispo <= 10) return 'orange';\n    return 'green';\n  };\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/books/${book.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          <img\n            src={`http://localhost:8000/media/book_${book.id}.jpg`}\n            alt={book.titre}\n            onError={(e) => {\n              e.target.onerror = null;\n              e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n            }}\n          />\n          <div className={`book-card-status ${getStatusColor()}`}>\n            {book.quantitie_Dispo > 0 ? 'Disponible' : 'Indisponible'}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{book.titre}</h3>\n          <p className=\"book-card-author\">Par {book.autheur}</p>\n          <p className=\"book-card-category\">{book.category_name}</p>\n          <p className=\"book-card-availability\">\n            {book.quantitie_Dispo} / {book.quantitie_Total} disponibles\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default BookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB;AACvB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAC7B;EACAC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,IAAI,CAAC;EACpDC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,IAAI,CAACG,KAAK,CAAC;EACzDF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,MAAM,CAACQ,WAAW,CAACJ,IAAI,CAACG,KAAK,CAAC,CAAC;;EAEvE;EACA,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIL,IAAI,CAACM,eAAe,IAAI,CAAC,EAAE,OAAO,KAAK;IAC3C,IAAIN,IAAI,CAACM,eAAe,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC/C,OAAO,OAAO;EAChB,CAAC;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBV,OAAA,CAACH,IAAI;MAACc,EAAE,EAAE,UAAUT,IAAI,CAACU,EAAE,EAAG;MAACH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACvDV,OAAA;QAAKS,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BV,OAAA;UACEa,GAAG,EAAE,oCAAoCX,IAAI,CAACU,EAAE,MAAO;UACvDE,GAAG,EAAEZ,IAAI,CAACa,KAAM;UAChBC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBF,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,wHAAwH;UACzI;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvB,OAAA;UAAKS,SAAS,EAAE,oBAAoBF,cAAc,CAAC,CAAC,EAAG;UAAAG,QAAA,EACpDR,IAAI,CAACM,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG;QAAc;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvB,OAAA;QAAKS,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCV,OAAA;UAAIS,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAER,IAAI,CAACa;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDvB,OAAA;UAAGS,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACR,IAAI,CAACsB,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDvB,OAAA;UAAGS,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAER,IAAI,CAACuB;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DvB,OAAA;UAAGS,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAClCR,IAAI,CAACM,eAAe,EAAC,KAAG,EAACN,IAAI,CAACwB,eAAe,EAAC,cACjD;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACI,EAAA,GAxCI1B,QAAQ;AA0Cd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}