{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  // Axios le définira automatiquement avec la boundary correcte\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n\n    // Log pour le débogage\n    console.log('Envoi de FormData:', config.url);\n    console.log('Méthode:', config.method);\n    console.log('Headers:', config.headers);\n  }\n  return config;\n}, error => {\n  console.error('Erreur dans l\\'intercepteur de requête:', error);\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  // Log pour le débogage des réponses réussies\n  console.log(`Réponse réussie de ${response.config.url}:`, response.data);\n  return response;\n}, error => {\n  // Créer un objet d'erreur plus détaillé\n  const enhancedError = {\n    message: 'Une erreur est survenue',\n    originalError: error,\n    timestamp: new Date().toISOString()\n  };\n\n  // Gérer les erreurs réseau (pas de réponse du serveur)\n  if (!error.response) {\n    enhancedError.type = 'network';\n    enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n    console.error('Erreur réseau:', error);\n  }\n  // Gérer les erreurs avec réponse du serveur\n  else {\n    enhancedError.status = error.response.status;\n    enhancedError.data = error.response.data;\n\n    // Log détaillé de l'erreur\n    console.error(`Erreur HTTP ${error.response.status} pour ${error.config.url}:`, {\n      status: error.response.status,\n      statusText: error.response.statusText,\n      data: error.response.data,\n      headers: error.response.headers,\n      config: {\n        url: error.config.url,\n        method: error.config.method,\n        headers: error.config.headers,\n        data: error.config.data instanceof FormData ? 'FormData (non affichable)' : error.config.data\n      }\n    });\n\n    // Gérer les erreurs d'authentification (401)\n    if (error.response.status === 401) {\n      enhancedError.type = 'auth';\n      enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    // Gérer les erreurs de permission (403)\n    else if (error.response.status === 403) {\n      enhancedError.type = 'permission';\n      enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n    }\n    // Gérer les erreurs de validation (400)\n    else if (error.response.status === 400) {\n      enhancedError.type = 'validation';\n      enhancedError.message = 'Les données fournies sont invalides.';\n\n      // Construire un message d'erreur plus détaillé\n      if (typeof error.response.data === 'object') {\n        const errorMessages = [];\n        Object.entries(error.response.data).forEach(([field, messages]) => {\n          if (Array.isArray(messages)) {\n            errorMessages.push(`${field}: ${messages.join(', ')}`);\n          } else if (typeof messages === 'string') {\n            errorMessages.push(`${field}: ${messages}`);\n          } else {\n            errorMessages.push(`${field}: Erreur de validation`);\n          }\n        });\n        if (errorMessages.length > 0) {\n          enhancedError.message = `Erreurs de validation: ${errorMessages.join('; ')}`;\n        }\n      }\n    }\n    // Gérer les erreurs serveur (500)\n    else if (error.response.status >= 500) {\n      enhancedError.type = 'server';\n      enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n    }\n  }\n\n  // Enregistrer l'erreur dans la console avec plus de détails\n  console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n  return Promise.reject(enhancedError);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.LIVRES, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  getCategories: async () => {\n    try {\n      const response = await api.get(API_CONFIG.ENDPOINTS.CATEGORIES);\n      console.log('Réponse des catégories:', response.data);\n\n      // Vérifier et nettoyer les données\n      let cleanedData = [];\n      if (Array.isArray(response.data)) {\n        // Si c'est déjà un tableau, filtrer les éléments invalides\n        cleanedData = response.data.filter(item => item && typeof item === 'object' && item.id && item.name);\n      } else if (response.data && typeof response.data === 'object') {\n        // Si c'est un objet, le convertir en tableau et filtrer\n        const values = Object.values(response.data);\n        cleanedData = values.filter(item => item && typeof item === 'object' && item.id && item.name);\n      }\n      console.log('Données de catégories nettoyées:', cleanedData);\n      return {\n        data: cleanedData\n      };\n    } catch (error) {\n      console.error('Erreur lors de la récupération des catégories:', error);\n      return {\n        data: []\n      }; // Retourner un tableau vide en cas d'erreur\n    }\n  },\n  create: data => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour de livre:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 30000\n    });\n  },\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: params => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.EBOOKS, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  getCategories: async () => {\n    try {\n      const response = await api.get(API_CONFIG.ENDPOINTS.CATEGORIES);\n      console.log('Réponse des catégories (ebooks):', response.data);\n\n      // Vérifier et nettoyer les données\n      let cleanedData = [];\n      if (Array.isArray(response.data)) {\n        // Si c'est déjà un tableau, filtrer les éléments invalides\n        cleanedData = response.data.filter(item => item && typeof item === 'object' && item.id && item.name);\n      } else if (response.data && typeof response.data === 'object') {\n        // Si c'est un objet, le convertir en tableau et filtrer\n        const values = Object.values(response.data);\n        cleanedData = values.filter(item => item && typeof item === 'object' && item.id && item.name);\n      }\n      console.log('Données de catégories nettoyées (ebooks):', cleanedData);\n      return {\n        data: cleanedData\n      };\n    } catch (error) {\n      console.error('Erreur lors de la récupération des catégories (ebooks):', error);\n      return {\n        data: []\n      }; // Retourner un tableau vide en cas d'erreur\n    }\n  },\n  create: data => {\n    console.log('Envoi de données ebook au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.EBOOKS, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour d\\'ebook:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 30000\n    });\n  },\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: id => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: id => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: data => {\n    console.log('Début de la mise à jour du profil');\n    console.log('URL:', `${API_CONFIG.ENDPOINTS.UTILISATEURS}update-profile/`);\n    console.log('Données envoyées:', data);\n\n    // Afficher le contenu du FormData\n    if (data instanceof FormData) {\n      console.log('FormData contenu:');\n      for (let pair of data.entries()) {\n        console.log(`${pair[0]}: ${pair[1]}`);\n      }\n    }\n    return api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}update-profile/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 30000 // Timeout plus long pour l'upload des fichiers\n    }).then(response => {\n      console.log('Réponse de mise à jour du profil:', response);\n      return response;\n    }).catch(error => {\n      var _error$response;\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      console.error('Détails de l\\'erreur:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      throw error;\n    });\n  },\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: id => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`)\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "console", "log", "url", "method", "error", "Promise", "reject", "response", "enhancedError", "message", "originalError", "timestamp", "Date", "toISOString", "type", "status", "statusText", "removeItem", "window", "location", "href", "errorMessages", "Object", "entries", "for<PERSON>ach", "field", "messages", "Array", "isArray", "push", "join", "length", "livresAPI", "getAll", "params", "get", "ENDPOINTS", "LIVRES", "getById", "id", "getCategories", "CATEGORIES", "cleanedData", "filter", "item", "name", "values", "post", "timeout", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "EBOOKS", "categoriesAPI", "empruntsAPI", "EMPRUNTS", "retourner", "reservationsAPI", "RESERVATIONS", "annuler", "utilisateursAPI", "getProfile", "UTILISATEURS", "updateProfile", "pair", "then", "catch", "_error$response", "getNotifications", "markNotificationRead", "statisticsAPI", "STATISTICS"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    // Axios le définira automatiquement avec la boundary correcte\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n\n      // Log pour le débogage\n      console.log('Envoi de FormData:', config.url);\n      console.log('Méthode:', config.method);\n      console.log('Headers:', config.headers);\n    }\n\n    return config;\n  },\n  (error) => {\n    console.error('Erreur dans l\\'intercepteur de requête:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    // Log pour le débogage des réponses réussies\n    console.log(`Réponse réussie de ${response.config.url}:`, response.data);\n    return response;\n  },\n  (error) => {\n    // Créer un objet d'erreur plus détaillé\n    const enhancedError = {\n      message: 'Une erreur est survenue',\n      originalError: error,\n      timestamp: new Date().toISOString(),\n    };\n\n    // Gérer les erreurs réseau (pas de réponse du serveur)\n    if (!error.response) {\n      enhancedError.type = 'network';\n      enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n      console.error('Erreur réseau:', error);\n    }\n    // Gérer les erreurs avec réponse du serveur\n    else {\n      enhancedError.status = error.response.status;\n      enhancedError.data = error.response.data;\n\n      // Log détaillé de l'erreur\n      console.error(`Erreur HTTP ${error.response.status} pour ${error.config.url}:`, {\n        status: error.response.status,\n        statusText: error.response.statusText,\n        data: error.response.data,\n        headers: error.response.headers,\n        config: {\n          url: error.config.url,\n          method: error.config.method,\n          headers: error.config.headers,\n          data: error.config.data instanceof FormData ? 'FormData (non affichable)' : error.config.data,\n        }\n      });\n\n      // Gérer les erreurs d'authentification (401)\n      if (error.response.status === 401) {\n        enhancedError.type = 'auth';\n        enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n        // Supprimer le token et rediriger vers la page de connexion\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n      // Gérer les erreurs de permission (403)\n      else if (error.response.status === 403) {\n        enhancedError.type = 'permission';\n        enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n      }\n      // Gérer les erreurs de validation (400)\n      else if (error.response.status === 400) {\n        enhancedError.type = 'validation';\n        enhancedError.message = 'Les données fournies sont invalides.';\n\n        // Construire un message d'erreur plus détaillé\n        if (typeof error.response.data === 'object') {\n          const errorMessages = [];\n          Object.entries(error.response.data).forEach(([field, messages]) => {\n            if (Array.isArray(messages)) {\n              errorMessages.push(`${field}: ${messages.join(', ')}`);\n            } else if (typeof messages === 'string') {\n              errorMessages.push(`${field}: ${messages}`);\n            } else {\n              errorMessages.push(`${field}: Erreur de validation`);\n            }\n          });\n\n          if (errorMessages.length > 0) {\n            enhancedError.message = `Erreurs de validation: ${errorMessages.join('; ')}`;\n          }\n        }\n      }\n      // Gérer les erreurs serveur (500)\n      else if (error.response.status >= 500) {\n        enhancedError.type = 'server';\n        enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n      }\n    }\n\n    // Enregistrer l'erreur dans la console avec plus de détails\n    console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n\n    return Promise.reject(enhancedError);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  getCategories: async () => {\n    try {\n      const response = await api.get(API_CONFIG.ENDPOINTS.CATEGORIES);\n      console.log('Réponse des catégories:', response.data);\n\n      // Vérifier et nettoyer les données\n      let cleanedData = [];\n\n      if (Array.isArray(response.data)) {\n        // Si c'est déjà un tableau, filtrer les éléments invalides\n        cleanedData = response.data.filter(item => item && typeof item === 'object' && item.id && item.name);\n      } else if (response.data && typeof response.data === 'object') {\n        // Si c'est un objet, le convertir en tableau et filtrer\n        const values = Object.values(response.data);\n        cleanedData = values.filter(item => item && typeof item === 'object' && item.id && item.name);\n      }\n\n      console.log('Données de catégories nettoyées:', cleanedData);\n      return { data: cleanedData };\n    } catch (error) {\n      console.error('Erreur lors de la récupération des catégories:', error);\n      return { data: [] }; // Retourner un tableau vide en cas d'erreur\n    }\n  },\n  create: (data) => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000,\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour de livre:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 30000,\n    });\n  },\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: (params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, { params }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.EBOOKS, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  getCategories: async () => {\n    try {\n      const response = await api.get(API_CONFIG.ENDPOINTS.CATEGORIES);\n      console.log('Réponse des catégories (ebooks):', response.data);\n\n      // Vérifier et nettoyer les données\n      let cleanedData = [];\n\n      if (Array.isArray(response.data)) {\n        // Si c'est déjà un tableau, filtrer les éléments invalides\n        cleanedData = response.data.filter(item => item && typeof item === 'object' && item.id && item.name);\n      } else if (response.data && typeof response.data === 'object') {\n        // Si c'est un objet, le convertir en tableau et filtrer\n        const values = Object.values(response.data);\n        cleanedData = values.filter(item => item && typeof item === 'object' && item.id && item.name);\n      }\n\n      console.log('Données de catégories nettoyées (ebooks):', cleanedData);\n      return { data: cleanedData };\n    } catch (error) {\n      console.error('Erreur lors de la récupération des catégories (ebooks):', error);\n      return { data: [] }; // Retourner un tableau vide en cas d'erreur\n    }\n  },\n  create: (data) => {\n    console.log('Envoi de données ebook au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.EBOOKS, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000,\n    });\n  },\n  update: (id, data) => {\n    console.log('Mise à jour d\\'ebook:', id, data);\n    return api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 30000,\n    });\n  },\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: (id) => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: (id) => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: (data) => {\n    console.log('Début de la mise à jour du profil');\n    console.log('URL:', `${API_CONFIG.ENDPOINTS.UTILISATEURS}update-profile/`);\n    console.log('Données envoyées:', data);\n\n    // Afficher le contenu du FormData\n    if (data instanceof FormData) {\n      console.log('FormData contenu:');\n      for (let pair of data.entries()) {\n        console.log(`${pair[0]}: ${pair[1]}`);\n      }\n    }\n\n    return api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}update-profile/`, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 30000, // Timeout plus long pour l'upload des fichiers\n    })\n    .then(response => {\n      console.log('Réponse de mise à jour du profil:', response);\n      return response;\n    })\n    .catch(error => {\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      console.error('Détails de l\\'erreur:', error.response?.data);\n      throw error;\n    });\n  },\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;;IAErC;IACAW,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,MAAM,CAACQ,GAAG,CAAC;IAC7CF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,MAAM,CAACS,MAAM,CAAC;IACtCH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,MAAM,CAACL,OAAO,CAAC;EACzC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAU,KAAK,IAAK;EACTJ,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;EAC/D,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAnB,GAAG,CAACM,YAAY,CAACgB,QAAQ,CAACd,GAAG,CAC1Bc,QAAQ,IAAK;EACZ;EACAP,OAAO,CAACC,GAAG,CAAC,sBAAsBM,QAAQ,CAACb,MAAM,CAACQ,GAAG,GAAG,EAAEK,QAAQ,CAACT,IAAI,CAAC;EACxE,OAAOS,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,MAAMI,aAAa,GAAG;IACpBC,OAAO,EAAE,yBAAyB;IAClCC,aAAa,EAAEN,KAAK;IACpBO,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,IAAI,CAACT,KAAK,CAACG,QAAQ,EAAE;IACnBC,aAAa,CAACM,IAAI,GAAG,SAAS;IAC9BN,aAAa,CAACC,OAAO,GAAG,oEAAoE;IAC5FT,OAAO,CAACI,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACxC;EACA;EAAA,KACK;IACHI,aAAa,CAACO,MAAM,GAAGX,KAAK,CAACG,QAAQ,CAACQ,MAAM;IAC5CP,aAAa,CAACV,IAAI,GAAGM,KAAK,CAACG,QAAQ,CAACT,IAAI;;IAExC;IACAE,OAAO,CAACI,KAAK,CAAC,eAAeA,KAAK,CAACG,QAAQ,CAACQ,MAAM,SAASX,KAAK,CAACV,MAAM,CAACQ,GAAG,GAAG,EAAE;MAC9Ea,MAAM,EAAEX,KAAK,CAACG,QAAQ,CAACQ,MAAM;MAC7BC,UAAU,EAAEZ,KAAK,CAACG,QAAQ,CAACS,UAAU;MACrClB,IAAI,EAAEM,KAAK,CAACG,QAAQ,CAACT,IAAI;MACzBT,OAAO,EAAEe,KAAK,CAACG,QAAQ,CAAClB,OAAO;MAC/BK,MAAM,EAAE;QACNQ,GAAG,EAAEE,KAAK,CAACV,MAAM,CAACQ,GAAG;QACrBC,MAAM,EAAEC,KAAK,CAACV,MAAM,CAACS,MAAM;QAC3Bd,OAAO,EAAEe,KAAK,CAACV,MAAM,CAACL,OAAO;QAC7BS,IAAI,EAAEM,KAAK,CAACV,MAAM,CAACI,IAAI,YAAYC,QAAQ,GAAG,2BAA2B,GAAGK,KAAK,CAACV,MAAM,CAACI;MAC3F;IACF,CAAC,CAAC;;IAEF;IACA,IAAIM,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACjCP,aAAa,CAACM,IAAI,GAAG,MAAM;MAC3BN,aAAa,CAACC,OAAO,GAAG,6CAA6C;MACrE;MACAb,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IACA;IAAA,KACK,IAAIhB,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACtCP,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,iEAAiE;IAC3F;IACA;IAAA,KACK,IAAIL,KAAK,CAACG,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;MACtCP,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,sCAAsC;;MAE9D;MACA,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC3C,MAAMuB,aAAa,GAAG,EAAE;QACxBC,MAAM,CAACC,OAAO,CAACnB,KAAK,CAACG,QAAQ,CAACT,IAAI,CAAC,CAAC0B,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;UACjE,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;YAC3BL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,KAAKC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UACxD,CAAC,MAAM,IAAI,OAAOJ,QAAQ,KAAK,QAAQ,EAAE;YACvCL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,KAAKC,QAAQ,EAAE,CAAC;UAC7C,CAAC,MAAM;YACLL,aAAa,CAACQ,IAAI,CAAC,GAAGJ,KAAK,wBAAwB,CAAC;UACtD;QACF,CAAC,CAAC;QAEF,IAAIJ,aAAa,CAACU,MAAM,GAAG,CAAC,EAAE;UAC5BvB,aAAa,CAACC,OAAO,GAAG,0BAA0BY,aAAa,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9E;MACF;IACF;IACA;IAAA,KACK,IAAI1B,KAAK,CAACG,QAAQ,CAACQ,MAAM,IAAI,GAAG,EAAE;MACrCP,aAAa,CAACM,IAAI,GAAG,QAAQ;MAC7BN,aAAa,CAACC,OAAO,GAAG,uEAAuE;IACjG;EACF;;EAEA;EACAT,OAAO,CAACI,KAAK,CAAC,eAAeI,aAAa,CAACC,OAAO,EAAE,EAAED,aAAa,CAAC;EAEpE,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;AACtC,CACF,CAAC;;AAED;AACA,OAAO,MAAMwB,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACC,MAAM,EAAE;IAAEH;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAChEC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;MAC/DzC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,QAAQ,CAACT,IAAI,CAAC;;MAErD;MACA,IAAI4C,WAAW,GAAG,EAAE;MAEpB,IAAIf,KAAK,CAACC,OAAO,CAACrB,QAAQ,CAACT,IAAI,CAAC,EAAE;QAChC;QACA4C,WAAW,GAAGnC,QAAQ,CAACT,IAAI,CAAC6C,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACC,IAAI,CAAC;MACtG,CAAC,MAAM,IAAItC,QAAQ,CAACT,IAAI,IAAI,OAAOS,QAAQ,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC7D;QACA,MAAMgD,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACvC,QAAQ,CAACT,IAAI,CAAC;QAC3C4C,WAAW,GAAGI,MAAM,CAACH,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACC,IAAI,CAAC;MAC/F;MAEA7C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyC,WAAW,CAAC;MAC5D,OAAO;QAAE5C,IAAI,EAAE4C;MAAY,CAAC;IAC9B,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE,OAAO;QAAEN,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;IACvB;EACF,CAAC;EACDZ,MAAM,EAAGY,IAAI,IAAK;IAChBE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEH,IAAI,CAAC;IACjD;IACA,OAAOb,GAAG,CAAC8D,IAAI,CAAC/D,UAAU,CAACoD,SAAS,CAACC,MAAM,EAAEvC,IAAI,EAAE;MACjDT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD;MACA2D,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,EAAEA,CAACV,EAAE,EAAEzC,IAAI,KAAK;IACpBE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,EAAE,EAAEzC,IAAI,CAAC;IAC9C,OAAOb,GAAG,CAACiE,GAAG,CAAC,GAAGlE,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,EAAEzC,IAAI,EAAE;MAC3DT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD2D,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDG,MAAM,EAAGZ,EAAE,IAAKtD,GAAG,CAACkE,MAAM,CAAC,GAAGnE,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAClEa,SAAS,EAAGb,EAAE,IAAKtD,GAAG,CAAC8D,IAAI,CAAC,GAAG/D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,aAAa,CAAC;EAC7Ec,QAAQ,EAAGd,EAAE,IAAKtD,GAAG,CAAC8D,IAAI,CAAC,GAAG/D,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,YAAY,CAAC;EAC3Ee,kBAAkB,EAAGpB,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,kBAAkB,EAAE;IAAEH;EAAO,CAAC,CAAC;EACrGqB,UAAU,EAAEA,CAAChB,EAAE,EAAEL,MAAM,KAAKjD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACC,MAAM,GAAGE,EAAE,WAAW,EAAE;IAAEL;EAAO,CAAC;AAChG,CAAC;;AAED;AACA,OAAO,MAAMsB,SAAS,GAAG;EACvBvB,MAAM,EAAGC,MAAM,IAAKjD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACqB,MAAM,EAAE;IAAEvB;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACqB,MAAM,GAAGlB,EAAE,GAAG,CAAC;EAChEC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;MAC/DzC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEM,QAAQ,CAACT,IAAI,CAAC;;MAE9D;MACA,IAAI4C,WAAW,GAAG,EAAE;MAEpB,IAAIf,KAAK,CAACC,OAAO,CAACrB,QAAQ,CAACT,IAAI,CAAC,EAAE;QAChC;QACA4C,WAAW,GAAGnC,QAAQ,CAACT,IAAI,CAAC6C,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACC,IAAI,CAAC;MACtG,CAAC,MAAM,IAAItC,QAAQ,CAACT,IAAI,IAAI,OAAOS,QAAQ,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC7D;QACA,MAAMgD,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACvC,QAAQ,CAACT,IAAI,CAAC;QAC3C4C,WAAW,GAAGI,MAAM,CAACH,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACC,IAAI,CAAC;MAC/F;MAEA7C,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyC,WAAW,CAAC;MACrE,OAAO;QAAE5C,IAAI,EAAE4C;MAAY,CAAC;IAC9B,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E,OAAO;QAAEN,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;IACvB;EACF,CAAC;EACDZ,MAAM,EAAGY,IAAI,IAAK;IAChBE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEH,IAAI,CAAC;IACvD;IACA,OAAOb,GAAG,CAAC8D,IAAI,CAAC/D,UAAU,CAACoD,SAAS,CAACqB,MAAM,EAAE3D,IAAI,EAAE;MACjDT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD;MACA2D,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,EAAEA,CAACV,EAAE,EAAEzC,IAAI,KAAK;IACpBE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,EAAE,EAAEzC,IAAI,CAAC;IAC9C,OAAOb,GAAG,CAACiE,GAAG,CAAC,GAAGlE,UAAU,CAACoD,SAAS,CAACqB,MAAM,GAAGlB,EAAE,GAAG,EAAEzC,IAAI,EAAE;MAC3DT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD2D,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDG,MAAM,EAAGZ,EAAE,IAAKtD,GAAG,CAACkE,MAAM,CAAC,GAAGnE,UAAU,CAACoD,SAAS,CAACqB,MAAM,GAAGlB,EAAE,GAAG;AACnE,CAAC;;AAED;AACA,OAAO,MAAMmB,aAAa,GAAG;EAC3BzB,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACK,UAAU,CAAC;EACtDH,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG,CAAC;EACpErD,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAAC8D,IAAI,CAAC/D,UAAU,CAACoD,SAAS,CAACK,UAAU,EAAE3C,IAAI,CAAC;EACjEmD,MAAM,EAAEA,CAACV,EAAE,EAAEzC,IAAI,KAAKb,GAAG,CAACiE,GAAG,CAAC,GAAGlE,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG,EAAEzC,IAAI,CAAC;EAC/EqD,MAAM,EAAGZ,EAAE,IAAKtD,GAAG,CAACkE,MAAM,CAAC,GAAGnE,UAAU,CAACoD,SAAS,CAACK,UAAU,GAAGF,EAAE,GAAG;AACvE,CAAC;;AAED;AACA,OAAO,MAAMoB,WAAW,GAAG;EACzB1B,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACwB,QAAQ,CAAC;EACpDtB,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAACwB,QAAQ,GAAGrB,EAAE,GAAG,CAAC;EAClEsB,SAAS,EAAGtB,EAAE,IAAKtD,GAAG,CAAC8D,IAAI,CAAC,GAAG/D,UAAU,CAACoD,SAAS,CAACwB,QAAQ,GAAGrB,EAAE,aAAa;AAChF,CAAC;;AAED;AACA,OAAO,MAAMuB,eAAe,GAAG;EAC7B7B,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAAC2B,YAAY,CAAC;EACxDzB,OAAO,EAAGC,EAAE,IAAKtD,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAAC2B,YAAY,GAAGxB,EAAE,GAAG,CAAC;EACtEyB,OAAO,EAAGzB,EAAE,IAAKtD,GAAG,CAACkE,MAAM,CAAC,GAAGnE,UAAU,CAACoD,SAAS,CAAC2B,YAAY,GAAGxB,EAAE,GAAG;AAC1E,CAAC;;AAED;AACA,OAAO,MAAM0B,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAMjF,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAAC+B,YAAY,cAAc,CAAC;EAC7EC,aAAa,EAAGtE,IAAI,IAAK;IACvBE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGjB,UAAU,CAACoD,SAAS,CAAC+B,YAAY,iBAAiB,CAAC;IAC1EnE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,IAAI,CAAC;;IAEtC;IACA,IAAIA,IAAI,YAAYC,QAAQ,EAAE;MAC5BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,KAAK,IAAIoE,IAAI,IAAIvE,IAAI,CAACyB,OAAO,CAAC,CAAC,EAAE;QAC/BvB,OAAO,CAACC,GAAG,CAAC,GAAGoE,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MACvC;IACF;IAEA,OAAOpF,GAAG,CAACiE,GAAG,CAAC,GAAGlE,UAAU,CAACoD,SAAS,CAAC+B,YAAY,iBAAiB,EAAErE,IAAI,EAAE;MAC1ET,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD2D,OAAO,EAAE,KAAK,CAAE;IAClB,CAAC,CAAC,CACDsB,IAAI,CAAC/D,QAAQ,IAAI;MAChBP,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEM,QAAQ,CAAC;MAC1D,OAAOA,QAAQ;IACjB,CAAC,CAAC,CACDgE,KAAK,CAACnE,KAAK,IAAI;MAAA,IAAAoE,eAAA;MACdxE,OAAO,CAACI,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,GAAAoE,eAAA,GAAEpE,KAAK,CAACG,QAAQ,cAAAiE,eAAA,uBAAdA,eAAA,CAAgB1E,IAAI,CAAC;MAC5D,MAAMM,KAAK;IACb,CAAC,CAAC;EACJ,CAAC;EACDqE,gBAAgB,EAAEA,CAAA,KAAMxF,GAAG,CAACkD,GAAG,CAAC,GAAGnD,UAAU,CAACoD,SAAS,CAAC+B,YAAY,gBAAgB,CAAC;EACrFO,oBAAoB,EAAGnC,EAAE,IAAKtD,GAAG,CAAC8D,IAAI,CAAC,GAAG/D,UAAU,CAACoD,SAAS,CAAC+B,YAAY,iBAAiB5B,EAAE,QAAQ;AACxG,CAAC;;AAED;AACA,OAAO,MAAMoC,aAAa,GAAG;EAC3B1C,MAAM,EAAEA,CAAA,KAAMhD,GAAG,CAACkD,GAAG,CAACnD,UAAU,CAACoD,SAAS,CAACwC,UAAU;AACvD,CAAC;AAED,eAAe3F,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}