{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\EbookCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookCard = ({\n  ebook\n}) => {\n  // Log des informations de l'ebook\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL correcte\n  const imageUrl = ebook.image ? config.getBookImageUrl(ebook.image) : ebook.static_image_url ? config.STATIC_URL + `images/ebooks/ebook_${ebook.id}.jpg` : config.DEFAULT_BOOK_IMAGE;\n  console.log(`EbookCard - URL d'image: ${imageUrl}`);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-card\",\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: `/ebooks/${ebook.id}`,\n      className: \"book-card-link\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-image\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: ebook.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-card-status green\",\n          children: ebook.format.toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"book-card-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-author\",\n          children: [\"Par \", ebook.autheur]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-category\",\n          children: ebook.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"book-card-availability\",\n          children: [\"Format: \", ebook.format.toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = EbookCard;\nexport default EbookCard;\nvar _c;\n$RefreshReg$(_c, \"EbookCard\");", "map": {"version": 3, "names": ["React", "Link", "config", "jsxDEV", "_jsxDEV", "EbookCard", "ebook", "console", "log", "id", "titre", "imageUrl", "image", "getBookImageUrl", "static_image_url", "STATIC_URL", "DEFAULT_BOOK_IMAGE", "className", "children", "to", "src", "alt", "onError", "e", "error", "target", "onerror", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "format", "toUpperCase", "autheur", "category_name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/EbookCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './BookCard.css'; // Réutilisation du même style\nimport config from '../config';\n\nconst EbookCard = ({ ebook }) => {\n  // Log des informations de l'ebook\n  console.log(`EbookCard - Ebook ID: ${ebook.id}, Titre: ${ebook.titre}`);\n\n  // Utiliser la fonction getBookImageUrl pour obtenir l'URL correcte\n  const imageUrl = ebook.image\n    ? config.getBookImageUrl(ebook.image)\n    : ebook.static_image_url\n      ? config.STATIC_URL + `images/ebooks/ebook_${ebook.id}.jpg`\n      : config.DEFAULT_BOOK_IMAGE;\n\n  console.log(`EbookCard - URL d'image: ${imageUrl}`);\n\n  return (\n    <div className=\"book-card\">\n      <Link to={`/ebooks/${ebook.id}`} className=\"book-card-link\">\n        <div className=\"book-card-image\">\n          <img\n            src={imageUrl}\n            alt={ebook.titre}\n            onError={(e) => {\n              console.error(`Erreur de chargement d'image: ${e.target.src}`);\n              e.target.onerror = null;\n              e.target.src = config.DEFAULT_BOOK_IMAGE;\n            }}\n          />\n          <div className=\"book-card-status green\">\n            {ebook.format.toUpperCase()}\n          </div>\n        </div>\n        <div className=\"book-card-content\">\n          <h3 className=\"book-card-title\">{ebook.titre}</h3>\n          <p className=\"book-card-author\">Par {ebook.autheur}</p>\n          <p className=\"book-card-category\">{ebook.category_name}</p>\n          <p className=\"book-card-availability\">\n            Format: {ebook.format.toUpperCase()}\n          </p>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\nexport default EbookCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB,CAAC,CAAC;AACzB,OAAOC,MAAM,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B;EACAC,OAAO,CAACC,GAAG,CAAC,yBAAyBF,KAAK,CAACG,EAAE,YAAYH,KAAK,CAACI,KAAK,EAAE,CAAC;;EAEvE;EACA,MAAMC,QAAQ,GAAGL,KAAK,CAACM,KAAK,GACxBV,MAAM,CAACW,eAAe,CAACP,KAAK,CAACM,KAAK,CAAC,GACnCN,KAAK,CAACQ,gBAAgB,GACpBZ,MAAM,CAACa,UAAU,GAAG,uBAAuBT,KAAK,CAACG,EAAE,MAAM,GACzDP,MAAM,CAACc,kBAAkB;EAE/BT,OAAO,CAACC,GAAG,CAAC,4BAA4BG,QAAQ,EAAE,CAAC;EAEnD,oBACEP,OAAA;IAAKa,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBd,OAAA,CAACH,IAAI;MAACkB,EAAE,EAAE,WAAWb,KAAK,CAACG,EAAE,EAAG;MAACQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBACzDd,OAAA;QAAKa,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9Bd,OAAA;UACEgB,GAAG,EAAET,QAAS;UACdU,GAAG,EAAEf,KAAK,CAACI,KAAM;UACjBY,OAAO,EAAGC,CAAC,IAAK;YACdhB,OAAO,CAACiB,KAAK,CAAC,iCAAiCD,CAAC,CAACE,MAAM,CAACL,GAAG,EAAE,CAAC;YAC9DG,CAAC,CAACE,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBH,CAAC,CAACE,MAAM,CAACL,GAAG,GAAGlB,MAAM,CAACc,kBAAkB;UAC1C;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF1B,OAAA;UAAKa,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCZ,KAAK,CAACyB,MAAM,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1B,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCd,OAAA;UAAIa,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEZ,KAAK,CAACI;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClD1B,OAAA;UAAGa,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,MAAI,EAACZ,KAAK,CAAC2B,OAAO;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvD1B,OAAA;UAAGa,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEZ,KAAK,CAAC4B;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3D1B,OAAA;UAAGa,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GAAC,UAC5B,EAACZ,KAAK,CAACyB,MAAM,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GAzCI9B,SAAS;AA2Cf,eAAeA,SAAS;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}