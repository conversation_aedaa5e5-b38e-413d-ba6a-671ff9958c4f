# RAPPORT DE PROJET DE FIN D'ANNÉE (PFA)

## SYSTÈME DE GESTION DE BIBLIOTHÈQUE - BiblioDesk

---

**Présenté par :** [Nom de l'étudiant]
**Encadré par :** [Nom de l'encadrant]
**Établissement :** [Nom de l'établissement]
**Année universitaire :** 2024-2025
**Filière :** Génie Informatique / Développement Web

---

## TABLE DES MATIÈRES

1. **INTRODUCTION GÉNÉRALE** .................................................... 4
2. **PRÉSENTATION GÉNÉRALE DU PROJET** ........................................ 5
3. **ANALYSE ET CONCEPTION** .................................................... 8
4. **RÉALISATION ET IMPLÉMENTATION** ........................................... 15
5. **TESTS ET VALIDATION** ..................................................... 22
6. **DÉPLOIEMENT ET MISE EN PRODUCTION** ....................................... 24
7. **CONCLUSION ET PERSPECTIVES** .............................................. 26
8. **ANNEXES** ................................................................. 28

---

## 1. INTRODUCTION GÉNÉRALE

### 1.1 Contexte du projet

Dans l'ère numérique actuelle, la gestion traditionnelle des bibliothèques fait face à de nombreux défis. Les méthodes manuelles de catalogage, d'emprunt et de suivi des livres deviennent obsolètes et inefficaces. Les utilisateurs modernes s'attendent à des services numériques rapides, intuitifs et accessibles 24h/24.

Ce projet de fin d'année s'inscrit dans cette démarche de modernisation des services bibliothécaires. BiblioDesk représente une solution complète de gestion de bibliothèque, alliant les technologies web modernes à une approche centrée utilisateur.

### 1.2 Problématique

Les bibliothèques traditionnelles rencontrent plusieurs problèmes majeurs :

- **Gestion manuelle inefficace** : Processus d'emprunt et de retour chronophages
- **Absence de traçabilité** : Difficulté de suivi des emprunts et des retards
- **Communication limitée** : Pas de système de notification automatique
- **Accès restreint** : Consultation du catalogue limitée aux heures d'ouverture
- **Gestion des ressources numériques** : Absence d'intégration des e-books
- **Statistiques inexistantes** : Manque d'outils d'analyse et de reporting

### 1.3 Objectifs du projet

#### Objectifs principaux :
- Développer une application web moderne de gestion de bibliothèque
- Automatiser les processus d'emprunt, de retour et de réservation
- Créer un système de notification intelligent
- Fournir une interface utilisateur intuitive et responsive
- Intégrer la gestion des livres physiques et numériques

#### Objectifs secondaires :
- Implémenter un système de statistiques avancées
- Optimiser l'expérience utilisateur
- Assurer la sécurité et la confidentialité des données
- Faciliter l'administration du système

### 1.4 Méthodologie adoptée

Le développement de BiblioDesk suit une approche agile avec les phases suivantes :

1. **Analyse des besoins** : Étude des exigences fonctionnelles et techniques
2. **Conception** : Modélisation UML et architecture système
3. **Développement itératif** : Implémentation par modules fonctionnels
4. **Tests continus** : Validation à chaque étape
5. **Déploiement** : Mise en production et documentation

---

## 2. PRÉSENTATION GÉNÉRALE DU PROJET

### 2.1 Description du projet

BiblioDesk est une application web complète de gestion de bibliothèque développée avec Django (backend) et React (frontend). Elle offre une solution moderne pour la gestion des ressources documentaires, des utilisateurs et des services bibliothécaires.

### 2.2 Fonctionnalités principales

#### 2.2.1 Gestion du catalogue
- **Livres physiques** : Ajout, modification, suppression avec gestion des stocks
- **E-books** : Catalogue numérique avec lecture en ligne
- **Catégorisation** : Organisation par catégories et sous-catégories
- **Recherche avancée** : Filtres multiples (titre, auteur, ISBN, catégorie)

#### 2.2.2 Système d'emprunt et de réservation
- **Emprunt automatisé** : Processus simplifié avec validation des disponibilités
- **Réservation intelligente** : File d'attente automatique pour les livres indisponibles
- **Gestion des retours** : Suivi automatique des dates de retour
- **Historique complet** : Traçabilité de tous les emprunts

#### 2.2.3 Gestion des utilisateurs
- **Authentification sécurisée** : Système de login avec vérification email
- **Profils utilisateurs** : Gestion des informations personnelles
- **Rôles et permissions** : Distinction administrateur/étudiant
- **Tableau de bord personnel** : Vue d'ensemble des emprunts en cours

#### 2.2.4 Système de notifications
- **Notifications in-app** : Alertes en temps réel dans l'interface
- **Emails automatiques** : Confirmations, rappels, alertes de retard
- **Types de notifications** :
  - Confirmation d'emprunt
  - Rappels de retour (2 jours avant échéance)
  - Alertes de retard
  - Disponibilité de livres réservés
  - Nouveaux livres ajoutés au catalogue

### 2.3 Architecture technique

#### 2.3.1 Architecture générale
```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Frontend      │ ←─────────────────→ │    Backend      │
│   React.js      │                     │    Django       │
│   - Components  │                     │   - Models      │
│   - Pages       │                     │   - Views       │
│   - Services    │                     │   - Serializers │
└─────────────────┘                     └─────────────────┘
                                                 │
                                                 ▼
                                        ┌─────────────────┐
                                        │   Base de       │
                                        │   données       │
                                        │   SQLite        │
                                        └─────────────────┘
```

#### 2.3.2 Technologies utilisées

**Backend (Django) :**
- Django 4.2 : Framework web Python
- Django REST Framework 3.14.0 : API REST
- SQLite : Base de données
- Pillow 10.1.0 : Traitement d'images
- SendGrid : Service d'envoi d'emails

**Frontend (React) :**
- React 18.2.0 : Bibliothèque JavaScript
- React Router 6.20.0 : Gestion du routage
- Bootstrap 5.3.2 : Framework CSS
- Axios 1.6.2 : Client HTTP
- React Icons 4.12.0 : Bibliothèque d'icônes

**Outils de développement :**
- CORS Headers : Gestion des requêtes cross-origin
- Python-dotenv : Variables d'environnement
- Node.js et npm : Environnement JavaScript

### 2.4 Acteurs du système

#### 2.4.1 Administrateur
**Responsabilités :**
- Gestion complète du catalogue de livres
- Administration des comptes utilisateurs
- Configuration du système
- Consultation des statistiques
- Gestion des notifications

**Fonctionnalités spécifiques :**
- Ajout/modification/suppression de livres
- Gestion des catégories
- Suivi des emprunts et retards
- Génération de rapports
- Configuration des paramètres système

#### 2.4.2 Étudiant/Utilisateur
**Responsabilités :**
- Consultation du catalogue
- Emprunt et réservation de livres
- Gestion de son profil personnel
- Respect des règles d'emprunt

**Fonctionnalités spécifiques :**
- Recherche et consultation de livres
- Emprunt de livres disponibles
- Réservation de livres indisponibles
- Consultation de l'historique personnel
- Lecture d'e-books en ligne

### 2.5 Contraintes et exigences

#### 2.5.1 Contraintes techniques
- **Performance** : Temps de réponse < 3 secondes
- **Compatibilité** : Support des navigateurs modernes
- **Responsive Design** : Adaptation mobile et tablette
- **Sécurité** : Protection des données personnelles
- **Scalabilité** : Architecture évolutive

#### 2.5.2 Contraintes fonctionnelles
- **Ergonomie** : Interface intuitive et moderne
- **Accessibilité** : Respect des standards d'accessibilité
- **Fiabilité** : Disponibilité 99% du temps
- **Maintenance** : Code documenté et maintenable

---

## 3. ANALYSE ET CONCEPTION

### 3.1 Analyse des besoins

#### 3.1.1 Besoins fonctionnels

**RF1 - Authentification et autorisation**
- Le système doit permettre l'authentification des utilisateurs
- Distinction entre les rôles administrateur et étudiant
- Vérification des emails lors de l'inscription
- Gestion sécurisée des sessions

**RF2 - Gestion du catalogue**
- Ajout, modification, suppression de livres (admin)
- Gestion des catégories de livres
- Upload et gestion des images de couverture
- Gestion des stocks et disponibilités

**RF3 - Système d'emprunt**
- Emprunt de livres disponibles
- Calcul automatique des dates de retour
- Vérification des disponibilités
- Limitation du nombre d'emprunts simultanés

**RF4 - Système de réservation**
- Réservation de livres indisponibles
- File d'attente automatique
- Notification lors de la disponibilité

**RF5 - Notifications**
- Notifications in-app en temps réel
- Emails automatiques (confirmations, rappels, retards)
- Personnalisation des types de notifications

**RF6 - Statistiques et rapports**
- Tableau de bord administrateur
- Statistiques d'utilisation
- Rapports d'emprunts et de retards
- Graphiques et visualisations

#### 3.1.2 Besoins non fonctionnels

**RNF1 - Performance**
- Temps de réponse inférieur à 3 secondes
- Support de 100 utilisateurs simultanés
- Optimisation des requêtes base de données

**RNF2 - Sécurité**
- Chiffrement des mots de passe
- Protection contre les attaques CSRF
- Validation des données côté serveur
- Gestion sécurisée des fichiers uploadés

**RNF3 - Utilisabilité**
- Interface intuitive et moderne
- Design responsive
- Navigation claire et logique
- Messages d'erreur explicites

**RNF4 - Fiabilité**
- Sauvegarde automatique des données
- Gestion des erreurs
- Logs détaillés pour le débogage
- Tests automatisés

### 3.2 Diagrammes de cas d'utilisation

#### 3.2.1 Cas d'utilisation - Étudiant

```
┌─────────────────────────────────────────────────────────────┐
│                    Système BiblioDesk                      │
│                                                             │
│  ┌─────────────────┐                                       │
│  │   S'authentifier │                                       │
│  └─────────────────┘                                       │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Consulter       │    │ Rechercher      │               │
│  │ catalogue       │    │ livres          │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Emprunter       │    │ Réserver        │               │
│  │ livre           │    │ livre           │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Consulter       │    │ Lire            │               │
│  │ profil          │    │ e-book          │               │
│  └─────────────────┘    └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
```

#### 3.2.2 Cas d'utilisation - Administrateur

```
┌─────────────────────────────────────────────────────────────┐
│                 Système BiblioDesk (Admin)                 │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Gérer           │    │ Gérer           │               │
│  │ catalogue       │    │ utilisateurs    │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Suivre          │    │ Consulter       │               │
│  │ emprunts        │    │ statistiques    │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ Gérer           │    │ Configurer      │               │
│  │ retards         │    │ notifications   │               │
│  └─────────────────┘    └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 Modélisation UML

#### 3.3.1 Diagramme de classes

```
┌─────────────────────┐     ┌─────────────────────┐
│       User          │     │    UserProfile      │
├─────────────────────┤     ├─────────────────────┤
│ - id: Integer       │ 1:1 │ - user_type: String │
│ - username: String  │────▶│ - photo: ImageField │
│ - email: String     │     │ - email_verified    │
│ - password: String  │     │ - messages_actions  │
└─────────────────────┘     └─────────────────────┘
           │                           │
           │ 1:n                       │ n:m
           ▼                           ▼
┌─────────────────────┐     ┌─────────────────────┐
│      Emprunt        │     │       Livres        │
├─────────────────────┤     ├─────────────────────┤
│ - date_emprunt      │ n:1 │ - titre: String     │
│ - date_retour_prevue│────▶│ - autheur: String   │
│ - date_retour       │     │ - price: Integer    │
│ - est_retourne      │     │ - quantitie_Total   │
└─────────────────────┘     │ - quantitie_Dispo   │
                            │ - desc: TextField   │
                            │ - isbn: String      │
                            │ - image: ImageField │
                            └─────────────────────┘
                                       │ n:1
                                       ▼
                            ┌─────────────────────┐
                            │    Cathegories      │
                            ├─────────────────────┤
                            │ - name: String      │
                            └─────────────────────┘
```

#### 3.3.2 Diagramme de séquence - Emprunt d'un livre

```
Utilisateur    Interface    API Django    Base de données    Système Email
    │              │            │               │                │
    │─────────────▶│            │               │                │
    │ Clic "Emprunter"          │               │                │
    │              │────────────▶│               │                │
    │              │ POST /emprunter            │                │
    │              │            │──────────────▶│                │
    │              │            │ Vérifier dispo│                │
    │              │            │◀──────────────│                │
    │              │            │               │                │
    │              │            │──────────────▶│                │
    │              │            │ Créer emprunt │                │
    │              │            │◀──────────────│                │
    │              │            │               │                │
    │              │            │──────────────▶│                │
    │              │            │ Maj quantité  │                │
    │              │            │◀──────────────│                │
    │              │            │               │                │
    │              │            │───────────────────────────────▶│
    │              │            │        Envoyer email           │
    │              │            │               │                │
    │              │◀───────────│               │                │
    │◀─────────────│ Confirmation              │                │
```

#### 3.3.3 Diagramme d'activité - Processus de notification

```
                    [Début]
                       │
                       ▼
              ┌─────────────────┐
              │ Vérification    │
              │ quotidienne     │
              │ des emprunts    │
              └─────────────────┘
                       │
                       ▼
              ┌─────────────────┐
              │ Identifier les  │
              │ emprunts en     │
              │ retard          │
              └─────────────────┘
                       │
                       ▼
                  ◊─────────◊
                 ╱ Retards   ╲
                ╱ détectés ? ╲
               ╱             ╲
              ◊───────────────◊
             ╱ Oui             ╲ Non
            ▼                   ▼
   ┌─────────────────┐    ┌─────────────────┐
   │ Envoyer         │    │ Vérifier        │
   │ notifications   │    │ rappels         │
   │ de retard       │    │ (2 jours avant) │
   └─────────────────┘    └─────────────────┘
            │                       │
            ▼                       ▼
   ┌─────────────────┐    ┌─────────────────┐
   │ Créer           │    │ Envoyer         │
   │ notification    │    │ rappels         │
   │ in-app          │    │ si nécessaire   │
   └─────────────────┘    └─────────────────┘
            │                       │
            └───────────┬───────────┘
                        ▼
                ┌─────────────────┐
                │ Mettre à jour   │
                │ logs et         │
                │ statistiques    │
                └─────────────────┘
                        │
                        ▼
                     [Fin]
```

### 3.4 Modélisation de la base de données

#### 3.4.1 Modèle Conceptuel de Données (MCD)

**Entités principales :**

1. **USER** (Utilisateur système Django)
   - id, username, email, password, first_name, last_name, is_active, date_joined

2. **USERPROFILE** (Profil étendu)
   - user_type, photo, email_verified, verification_token, messages_actions

3. **LIVRES** (Catalogue des livres physiques)
   - titre, autheur, price, quantitie_Total, quantitie_Dispo, desc, date_publication, isbn, url, image

4. **EBOOK** (Catalogue des livres numériques)
   - titre, autheur, description, date_publication, isbn, format, url, image, prix

5. **CATHEGORIES** (Catégories de livres)
   - name

6. **EMPRUNT** (Transactions d'emprunt)
   - date_emprunt, date_retour_prevue, date_retour, est_retourne

7. **RESERVATION** (Réservations de livres)
   - date_reservation, est_active

8. **NOTIFICATION** (Système de notifications)
   - type_notification, message, date_creation, est_lue

9. **EMAILLOG** (Journal des emails)
   - type_email, sujet, contenu, destinataires, date_envoi, statut, message_erreur

#### 3.4.2 Modèle Logique de Données (MLD)

**Relations identifiées :**

```sql
-- Table des utilisateurs (Django User)
User (id, username, email, password, first_name, last_name, is_active, date_joined)

-- Table des profils utilisateurs
UserProfile (id, #user_id, user_type, photo, email_verified, verification_token, messages_actions)

-- Table des catégories
Cathegories (id, name)

-- Table des livres
Livres (id, titre, autheur, #category_id, price, quantitie_Total, quantitie_Dispo,
        desc, date_publication, isbn, url, image, static_image_url)

-- Table des e-books
Ebook (id, titre, autheur, #category_id, description, date_publication, isbn,
       format, url, image, prix, date_ajout, static_image_url)

-- Table des emprunts
Emprunt (id, #livre_id, #utilisateur_id, date_emprunt, date_retour_prevue,
         date_retour, est_retourne)

-- Table des réservations
Reservation (id, #livre_id, #utilisateur_id, date_reservation, est_active)

-- Table des notifications
Notification (id, #utilisateur_id, type_notification, message, #livre_id,
              date_creation, est_lue)

-- Table des logs d'emails
EmailLog (id, type_email, sujet, contenu, destinataires, date_envoi,
          statut, message_erreur)

-- Table de liaison Many-to-Many (UserProfile ↔ Livres)
UserProfile_livres_empruntes (id, #userprofile_id, #livres_id)
```

**Contraintes d'intégrité :**

- **Clés primaires** : Chaque table possède un identifiant unique auto-incrémenté
- **Clés étrangères** : Relations référentielles avec CASCADE ou PROTECT selon le contexte
- **Contraintes métier** :
  - quantitie_Dispo ≤ quantitie_Total
  - date_retour_prevue > date_emprunt
  - email unique par utilisateur
  - ISBN unique par livre

#### 3.4.3 Dictionnaire de données

**Table LIVRES :**
| Champ | Type | Taille | Contraintes | Description |
|-------|------|--------|-------------|-------------|
| id | Integer | - | PK, Auto-increment | Identifiant unique |
| titre | CharField | 50 | NOT NULL | Titre du livre |
| autheur | CharField | 25 | NOT NULL | Auteur du livre |
| category_id | ForeignKey | - | NOT NULL | Référence vers Cathegories |
| price | PositiveInteger | - | NOT NULL | Prix du livre |
| quantitie_Total | PositiveInteger | - | Default=1 | Quantité totale |
| quantitie_Dispo | PositiveInteger | - | Default=quantitie_Total | Quantité disponible |
| desc | TextField | - | NOT NULL | Description |
| date_publication | DateTime | - | NOT NULL | Date de publication |
| isbn | CharField | 13 | Default='0000000000000' | Code ISBN |
| url | URLField | 200 | Default='https://www.google.com' | URL de référence |
| image | ImageField | - | NULL, upload_to='livres/' | Image de couverture |

**Table EMPRUNT :**
| Champ | Type | Taille | Contraintes | Description |
|-------|------|--------|-------------|-------------|
| id | Integer | - | PK, Auto-increment | Identifiant unique |
| livre_id | ForeignKey | - | NOT NULL | Référence vers Livres |
| utilisateur_id | ForeignKey | - | NOT NULL | Référence vers User |
| date_emprunt | DateTime | - | Auto_now_add | Date d'emprunt |
| date_retour_prevue | DateTime | - | NULL | Date de retour prévue |
| date_retour | DateTime | - | NULL | Date de retour effective |
| est_retourne | Boolean | - | Default=False | Statut de retour |

### 3.5 Architecture logicielle

#### 3.5.1 Architecture en couches

```
┌─────────────────────────────────────────────────────────────┐
│                    COUCHE PRÉSENTATION                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   React Pages   │  │   Components    │  │   Styles    │ │
│  │   - Home        │  │   - BookCard    │  │   - CSS     │ │
│  │   - Books       │  │   - Header      │  │   - Bootstrap│ │
│  │   - Profile     │  │   - Sidebar     │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼ HTTP/REST API
┌─────────────────────────────────────────────────────────────┐
│                     COUCHE SERVICES                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   API Views     │  │   Serializers   │  │   Utils     │ │
│  │   - LivreViewSet│  │   - LivreSerial │  │   - Email   │ │
│  │   - UserViewSet │  │   - UserSerial  │  │   - Notif   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼ ORM Django
┌─────────────────────────────────────────────────────────────┐
│                     COUCHE MÉTIER                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │     Models      │  │   Business      │  │   Signals   │ │
│  │   - Livres      │  │   Logic         │  │   - Post    │ │
│  │   - Emprunt     │  │   - Validation  │  │   Save      │ │
│  │   - User        │  │   - Calculs     │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼ SQL
┌─────────────────────────────────────────────────────────────┐
│                   COUCHE PERSISTANCE                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    SQLite       │  │   Migrations    │  │   Media     │ │
│  │   Database      │  │   - Schema      │  │   Files     │ │
│  │                 │  │   - Evolution   │  │   - Images  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 3.5.2 Patterns de conception utilisés

**1. Model-View-Controller (MVC) avec Django**
- **Models** : Gestion des données et logique métier
- **Views** : Traitement des requêtes et logique de présentation
- **Templates** : Présentation (pour les vues Django classiques)

**2. Component-Based Architecture (React)**
- Composants réutilisables et modulaires
- Props et state pour la gestion des données
- Hooks pour la logique métier

**3. Repository Pattern**
- Abstraction de l'accès aux données via l'ORM Django
- Séparation entre logique métier et persistance

**4. Observer Pattern**
- Système de notifications basé sur les signaux Django
- Réactivité de l'interface React avec les hooks

---

## 4. RÉALISATION ET IMPLÉMENTATION

### 4.1 Architecture technique détaillée

#### 4.1.1 Structure du projet

```
Gestion_bibliotheque/
├── Gestion_bibliotheque/          # Configuration Django principale
│   ├── __init__.py
│   ├── settings.py               # Configuration globale
│   ├── urls.py                   # Routage principal
│   ├── wsgi.py                   # Interface WSGI
│   └── views.py                  # Vues globales
├── livres/                       # Application gestion des livres
│   ├── migrations/               # Migrations base de données
│   ├── templates/                # Templates Django
│   ├── static/                   # Fichiers statiques
│   ├── models.py                 # Modèles de données
│   ├── views.py                  # Vues Django
│   ├── api_views.py              # Vues API REST
│   ├── serializers.py            # Sérialiseurs DRF
│   ├── urls.py                   # URLs Django
│   ├── api_urls.py               # URLs API
│   ├── utils.py                  # Fonctions utilitaires
│   └── tasks.py                  # Tâches Celery
├── utilisateurs/                 # Application gestion des utilisateurs
│   ├── migrations/
│   ├── templates/
│   ├── models.py
│   ├── views.py
│   ├── api_views.py
│   ├── serializers.py
│   ├── urls.py
│   ├── api_urls.py
│   └── utils.py
├── frontend/                     # Application React
│   ├── public/
│   ├── src/
│   │   ├── components/           # Composants réutilisables
│   │   ├── pages/                # Pages de l'application
│   │   ├── services/             # Services API
│   │   ├── context/              # Contextes React
│   │   ├── utils/                # Utilitaires
│   │   ├── App.js                # Composant principal
│   │   └── index.js              # Point d'entrée
│   ├── package.json              # Dépendances Node.js
│   └── build/                    # Build de production
├── media/                        # Fichiers uploadés
├── static/                       # Fichiers statiques collectés
├── templates/                    # Templates globaux
├── requirements.txt              # Dépendances Python
├── manage.py                     # Script de gestion Django
└── README.md                     # Documentation
```

### 4.2 Implémentation du Backend (Django)

#### 4.2.1 Configuration du projet

**settings.py - Configuration principale :**

```python
# Configuration email (SendGrid)
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'
EMAIL_HOST_PASSWORD = SENDGRID_API_KEY
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Applications installées
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'livres',                    # Application gestion des livres
    'utilisateurs',              # Application gestion des utilisateurs
    'rest_framework',            # Django REST Framework
    'rest_framework.authtoken',  # Authentification par tokens
    'corsheaders',               # Gestion CORS
]

# Configuration CORS
CORS_ALLOW_ALL_ORIGINS = DEBUG
CORS_ALLOWED_ORIGINS = [
    os.getenv('FRONTEND_URL', 'http://localhost:3000'),
    "http://127.0.0.1:3000",
]
CORS_ALLOW_CREDENTIALS = True
```

#### 4.2.2 Modèles de données

**livres/models.py - Modèles principaux :**

```python
class Livres(models.Model):
    titre = models.CharField(max_length=50)
    autheur = models.CharField(max_length=25)
    category = models.ForeignKey(cathegories, on_delete=models.CASCADE)
    price = models.PositiveIntegerField()
    quantitie_Total = models.PositiveIntegerField(default=1)
    quantitie_Dispo = models.PositiveIntegerField(default=quantitie_Total)
    desc = models.TextField()
    date_publication = models.DateTimeField()
    isbn = models.CharField(max_length=13, default='0000000000000')
    url = models.URLField(max_length=200, default='https://www.google.com')
    image = models.ImageField(null=True, blank=True, upload_to='livres/')
    static_image_url = models.CharField(max_length=255, null=True, blank=True)

    def statut_quantite(self):
        if self.quantitie_Dispo <= 10:
            return 'red'
        elif self.quantitie_Dispo <= 50:
            return 'orange'
        else:
            return 'green'

class Emprunt(models.Model):
    livre = models.ForeignKey(Livres, on_delete=models.CASCADE)
    utilisateur = models.ForeignKey(User, on_delete=models.CASCADE)
    date_emprunt = models.DateTimeField(auto_now_add=True)
    date_retour_prevue = models.DateTimeField(null=True, blank=True)
    date_retour = models.DateTimeField(null=True, blank=True)
    est_retourne = models.BooleanField(default=False)

    def est_en_retard(self):
        if not self.est_retourne and timezone.now() > self.date_retour_prevue:
            return True
        return False

class Notification(models.Model):
    TYPE_CHOICES = [
        ('retard', 'Retard de retour'),
        ('rappel', 'Rappel de retour (2 jours)'),
        ('nouveau', 'Nouveau livre'),
        ('emprunt', 'Emprunt effectué'),
        ('reservation', 'Réservation effectuée'),
        ('disponible', 'Le livre est disponible'),
        ('retour', 'Livre retourné')
    ]

    utilisateur = models.ForeignKey(User, on_delete=models.CASCADE)
    type_notification = models.CharField(max_length=20, choices=TYPE_CHOICES)
    message = models.TextField()
    livre = models.ForeignKey(Livres, on_delete=models.CASCADE, null=True, blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    est_lue = models.BooleanField(default=False)
```

#### 4.2.3 API REST avec Django REST Framework

**livres/api_views.py - ViewSets API :**

```python
class LivreViewSet(viewsets.ModelViewSet):
    queryset = Livres.objects.all()
    serializer_class = LivreSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['titre', 'autheur', 'category__name', 'isbn']
    ordering_fields = ['titre', 'autheur', 'date_publication']

    @action(detail=True, methods=['post'])
    def emprunter(self, request, pk=None):
        livre = self.get_object()
        user = request.user

        # Vérifications métier
        if livre.quantitie_Dispo <= 0:
            return Response(
                {"detail": "Ce livre n'est pas disponible."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Créer l'emprunt
        livre.quantitie_Dispo -= 1
        livre.save()

        emprunt = Emprunt.objects.create(
            livre=livre,
            utilisateur=user,
            date_retour_prevue=timezone.now() + timedelta(days=4)
        )

        # Notification
        Notification.objects.create(
            utilisateur=user,
            type_notification='emprunt',
            message=f"Vous avez emprunté le livre '{livre.titre}'",
            livre=livre
        )

        return Response(
            {"detail": f"Vous avez emprunté le livre '{livre.titre}' avec succès."},
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        # Statistiques générales
        total_livres = Livres.objects.count()
        valeur_totale = Livres.objects.aggregate(total=Sum('price'))['total'] or 0
        total_reservations = Reservation.objects.filter(est_active=True).count()

        # Top 10 des livres les plus empruntés
        top_livres = Emprunt.objects.values('livre__titre').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        return Response({
            'kpi': {
                'total_livres': total_livres,
                'valeur_totale': float(valeur_totale),
                'total_reservations': total_reservations,
            },
            'top_livres': {
                'labels': [item['livre__titre'] for item in top_livres],
                'values': [item['count'] for item in top_livres]
            }
        })
```

#### 4.2.4 Système de notifications

**livres/utils.py - Fonctions utilitaires :**

```python
def envoyer_notification_email_nouveau_livre(livre):
    """
    Envoie un email à tous les utilisateurs pour les informer
    qu'un nouveau livre a été ajouté à la bibliothèque
    """
    sujet = f"Nouveau livre disponible : {livre.titre}"

    context = {
        'titre': livre.titre,
        'auteur': livre.autheur,
        'categorie': livre.category.name,
        'description': livre.desc,
        'image_url': livre.image.url if livre.image else None,
    }

    # Récupérer tous les emails des utilisateurs actifs
    emails_destinataires = User.objects.filter(
        is_active=True
    ).values_list('email', flat=True)

    html_message = render_to_string('emails/nouveau_livre.html', context)
    text_message = strip_tags(html_message)

    try:
        send_mail(
            sujet,
            text_message,
            settings.DEFAULT_FROM_EMAIL,
            emails_destinataires,
            fail_silently=False,
            html_message=html_message
        )

        # Enregistrer le log de l'email
        EmailLog.objects.create(
            type_email='nouveau_livre',
            sujet=sujet,
            contenu=html_message,
            destinataires=', '.join(emails_destinataires),
            statut=True
        )

        return True
    except Exception as e:
        # Enregistrer le log d'erreur
        EmailLog.objects.create(
            type_email='nouveau_livre',
            sujet=sujet,
            contenu=html_message,
            destinataires=', '.join(emails_destinataires),
            statut=False,
            message_erreur=str(e)
        )

        return False

def envoyer_notification_retard(emprunt, jours_retard):
    """
    Envoie une notification de retard à l'utilisateur
    """
    sujet = f"RETARD : Votre livre '{emprunt.livre.titre}' est en retard de {jours_retard} jour(s)"

    context = {
        'utilisateur': emprunt.utilisateur.username,
        'titre': emprunt.livre.titre,
        'auteur': emprunt.livre.autheur,
        'date_emprunt': emprunt.date_emprunt.strftime('%d/%m/%Y'),
        'date_retour_prevue': emprunt.date_retour_prevue.strftime('%d/%m/%Y'),
        'jours_retard': jours_retard
    }

    html_message = render_to_string('emails/retard.html', context)
    text_message = strip_tags(html_message)

    try:
        send_mail(
            sujet,
            text_message,
            settings.DEFAULT_FROM_EMAIL,
            [emprunt.utilisateur.email],
            fail_silently=False,
            html_message=html_message
        )

        # Créer une notification dans le système
        Notification.objects.create(
            utilisateur=emprunt.utilisateur,
            type_notification='retard',
            message=f"RETARD : Votre livre '{emprunt.livre.titre}' est en retard de {jours_retard} jour(s)",
            livre=emprunt.livre
        )

        return True
    except Exception as e:
        return False
```

### 4.3 Implémentation du Frontend (React)

#### 4.3.1 Structure des composants React

**frontend/src/App.js - Composant principal :**

```javascript
import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import { AlertProvider, useAlert } from './context/AlertContext';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Books from './pages/Books';
import BookDetail from './pages/BookDetail';
import AddBook from './pages/AddBook';
import Statistics from './pages/Statistics';
import Profile from './pages/Profile';

// Components
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Loading from './components/Loading';

function AppContent() {
  const { isAuthenticated, loading, authError } = useAuth();
  const { showError } = useAlert();
  const [darkMode, setDarkMode] = useState(true);

  // Protected route component
  const ProtectedRoute = ({ children }) => {
    if (!isAuthenticated) {
      return <Navigate to="/login" />;
    }
    return children;
  };

  // Admin route component
  const AdminRoute = ({ children }) => {
    const { isAuthenticated, currentUser } = useAuth();

    if (!isAuthenticated) {
      return <Navigate to="/login" />;
    }

    const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;

    if (!isAdmin) {
      return <Navigate to="/" />;
    }

    return children;
  };

  return (
    <div className={`app-container ${darkMode ? '' : 'light'}`}>
      {isAuthenticated && <Sidebar />}
      <div className="app-content">
        <Header darkMode={darkMode} toggleDarkMode={toggleDarkMode} />

        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/books" element={<Books />} />
          <Route path="/books/:id" element={<BookDetail />} />
          <Route
            path="/add-book"
            element={
              <AdminRoute>
                <AddBook />
              </AdminRoute>
            }
          />
          <Route
            path="/statistics"
            element={
              <AdminRoute>
                <Statistics />
              </AdminRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </div>
  );
}

function App() {
  return (
    <AlertProvider>
      <AppContent />
    </AlertProvider>
  );
}

export default App;
```

#### 4.3.2 Gestion de l'état avec Context API

**frontend/src/context/AuthContext.js - Contexte d'authentification :**

```javascript
import React, { createContext, useContext, useState, useEffect } from 'react';
import { userAPI } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (token) {
        const response = await userAPI.getProfile();
        setCurrentUser(response.data);
        setIsAuthenticated(true);
      }
    } catch (error) {
      localStorage.removeItem('authToken');
      setIsAuthenticated(false);
      setCurrentUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      const response = await userAPI.login(credentials);
      const { token, user } = response.data;

      localStorage.setItem('authToken', token);
      setCurrentUser(user);
      setIsAuthenticated(true);
      setAuthError(null);

      return { success: true };
    } catch (error) {
      setAuthError(error.response?.data?.detail || 'Erreur de connexion');
      return { success: false, error: error.response?.data?.detail };
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    setCurrentUser(null);
    setAuthError(null);
  };

  const value = {
    isAuthenticated,
    currentUser,
    loading,
    authError,
    login,
    logout,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

#### 4.3.3 Services API

**frontend/src/services/api.js - Configuration des appels API :**

```javascript
import axios from 'axios';
import API_CONFIG from '../apiConfig';

// Configuration de base d'Axios
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Token ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API des livres
export const booksAPI = {
  getAll: (params = {}) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),
  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),
  create: (data) => api.post(API_CONFIG.ENDPOINTS.LIVRES, data),
  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),
  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),
  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),
  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),
  search: (query) => api.get(API_CONFIG.ENDPOINTS.LIVRES, {
    params: { search: query }
  }),
};

// API des utilisateurs
export const userAPI = {
  login: (credentials) => api.post(API_CONFIG.ENDPOINTS.LOGIN, credentials),
  register: (userData) => api.post(API_CONFIG.ENDPOINTS.REGISTER, userData),
  logout: () => api.post(API_CONFIG.ENDPOINTS.LOGOUT),
  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profile/`),
  updateProfile: (data) => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profile/`, data),
  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),
  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),
};

// API des statistiques
export const statisticsAPI = {
  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS),
};

export default api;
```

#### 4.3.4 Composants réutilisables

**frontend/src/components/BookCard.js - Carte de livre :**

```javascript
import React from 'react';
import { Card, Button, Badge } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useAlert } from '../context/AlertContext';
import { booksAPI } from '../services/api';
import config from '../config';
import './BookCard.css';

const BookCard = ({ book, onBookUpdate }) => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { showSuccess, showError } = useAlert();

  const handleEmprunter = async (e) => {
    e.stopPropagation();
    try {
      await booksAPI.emprunter(book.id);
      showSuccess(`Livre "${book.titre}" emprunté avec succès !`);
      if (onBookUpdate) onBookUpdate();
    } catch (error) {
      showError(error.response?.data?.detail || 'Erreur lors de l\'emprunt');
    }
  };

  const handleReserver = async (e) => {
    e.stopPropagation();
    try {
      await booksAPI.reserver(book.id);
      showSuccess(`Livre "${book.titre}" réservé avec succès !`);
      if (onBookUpdate) onBookUpdate();
    } catch (error) {
      showError(error.response?.data?.detail || 'Erreur lors de la réservation');
    }
  };

  const getImageUrl = () => {
    if (book.image) {
      return book.image.startsWith('http') ? book.image : `${config.API_BASE_URL}${book.image}`;
    }
    return config.DEFAULT_BOOK_IMAGE;
  };

  const getStatusBadge = () => {
    if (book.quantitie_Dispo > 0) {
      return <Badge bg="success">Disponible ({book.quantitie_Dispo})</Badge>;
    } else {
      return <Badge bg="danger">Indisponible</Badge>;
    }
  };

  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;

  return (
    <Card className="book-card h-100" onClick={() => navigate(`/books/${book.id}`)}>
      <div className="book-image-container">
        <Card.Img
          variant="top"
          src={getImageUrl()}
          alt={book.titre}
          className="book-image"
          onError={(e) => {
            e.target.src = config.DEFAULT_BOOK_IMAGE;
          }}
        />
        <div className="book-status-overlay">
          {getStatusBadge()}
        </div>
      </div>

      <Card.Body className="d-flex flex-column">
        <Card.Title className="book-title">{book.titre}</Card.Title>
        <Card.Subtitle className="mb-2 text-muted">par {book.autheur}</Card.Subtitle>
        <Card.Text className="book-category">
          <small className="text-muted">{book.category_name}</small>
        </Card.Text>
        <Card.Text className="book-price">
          <strong>{book.price} DA</strong>
        </Card.Text>

        <div className="mt-auto">
          {book.quantitie_Dispo > 0 ? (
            <Button
              variant="primary"
              size="sm"
              onClick={handleEmprunter}
              className="w-100 mb-2"
            >
              Emprunter
            </Button>
          ) : (
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={handleReserver}
              className="w-100 mb-2"
            >
              Réserver
            </Button>
          )}

          {isAdmin && (
            <Button
              variant="outline-primary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/books/${book.id}/edit`);
              }}
              className="w-100"
            >
              Modifier
            </Button>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default BookCard;
```

### 4.4 Fonctionnalités avancées implémentées

#### 4.4.1 Système de recherche et filtrage

**Recherche avancée côté backend :**

```python
# Dans livres/api_views.py
class LivreViewSet(viewsets.ModelViewSet):
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ['titre', 'autheur', 'category__name', 'isbn', 'desc']
    ordering_fields = ['titre', 'autheur', 'date_publication', 'price']
    filterset_fields = ['category', 'quantitie_Dispo']

    def get_queryset(self):
        queryset = Livres.objects.all()

        # Filtrage par disponibilité
        disponible = self.request.query_params.get('disponible', None)
        if disponible is not None:
            if disponible.lower() == 'true':
                queryset = queryset.filter(quantitie_Dispo__gt=0)
            else:
                queryset = queryset.filter(quantitie_Dispo=0)

        # Filtrage par prix
        prix_min = self.request.query_params.get('prix_min', None)
        prix_max = self.request.query_params.get('prix_max', None)
        if prix_min is not None:
            queryset = queryset.filter(price__gte=prix_min)
        if prix_max is not None:
            queryset = queryset.filter(price__lte=prix_max)

        return queryset
```

**Composant de recherche avancée React :**

```javascript
// frontend/src/components/AdvancedSearch.js
import React, { useState, useEffect } from 'react';
import { Form, Row, Col, Button, Accordion } from 'react-bootstrap';

const AdvancedSearch = ({ onSearch, categories }) => {
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    disponible: '',
    prix_min: '',
    prix_max: '',
    ordering: 'titre'
  });

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSearch = () => {
    const activeFilters = Object.entries(filters)
      .filter(([key, value]) => value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    onSearch(activeFilters);
  };

  const handleReset = () => {
    setFilters({
      search: '',
      category: '',
      disponible: '',
      prix_min: '',
      prix_max: '',
      ordering: 'titre'
    });
    onSearch({});
  };

  return (
    <Accordion className="mb-4">
      <Accordion.Item eventKey="0">
        <Accordion.Header>Recherche avancée</Accordion.Header>
        <Accordion.Body>
          <Form>
            <Row className="mb-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Recherche textuelle</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Titre, auteur, ISBN..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Catégorie</Form.Label>
                  <Form.Select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                  >
                    <option value="">Toutes les catégories</option>
                    {categories.map(cat => (
                      <option key={cat.id} value={cat.id}>{cat.name}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row className="mb-3">
              <Col md={4}>
                <Form.Group>
                  <Form.Label>Disponibilité</Form.Label>
                  <Form.Select
                    value={filters.disponible}
                    onChange={(e) => handleFilterChange('disponible', e.target.value)}
                  >
                    <option value="">Tous</option>
                    <option value="true">Disponible</option>
                    <option value="false">Indisponible</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group>
                  <Form.Label>Prix minimum</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="0"
                    value={filters.prix_min}
                    onChange={(e) => handleFilterChange('prix_min', e.target.value)}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group>
                  <Form.Label>Prix maximum</Form.Label>
                  <Form.Control
                    type="number"
                    placeholder="1000"
                    value={filters.prix_max}
                    onChange={(e) => handleFilterChange('prix_max', e.target.value)}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row className="mb-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Trier par</Form.Label>
                  <Form.Select
                    value={filters.ordering}
                    onChange={(e) => handleFilterChange('ordering', e.target.value)}
                  >
                    <option value="titre">Titre (A-Z)</option>
                    <option value="-titre">Titre (Z-A)</option>
                    <option value="autheur">Auteur (A-Z)</option>
                    <option value="-autheur">Auteur (Z-A)</option>
                    <option value="price">Prix (croissant)</option>
                    <option value="-price">Prix (décroissant)</option>
                    <option value="-date_publication">Plus récent</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <div className="d-flex gap-2">
              <Button variant="primary" onClick={handleSearch}>
                Rechercher
              </Button>
              <Button variant="outline-secondary" onClick={handleReset}>
                Réinitialiser
              </Button>
            </div>
          </Form>
        </Accordion.Body>
      </Accordion.Item>
    </Accordion>
  );
};

export default AdvancedSearch;
```

#### 4.4.2 Tableau de bord et statistiques

**Page des statistiques React :**

```javascript
// frontend/src/pages/Statistics.js
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Alert } from 'react-bootstrap';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import { statisticsAPI } from '../services/api';
import { useAlert } from '../context/AlertContext';
import Loading from '../components/Loading';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const Statistics = () => {
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const { showError } = useAlert();

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      const response = await statisticsAPI.getAll();
      setStatistics(response.data);
    } catch (error) {
      showError('Erreur lors du chargement des statistiques');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Loading fullScreen message="Chargement des statistiques..." />;
  }

  if (!statistics) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          Impossible de charger les statistiques.
        </Alert>
      </Container>
    );
  }

  const { kpi, top_livres, top_etudiants, emprunts_par_jour } = statistics;

  // Configuration du graphique des livres populaires
  const livresChartData = {
    labels: top_livres.labels,
    datasets: [
      {
        label: 'Nombre d\'emprunts',
        data: top_livres.values,
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Configuration du graphique des étudiants actifs
  const etudiantsChartData = {
    labels: top_etudiants.labels,
    datasets: [
      {
        label: 'Livres retournés',
        data: top_etudiants.retournes,
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
      {
        label: 'Livres non retournés',
        data: top_etudiants.non_retournes,
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Configuration du graphique en secteurs
  const statusChartData = {
    labels: ['Livres retournés', 'Livres non retournés'],
    datasets: [
      {
        data: [kpi.livres_retournes, kpi.livres_non_retournes],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 99, 132, 0.6)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Statistiques de la bibliothèque',
      },
    },
  };

  return (
    <Container fluid className="mt-4">
      <h1 className="mb-4">Tableau de bord - Statistiques</h1>

      {/* KPI Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <Card.Title className="text-primary">{kpi.total_livres}</Card.Title>
              <Card.Text>Total des livres</Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <Card.Title className="text-success">{kpi.valeur_totale.toLocaleString()} DA</Card.Title>
              <Card.Text>Valeur totale</Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <Card.Title className="text-warning">{kpi.total_reservations}</Card.Title>
              <Card.Text>Réservations actives</Card.Text>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <Card.Title className="text-info">{kpi.livres_retournes + kpi.livres_non_retournes}</Card.Title>
              <Card.Text>Total emprunts</Card.Text>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <h5>Top 10 des livres les plus empruntés</h5>
            </Card.Header>
            <Card.Body>
              <Bar data={livresChartData} options={chartOptions} />
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <h5>Statut des emprunts</h5>
            </Card.Header>
            <Card.Body>
              <Doughnut data={statusChartData} options={chartOptions} />
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col md={12}>
          <Card className="mb-4">
            <Card.Header>
              <h5>Top 10 des étudiants les plus actifs</h5>
            </Card.Header>
            <Card.Body>
              <Bar data={etudiantsChartData} options={chartOptions} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Statistics;
```

#### 4.4.3 Gestion des notifications en temps réel

**Composant de notifications React :**

```javascript
// frontend/src/components/NotificationCenter.js
import React, { useState, useEffect } from 'react';
import { Dropdown, Badge, ListGroup, Button } from 'react-bootstrap';
import { BsBell, BsBellFill } from 'react-icons/bs';
import { userAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      fetchNotifications();
      // Polling pour les nouvelles notifications
      const interval = setInterval(fetchNotifications, 30000); // Toutes les 30 secondes
      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  const fetchNotifications = async () => {
    try {
      const response = await userAPI.getNotifications();
      setNotifications(response.data);
      setUnreadCount(response.data.filter(n => !n.est_lue).length);
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await userAPI.markNotificationRead(notificationId);
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, est_lue: true } : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Erreur lors du marquage de la notification:', error);
    }
  };

  const markAllAsRead = async () => {
    setLoading(true);
    try {
      const unreadNotifications = notifications.filter(n => !n.est_lue);
      await Promise.all(
        unreadNotifications.map(n => userAPI.markNotificationRead(n.id))
      );
      setNotifications(prev => prev.map(n => ({ ...n, est_lue: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Erreur lors du marquage des notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'retard': return '⚠️';
      case 'rappel': return '⏰';
      case 'nouveau': return '📚';
      case 'emprunt': return '✅';
      case 'reservation': return '📋';
      case 'disponible': return '🔔';
      case 'retour': return '↩️';
      default: return '📢';
    }
  };

  const getNotificationVariant = (type) => {
    switch (type) {
      case 'retard': return 'danger';
      case 'rappel': return 'warning';
      case 'nouveau': return 'info';
      case 'emprunt': return 'success';
      case 'reservation': return 'primary';
      case 'disponible': return 'success';
      case 'retour': return 'secondary';
      default: return 'light';
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="link" className="position-relative p-2 border-0 bg-transparent">
        {unreadCount > 0 ? <BsBellFill size={20} /> : <BsBell size={20} />}
        {unreadCount > 0 && (
          <Badge
            bg="danger"
            pill
            className="position-absolute top-0 start-100 translate-middle"
            style={{ fontSize: '0.7rem' }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Dropdown.Toggle>

      <Dropdown.Menu style={{ width: '350px', maxHeight: '400px', overflowY: 'auto' }}>
        <Dropdown.Header className="d-flex justify-content-between align-items-center">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button
              variant="link"
              size="sm"
              onClick={markAllAsRead}
              disabled={loading}
              className="p-0"
            >
              Tout marquer comme lu
            </Button>
          )}
        </Dropdown.Header>

        {notifications.length === 0 ? (
          <Dropdown.ItemText className="text-center text-muted py-3">
            Aucune notification
          </Dropdown.ItemText>
        ) : (
          <ListGroup variant="flush">
            {notifications.slice(0, 10).map((notification) => (
              <ListGroup.Item
                key={notification.id}
                className={`border-0 ${!notification.est_lue ? 'bg-light' : ''}`}
                style={{ cursor: 'pointer' }}
                onClick={() => !notification.est_lue && markAsRead(notification.id)}
              >
                <div className="d-flex align-items-start">
                  <span className="me-2" style={{ fontSize: '1.2rem' }}>
                    {getNotificationIcon(notification.type_notification)}
                  </span>
                  <div className="flex-grow-1">
                    <div className="fw-bold small">
                      {notification.message}
                    </div>
                    <small className="text-muted">
                      {new Date(notification.date_creation).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </small>
                  </div>
                  {!notification.est_lue && (
                    <Badge bg={getNotificationVariant(notification.type_notification)} className="ms-2">
                      Nouveau
                    </Badge>
                  )}
                </div>
              </ListGroup.Item>
            ))}
          </ListGroup>
        )}

        {notifications.length > 10 && (
          <Dropdown.ItemText className="text-center">
            <small className="text-muted">
              Et {notifications.length - 10} autres notifications...
            </small>
          </Dropdown.ItemText>
        )}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default NotificationCenter;
```
