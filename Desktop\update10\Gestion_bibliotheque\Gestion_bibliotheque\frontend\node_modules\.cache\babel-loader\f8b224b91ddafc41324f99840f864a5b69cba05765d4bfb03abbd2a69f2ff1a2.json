{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\AddEbook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { categoriesAPI, ebooksAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport './AddBook.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddEbook = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    showSuccess,\n    showError\n  } = useAlert();\n  const [loading, setLoading] = useState(false);\n  const [categories, setCategories] = useState([]);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    description: '',\n    date_publication: '',\n    isbn: '',\n    format: 'pdf',\n    prix: '',\n    url: '',\n    image: null\n  });\n  const [imagePreview, setImagePreview] = useState(null);\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await categoriesAPI.getAll();\n        console.log('Réponse des catégories:', response);\n\n        // Vérifier la structure de la réponse et extraire le tableau de catégories\n        if (response && response.data) {\n          // Si response.data est un tableau\n          if (Array.isArray(response.data)) {\n            setCategories(response.data);\n          }\n          // Si response.data contient une propriété 'results' qui est un tableau\n          else if (response.data.results && Array.isArray(response.data.results)) {\n            setCategories(response.data.results);\n          }\n          // Si response.data est un objet avec des catégories\n          else if (typeof response.data === 'object') {\n            // Essayer de trouver un tableau dans la réponse\n            const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));\n            if (possibleArrays.length > 0) {\n              setCategories(possibleArrays[0]);\n            } else {\n              // Créer un tableau à partir des entrées de l'objet\n              const categoriesArray = Object.entries(response.data).map(([id, category]) => {\n                if (typeof category === 'object') {\n                  return {\n                    id: id,\n                    ...category\n                  };\n                } else {\n                  return {\n                    id: id,\n                    name: category\n                  };\n                }\n              });\n              setCategories(categoriesArray);\n            }\n          } else {\n            // Fallback: initialiser avec un tableau vide\n            console.error('Format de données de catégories non reconnu:', response.data);\n            setCategories([]);\n          }\n        } else {\n          // Aucune donnée reçue\n          console.error('Aucune donnée de catégories reçue');\n          setCategories([]);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des catégories:', error);\n        showError('Impossible de charger les catégories. Veuillez réessayer plus tard.');\n        setCategories([]);\n      }\n    };\n    fetchCategories();\n  }, [showError]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData({\n        ...formData,\n        image: file\n      });\n\n      // Créer un aperçu de l'image\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.category) {\n      showError('Veuillez sélectionner une catégorie');\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n\n      // Ajouter chaque champ au FormData avec des logs pour le débogage\n      Object.keys(formData).forEach(key => {\n        console.log(`Ajout du champ ${key} au FormData:`, formData[key]);\n        if (key === 'image' && formData[key]) {\n          formDataObj.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          // Convertir les nombres en chaînes pour éviter les problèmes\n          if (key === 'prix') {\n            formDataObj.append(key, String(formData[key]));\n          } else {\n            formDataObj.append(key, formData[key]);\n          }\n        }\n      });\n\n      // Log pour vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n\n      // Appel API avec gestion d'erreur détaillée\n      const response = await ebooksAPI.create(formDataObj);\n      console.log('Réponse de l\\'API:', response);\n      showSuccess('Ebook ajouté avec succès !');\n      navigate('/ebooks');\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de l\\'ebook:', error);\n\n      // Afficher des détails d'erreur plus précis\n      if (error.data && error.data.detail) {\n        showError(`Erreur: ${error.data.detail}`);\n      } else if (error.data) {\n        // Construire un message d'erreur à partir des erreurs de validation\n        const errorMessages = [];\n        Object.keys(error.data).forEach(key => {\n          errorMessages.push(`${key}: ${error.data[key]}`);\n        });\n        if (errorMessages.length > 0) {\n          showError(`Erreurs de validation: ${errorMessages.join(', ')}`);\n        } else {\n          showError('Erreur lors de l\\'ajout de l\\'ebook. Veuillez réessayer plus tard.');\n        }\n      } else {\n        showError('Erreur lors de l\\'ajout de l\\'ebook. Veuillez réessayer plus tard.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-book-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"add-book-title\",\n      children: \"Ajouter un ebook\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-book-card\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"add-book-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"titre\",\n              children: [\"Titre de l'ebook \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 55\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"titre\",\n              name: \"titre\",\n              value: formData.titre,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le titre de l'ebook\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"autheur\",\n              children: [\"Auteur \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"autheur\",\n              name: \"autheur\",\n              value: formData.autheur,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le nom de l'auteur\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: [\"Cat\\xE9gorie \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"S\\xE9lectionnez une cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), Array.isArray(categories) ? categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id || Math.random(), false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                disabled: true,\n                children: \"Chargement des cat\\xE9gories...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"format\",\n              children: [\"Format \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"format\",\n              name: \"format\",\n              value: formData.format,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"pdf\",\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"epub\",\n                children: \"EPUB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"mobi\",\n                children: \"MOBI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"prix\",\n              children: [\"Prix (MAD) \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 48\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"prix\",\n              name: \"prix\",\n              value: formData.prix,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le prix de l'ebook\",\n              min: \"0\",\n              step: \"0.01\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isbn\",\n              children: [\"ISBN \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"isbn\",\n              name: \"isbn\",\n              value: formData.isbn,\n              onChange: handleInputChange,\n              placeholder: \"Entrez l'ISBN de l'ebook\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"date_publication\",\n              children: [\"Date de publication \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 69\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"date_publication\",\n              name: \"date_publication\",\n              value: formData.date_publication,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"url\",\n              children: [\"URL de l'ebook \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              id: \"url\",\n              name: \"url\",\n              value: formData.url,\n              onChange: handleInputChange,\n              placeholder: \"Entrez l'URL de l'ebook\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: [\"Description \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 54\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            placeholder: \"Description de l'ebook\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: \"Image de couverture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            name: \"image\",\n            onChange: handleImageChange,\n            accept: \"image/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Aper\\xE7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"cancel-button\",\n            onClick: () => navigate('/ebooks'),\n            disabled: loading,\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            disabled: loading,\n            children: loading ? 'Ajout en cours...' : 'Ajouter l\\'ebook'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(AddEbook, \"S+s3gzXMkl/Qtb8pmp7QeT9mFBg=\", false, function () {\n  return [useNavigate, useAlert];\n});\n_c = AddEbook;\nexport default AddEbook;\nvar _c;\n$RefreshReg$(_c, \"AddEbook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "categoriesAPI", "ebooksAPI", "useAlert", "jsxDEV", "_jsxDEV", "AddEbook", "_s", "navigate", "showSuccess", "showError", "loading", "setLoading", "categories", "setCategories", "formData", "setFormData", "titre", "autheur", "category", "description", "date_publication", "isbn", "format", "prix", "url", "image", "imagePreview", "setImagePreview", "fetchCategories", "response", "getAll", "console", "log", "data", "Array", "isArray", "results", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "filter", "val", "length", "categoriesArray", "entries", "map", "id", "name", "error", "handleInputChange", "e", "value", "target", "handleImageChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formDataObj", "FormData", "keys", "for<PERSON>ach", "key", "append", "undefined", "String", "pair", "create", "detail", "errorMessages", "push", "join", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "onChange", "placeholder", "required", "Math", "random", "disabled", "min", "step", "accept", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/AddEbook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { categoriesAPI, ebooksAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport './AddBook.css';\n\nconst AddEbook = () => {\n  const navigate = useNavigate();\n  const { showSuccess, showError } = useAlert();\n  const [loading, setLoading] = useState(false);\n  const [categories, setCategories] = useState([]);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    description: '',\n    date_publication: '',\n    isbn: '',\n    format: 'pdf',\n    prix: '',\n    url: '',\n    image: null\n  });\n  const [imagePreview, setImagePreview] = useState(null);\n\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await categoriesAPI.getAll();\n        console.log('Réponse des catégories:', response);\n\n        // Vérifier la structure de la réponse et extraire le tableau de catégories\n        if (response && response.data) {\n          // Si response.data est un tableau\n          if (Array.isArray(response.data)) {\n            setCategories(response.data);\n          }\n          // Si response.data contient une propriété 'results' qui est un tableau\n          else if (response.data.results && Array.isArray(response.data.results)) {\n            setCategories(response.data.results);\n          }\n          // Si response.data est un objet avec des catégories\n          else if (typeof response.data === 'object') {\n            // Essayer de trouver un tableau dans la réponse\n            const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));\n            if (possibleArrays.length > 0) {\n              setCategories(possibleArrays[0]);\n            } else {\n              // Créer un tableau à partir des entrées de l'objet\n              const categoriesArray = Object.entries(response.data).map(([id, category]) => {\n                if (typeof category === 'object') {\n                  return { id: id, ...category };\n                } else {\n                  return { id: id, name: category };\n                }\n              });\n              setCategories(categoriesArray);\n            }\n          } else {\n            // Fallback: initialiser avec un tableau vide\n            console.error('Format de données de catégories non reconnu:', response.data);\n            setCategories([]);\n          }\n        } else {\n          // Aucune donnée reçue\n          console.error('Aucune donnée de catégories reçue');\n          setCategories([]);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des catégories:', error);\n        showError('Impossible de charger les catégories. Veuillez réessayer plus tard.');\n        setCategories([]);\n      }\n    };\n\n    fetchCategories();\n  }, [showError]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData({\n        ...formData,\n        image: file\n      });\n\n      // Créer un aperçu de l'image\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!formData.category) {\n      showError('Veuillez sélectionner une catégorie');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n\n      // Ajouter chaque champ au FormData avec des logs pour le débogage\n      Object.keys(formData).forEach(key => {\n        console.log(`Ajout du champ ${key} au FormData:`, formData[key]);\n\n        if (key === 'image' && formData[key]) {\n          formDataObj.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          // Convertir les nombres en chaînes pour éviter les problèmes\n          if (key === 'prix') {\n            formDataObj.append(key, String(formData[key]));\n          } else {\n            formDataObj.append(key, formData[key]);\n          }\n        }\n      });\n\n      // Log pour vérifier le contenu du FormData\n      for (let pair of formDataObj.entries()) {\n        console.log(`FormData contient: ${pair[0]}: ${pair[1]}`);\n      }\n\n      // Appel API avec gestion d'erreur détaillée\n      const response = await ebooksAPI.create(formDataObj);\n      console.log('Réponse de l\\'API:', response);\n\n      showSuccess('Ebook ajouté avec succès !');\n      navigate('/ebooks');\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout de l\\'ebook:', error);\n\n      // Afficher des détails d'erreur plus précis\n      if (error.data && error.data.detail) {\n        showError(`Erreur: ${error.data.detail}`);\n      } else if (error.data) {\n        // Construire un message d'erreur à partir des erreurs de validation\n        const errorMessages = [];\n        Object.keys(error.data).forEach(key => {\n          errorMessages.push(`${key}: ${error.data[key]}`);\n        });\n\n        if (errorMessages.length > 0) {\n          showError(`Erreurs de validation: ${errorMessages.join(', ')}`);\n        } else {\n          showError('Erreur lors de l\\'ajout de l\\'ebook. Veuillez réessayer plus tard.');\n        }\n      } else {\n        showError('Erreur lors de l\\'ajout de l\\'ebook. Veuillez réessayer plus tard.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"add-book-container\">\n      <h1 className=\"add-book-title\">Ajouter un ebook</h1>\n\n      <div className=\"add-book-card\">\n        <form className=\"add-book-form\" onSubmit={handleSubmit}>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"titre\">Titre de l'ebook <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"titre\"\n                name=\"titre\"\n                value={formData.titre}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le titre de l'ebook\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"autheur\">Auteur <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"autheur\"\n                name=\"autheur\"\n                value={formData.autheur}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le nom de l'auteur\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"category\">Catégorie <span className=\"required\">*</span></label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Sélectionnez une catégorie</option>\n                {Array.isArray(categories) ? (\n                  categories.map(category => (\n                    <option key={category.id || Math.random()} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))\n                ) : (\n                  <option value=\"\" disabled>Chargement des catégories...</option>\n                )}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"format\">Format <span className=\"required\">*</span></label>\n              <select\n                id=\"format\"\n                name=\"format\"\n                value={formData.format}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"pdf\">PDF</option>\n                <option value=\"epub\">EPUB</option>\n                <option value=\"mobi\">MOBI</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"prix\">Prix (MAD) <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"prix\"\n                name=\"prix\"\n                value={formData.prix}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le prix de l'ebook\"\n                min=\"0\"\n                step=\"0.01\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"isbn\">ISBN <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"isbn\"\n                name=\"isbn\"\n                value={formData.isbn}\n                onChange={handleInputChange}\n                placeholder=\"Entrez l'ISBN de l'ebook\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"date_publication\">Date de publication <span className=\"required\">*</span></label>\n              <input\n                type=\"date\"\n                id=\"date_publication\"\n                name=\"date_publication\"\n                value={formData.date_publication}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"url\">URL de l'ebook <span className=\"required\">*</span></label>\n              <input\n                type=\"url\"\n                id=\"url\"\n                name=\"url\"\n                value={formData.url}\n                onChange={handleInputChange}\n                placeholder=\"Entrez l'URL de l'ebook\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\">Description <span className=\"required\">*</span></label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              placeholder=\"Description de l'ebook\"\n              required\n            ></textarea>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"image\">Image de couverture</label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              name=\"image\"\n              onChange={handleImageChange}\n              accept=\"image/*\"\n            />\n            {imagePreview && (\n              <div className=\"image-preview\">\n                <img src={imagePreview} alt=\"Aperçu\" />\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"button\"\n              className=\"cancel-button\"\n              onClick={() => navigate('/ebooks')}\n              disabled={loading}\n            >\n              Annuler\n            </button>\n            <button\n              type=\"submit\"\n              className=\"submit-button\"\n              disabled={loading}\n            >\n              {loading ? 'Ajout en cours...' : 'Ajouter l\\'ebook'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddEbook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,EAAEC,SAAS,QAAQ,iBAAiB;AAC1D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,WAAW;IAAEC;EAAU,CAAC,GAAGP,QAAQ,CAAC,CAAC;EAC7C,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM7B,aAAa,CAAC8B,MAAM,CAAC,CAAC;QAC7CC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAAC;;QAEhD;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACI,IAAI,EAAE;UAC7B;UACA,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAAC,EAAE;YAChCpB,aAAa,CAACgB,QAAQ,CAACI,IAAI,CAAC;UAC9B;UACA;UAAA,KACK,IAAIJ,QAAQ,CAACI,IAAI,CAACG,OAAO,IAAIF,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC,EAAE;YACtEvB,aAAa,CAACgB,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;UACtC;UACA;UAAA,KACK,IAAI,OAAOP,QAAQ,CAACI,IAAI,KAAK,QAAQ,EAAE;YAC1C;YACA,MAAMI,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACV,QAAQ,CAACI,IAAI,CAAC,CAACO,MAAM,CAACC,GAAG,IAAIP,KAAK,CAACC,OAAO,CAACM,GAAG,CAAC,CAAC;YACrF,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;cAC7B7B,aAAa,CAACwB,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,MAAM;cACL;cACA,MAAMM,eAAe,GAAGL,MAAM,CAACM,OAAO,CAACf,QAAQ,CAACI,IAAI,CAAC,CAACY,GAAG,CAAC,CAAC,CAACC,EAAE,EAAE5B,QAAQ,CAAC,KAAK;gBAC5E,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;kBAChC,OAAO;oBAAE4B,EAAE,EAAEA,EAAE;oBAAE,GAAG5B;kBAAS,CAAC;gBAChC,CAAC,MAAM;kBACL,OAAO;oBAAE4B,EAAE,EAAEA,EAAE;oBAAEC,IAAI,EAAE7B;kBAAS,CAAC;gBACnC;cACF,CAAC,CAAC;cACFL,aAAa,CAAC8B,eAAe,CAAC;YAChC;UACF,CAAC,MAAM;YACL;YACAZ,OAAO,CAACiB,KAAK,CAAC,8CAA8C,EAAEnB,QAAQ,CAACI,IAAI,CAAC;YAC5EpB,aAAa,CAAC,EAAE,CAAC;UACnB;QACF,CAAC,MAAM;UACL;UACAkB,OAAO,CAACiB,KAAK,CAAC,mCAAmC,CAAC;UAClDnC,aAAa,CAAC,EAAE,CAAC;QACnB;MACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtEvC,SAAS,CAAC,qEAAqE,CAAC;QAChFI,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC;IAEDe,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnB,SAAS,CAAC,CAAC;EAEf,MAAMwC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEH,IAAI;MAAEI;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiC,IAAI,GAAGI;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,iBAAiB,GAAIH,CAAC,IAAK;IAC/B,MAAMI,IAAI,GAAGJ,CAAC,CAACE,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRvC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXW,KAAK,EAAE6B;MACT,CAAC,CAAC;;MAEF;MACA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvB/B,eAAe,CAAC6B,MAAM,CAACG,MAAM,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAAChD,QAAQ,CAACI,QAAQ,EAAE;MACtBT,SAAS,CAAC,qCAAqC,CAAC;MAChD;IACF;IAEA,IAAI;MACFE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMoD,WAAW,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAElC;MACA1B,MAAM,CAAC2B,IAAI,CAACnD,QAAQ,CAAC,CAACoD,OAAO,CAACC,GAAG,IAAI;QACnCpC,OAAO,CAACC,GAAG,CAAC,kBAAkBmC,GAAG,eAAe,EAAErD,QAAQ,CAACqD,GAAG,CAAC,CAAC;QAEhE,IAAIA,GAAG,KAAK,OAAO,IAAIrD,QAAQ,CAACqD,GAAG,CAAC,EAAE;UACpCJ,WAAW,CAACK,MAAM,CAACD,GAAG,EAAErD,QAAQ,CAACqD,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAIrD,QAAQ,CAACqD,GAAG,CAAC,KAAK,IAAI,IAAIrD,QAAQ,CAACqD,GAAG,CAAC,KAAKE,SAAS,EAAE;UAChE;UACA,IAAIF,GAAG,KAAK,MAAM,EAAE;YAClBJ,WAAW,CAACK,MAAM,CAACD,GAAG,EAAEG,MAAM,CAACxD,QAAQ,CAACqD,GAAG,CAAC,CAAC,CAAC;UAChD,CAAC,MAAM;YACLJ,WAAW,CAACK,MAAM,CAACD,GAAG,EAAErD,QAAQ,CAACqD,GAAG,CAAC,CAAC;UACxC;QACF;MACF,CAAC,CAAC;;MAEF;MACA,KAAK,IAAII,IAAI,IAAIR,WAAW,CAACnB,OAAO,CAAC,CAAC,EAAE;QACtCb,OAAO,CAACC,GAAG,CAAC,sBAAsBuC,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAM1C,QAAQ,GAAG,MAAM5B,SAAS,CAACuE,MAAM,CAACT,WAAW,CAAC;MACpDhC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;MAE3CrB,WAAW,CAAC,4BAA4B,CAAC;MACzCD,QAAQ,CAAC,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;MAE5D;MACA,IAAIA,KAAK,CAACf,IAAI,IAAIe,KAAK,CAACf,IAAI,CAACwC,MAAM,EAAE;QACnChE,SAAS,CAAC,WAAWuC,KAAK,CAACf,IAAI,CAACwC,MAAM,EAAE,CAAC;MAC3C,CAAC,MAAM,IAAIzB,KAAK,CAACf,IAAI,EAAE;QACrB;QACA,MAAMyC,aAAa,GAAG,EAAE;QACxBpC,MAAM,CAAC2B,IAAI,CAACjB,KAAK,CAACf,IAAI,CAAC,CAACiC,OAAO,CAACC,GAAG,IAAI;UACrCO,aAAa,CAACC,IAAI,CAAC,GAAGR,GAAG,KAAKnB,KAAK,CAACf,IAAI,CAACkC,GAAG,CAAC,EAAE,CAAC;QAClD,CAAC,CAAC;QAEF,IAAIO,aAAa,CAAChC,MAAM,GAAG,CAAC,EAAE;UAC5BjC,SAAS,CAAC,0BAA0BiE,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACjE,CAAC,MAAM;UACLnE,SAAS,CAAC,oEAAoE,CAAC;QACjF;MACF,CAAC,MAAM;QACLA,SAAS,CAAC,oEAAoE,CAAC;MACjF;IACF,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKyE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC1E,OAAA;MAAIyE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEpD9E,OAAA;MAAKyE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1E,OAAA;QAAMyE,SAAS,EAAC,eAAe;QAACM,QAAQ,EAAEtB,YAAa;QAAAiB,QAAA,gBACrD1E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,mBAAiB,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnF9E,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXvC,EAAE,EAAC,OAAO;cACVC,IAAI,EAAC,OAAO;cACZI,KAAK,EAAErC,QAAQ,CAACE,KAAM;cACtBsE,QAAQ,EAAErC,iBAAkB;cAC5BsC,WAAW,EAAC,4BAA4B;cACxCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,SAAS;cAAAN,QAAA,GAAC,SAAO,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E9E,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXvC,EAAE,EAAC,SAAS;cACZC,IAAI,EAAC,SAAS;cACdI,KAAK,EAAErC,QAAQ,CAACG,OAAQ;cACxBqE,QAAQ,EAAErC,iBAAkB;cAC5BsC,WAAW,EAAC,2BAA2B;cACvCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,UAAU;cAAAN,QAAA,GAAC,eAAU,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E9E,OAAA;cACE0C,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfI,KAAK,EAAErC,QAAQ,CAACI,QAAS;cACzBoE,QAAQ,EAAErC,iBAAkB;cAC5BuC,QAAQ;cAAAV,QAAA,gBAER1E,OAAA;gBAAQ+C,KAAK,EAAC,EAAE;gBAAA2B,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDhD,KAAK,CAACC,OAAO,CAACvB,UAAU,CAAC,GACxBA,UAAU,CAACiC,GAAG,CAAC3B,QAAQ,iBACrBd,OAAA;gBAA2C+C,KAAK,EAAEjC,QAAQ,CAAC4B,EAAG;gBAAAgC,QAAA,EAC3D5D,QAAQ,CAAC6B;cAAI,GADH7B,QAAQ,CAAC4B,EAAE,IAAI2C,IAAI,CAACC,MAAM,CAAC,CAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjC,CACT,CAAC,gBAEF9E,OAAA;gBAAQ+C,KAAK,EAAC,EAAE;gBAACwC,QAAQ;gBAAAb,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAC/D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,QAAQ;cAAAN,QAAA,GAAC,SAAO,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1E9E,OAAA;cACE0C,EAAE,EAAC,QAAQ;cACXC,IAAI,EAAC,QAAQ;cACbI,KAAK,EAAErC,QAAQ,CAACQ,MAAO;cACvBgE,QAAQ,EAAErC,iBAAkB;cAC5BuC,QAAQ;cAAAV,QAAA,gBAER1E,OAAA;gBAAQ+C,KAAK,EAAC,KAAK;gBAAA2B,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC9E,OAAA;gBAAQ+C,KAAK,EAAC,MAAM;gBAAA2B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC9E,OAAA;gBAAQ+C,KAAK,EAAC,MAAM;gBAAA2B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,aAAW,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5E9E,OAAA;cACEiF,IAAI,EAAC,QAAQ;cACbvC,EAAE,EAAC,MAAM;cACTC,IAAI,EAAC,MAAM;cACXI,KAAK,EAAErC,QAAQ,CAACS,IAAK;cACrB+D,QAAQ,EAAErC,iBAAkB;cAC5BsC,WAAW,EAAC,2BAA2B;cACvCK,GAAG,EAAC,GAAG;cACPC,IAAI,EAAC,MAAM;cACXL,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE9E,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXvC,EAAE,EAAC,MAAM;cACTC,IAAI,EAAC,MAAM;cACXI,KAAK,EAAErC,QAAQ,CAACO,IAAK;cACrBiE,QAAQ,EAAErC,iBAAkB;cAC5BsC,WAAW,EAAC,0BAA0B;cACtCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,kBAAkB;cAAAN,QAAA,GAAC,sBAAoB,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjG9E,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXvC,EAAE,EAAC,kBAAkB;cACrBC,IAAI,EAAC,kBAAkB;cACvBI,KAAK,EAAErC,QAAQ,CAACM,gBAAiB;cACjCkE,QAAQ,EAAErC,iBAAkB;cAC5BuC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1E,OAAA;cAAOgF,OAAO,EAAC,KAAK;cAAAN,QAAA,GAAC,iBAAe,eAAA1E,OAAA;gBAAMyE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E9E,OAAA;cACEiF,IAAI,EAAC,KAAK;cACVvC,EAAE,EAAC,KAAK;cACRC,IAAI,EAAC,KAAK;cACVI,KAAK,EAAErC,QAAQ,CAACU,GAAI;cACpB8D,QAAQ,EAAErC,iBAAkB;cAC5BsC,WAAW,EAAC,yBAAyB;cACrCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1E,OAAA;YAAOgF,OAAO,EAAC,aAAa;YAAAN,QAAA,GAAC,cAAY,eAAA1E,OAAA;cAAMyE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpF9E,OAAA;YACE0C,EAAE,EAAC,aAAa;YAChBC,IAAI,EAAC,aAAa;YAClBI,KAAK,EAAErC,QAAQ,CAACK,WAAY;YAC5BmE,QAAQ,EAAErC,iBAAkB;YAC5BsC,WAAW,EAAC,wBAAwB;YACpCC,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1E,OAAA;YAAOgF,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD9E,OAAA;YACEiF,IAAI,EAAC,MAAM;YACXvC,EAAE,EAAC,OAAO;YACVC,IAAI,EAAC,OAAO;YACZuC,QAAQ,EAAEjC,iBAAkB;YAC5ByC,MAAM,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EACDxD,YAAY,iBACXtB,OAAA;YAAKyE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1E,OAAA;cAAK2F,GAAG,EAAErE,YAAa;cAACsE,GAAG,EAAC;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1E,OAAA;YACEiF,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,eAAe;YACzBoB,OAAO,EAAEA,CAAA,KAAM1F,QAAQ,CAAC,SAAS,CAAE;YACnCoF,QAAQ,EAAEjF,OAAQ;YAAAoE,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA;YACEiF,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,eAAe;YACzBc,QAAQ,EAAEjF,OAAQ;YAAAoE,QAAA,EAEjBpE,OAAO,GAAG,mBAAmB,GAAG;UAAkB;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAvVID,QAAQ;EAAA,QACKN,WAAW,EACOG,QAAQ;AAAA;AAAAgG,EAAA,GAFvC7F,QAAQ;AAyVd,eAAeA,QAAQ;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}