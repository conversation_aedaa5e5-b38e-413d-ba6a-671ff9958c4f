{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\AddBook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { categoriesAPI, livresAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport './AddBook.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddBook = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    showSuccess,\n    showError\n  } = useAlert();\n  const [loading, setLoading] = useState(false);\n  const [categories, setCategories] = useState([]);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    image: null\n  });\n  const [imagePreview, setImagePreview] = useState(null);\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await categoriesAPI.getAll();\n        console.log('Réponse des catégories:', response);\n\n        // Vérifier la structure de la réponse et extraire le tableau de catégories\n        if (response && response.data) {\n          // Si response.data est un tableau\n          if (Array.isArray(response.data)) {\n            setCategories(response.data);\n          }\n          // Si response.data contient une propriété 'results' qui est un tableau\n          else if (response.data.results && Array.isArray(response.data.results)) {\n            setCategories(response.data.results);\n          }\n          // Si response.data est un objet avec des catégories\n          else if (typeof response.data === 'object') {\n            // Essayer de trouver un tableau dans la réponse\n            const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));\n            if (possibleArrays.length > 0) {\n              setCategories(possibleArrays[0]);\n            } else {\n              // Créer un tableau à partir des entrées de l'objet\n              const categoriesArray = Object.entries(response.data).map(([id, category]) => {\n                if (typeof category === 'object') {\n                  return {\n                    id: id,\n                    ...category\n                  };\n                } else {\n                  return {\n                    id: id,\n                    name: category\n                  };\n                }\n              });\n              setCategories(categoriesArray);\n            }\n          } else {\n            // Fallback: initialiser avec un tableau vide\n            console.error('Format de données de catégories non reconnu:', response.data);\n            setCategories([]);\n          }\n        } else {\n          // Aucune donnée reçue\n          console.error('Aucune donnée de catégories reçue');\n          setCategories([]);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des catégories:', error);\n        showError('Impossible de charger les catégories. Veuillez réessayer plus tard.');\n        setCategories([]);\n      }\n    };\n    fetchCategories();\n  }, [showError]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData({\n        ...formData,\n        image: file\n      });\n\n      // Créer un aperçu de l'image\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.category) {\n      showError('Veuillez sélectionner une catégorie');\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (key === 'image' && formData[key]) {\n          formDataObj.append(key, formData[key]);\n        } else if (formData[key]) {\n          formDataObj.append(key, formData[key]);\n        }\n      });\n      await livresAPI.create(formDataObj);\n      showSuccess('Livre ajouté avec succès !');\n      navigate('/books');\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout du livre:', error);\n      showError('Erreur lors de l\\'ajout du livre. Veuillez réessayer plus tard.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-book-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"add-book-title\",\n      children: \"Ajouter un livre\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-book-card\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"add-book-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"titre\",\n              children: [\"Titre du livre \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 53\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"titre\",\n              name: \"titre\",\n              value: formData.titre,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le titre du livre\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"autheur\",\n              children: [\"Auteur \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"autheur\",\n              name: \"autheur\",\n              value: formData.autheur,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le nom de l'auteur\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: [\"Cat\\xE9gorie \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"S\\xE9lectionnez une cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"quantitie_Total\",\n              children: [\"Quantit\\xE9 totale \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 64\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"quantitie_Total\",\n              name: \"quantitie_Total\",\n              value: formData.quantitie_Total,\n              onChange: handleInputChange,\n              placeholder: \"Entrez la quantit\\xE9 totale\",\n              min: \"1\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              children: [\"Prix (MAD) \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"price\",\n              name: \"price\",\n              value: formData.price,\n              onChange: handleInputChange,\n              placeholder: \"Entrez le prix du livre\",\n              min: \"0\",\n              step: \"0.01\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isbn\",\n              children: [\"ISBN \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"isbn\",\n              name: \"isbn\",\n              value: formData.isbn,\n              onChange: handleInputChange,\n              placeholder: \"Entrez l'ISBN du livre\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"date_publication\",\n              children: [\"Date de publication \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 69\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"date_publication\",\n              name: \"date_publication\",\n              value: formData.date_publication,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"desc\",\n            children: [\"Description \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 47\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"desc\",\n            name: \"desc\",\n            value: formData.desc,\n            onChange: handleInputChange,\n            placeholder: \"Description du livre\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: [\"Image du livre \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 51\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            name: \"image\",\n            onChange: handleImageChange,\n            accept: \"image/*\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Aper\\xE7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"cancel-button\",\n            onClick: () => navigate('/books'),\n            disabled: loading,\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            disabled: loading,\n            children: loading ? 'Ajout en cours...' : 'Ajouter le livre'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(AddBook, \"jy1JV+o4iuDJItfx1Om5WxyoltI=\", false, function () {\n  return [useNavigate, useAlert];\n});\n_c = AddBook;\nexport default AddBook;\nvar _c;\n$RefreshReg$(_c, \"AddBook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "categoriesAPI", "livresAPI", "useAlert", "jsxDEV", "_jsxDEV", "AddBook", "_s", "navigate", "showSuccess", "showError", "loading", "setLoading", "categories", "setCategories", "formData", "setFormData", "titre", "autheur", "category", "price", "quantitie_Total", "desc", "date_publication", "isbn", "image", "imagePreview", "setImagePreview", "fetchCategories", "response", "getAll", "console", "log", "data", "Array", "isArray", "results", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "filter", "val", "length", "categoriesArray", "entries", "map", "id", "name", "error", "handleInputChange", "e", "value", "target", "handleImageChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formDataObj", "FormData", "keys", "for<PERSON>ach", "key", "append", "create", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "onChange", "placeholder", "required", "min", "step", "accept", "src", "alt", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/AddBook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { categoriesAPI, livresAPI } from '../services/api';\nimport { useAlert } from '../context/AlertContext';\nimport './AddBook.css';\n\nconst AddBook = () => {\n  const navigate = useNavigate();\n  const { showSuccess, showError } = useAlert();\n  const [loading, setLoading] = useState(false);\n  const [categories, setCategories] = useState([]);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    price: '',\n    quantitie_Total: '',\n    desc: '',\n    date_publication: '',\n    isbn: '',\n    image: null\n  });\n  const [imagePreview, setImagePreview] = useState(null);\n\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await categoriesAPI.getAll();\n        console.log('Réponse des catégories:', response);\n\n        // Vérifier la structure de la réponse et extraire le tableau de catégories\n        if (response && response.data) {\n          // Si response.data est un tableau\n          if (Array.isArray(response.data)) {\n            setCategories(response.data);\n          }\n          // Si response.data contient une propriété 'results' qui est un tableau\n          else if (response.data.results && Array.isArray(response.data.results)) {\n            setCategories(response.data.results);\n          }\n          // Si response.data est un objet avec des catégories\n          else if (typeof response.data === 'object') {\n            // Essayer de trouver un tableau dans la réponse\n            const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));\n            if (possibleArrays.length > 0) {\n              setCategories(possibleArrays[0]);\n            } else {\n              // Créer un tableau à partir des entrées de l'objet\n              const categoriesArray = Object.entries(response.data).map(([id, category]) => {\n                if (typeof category === 'object') {\n                  return { id: id, ...category };\n                } else {\n                  return { id: id, name: category };\n                }\n              });\n              setCategories(categoriesArray);\n            }\n          } else {\n            // Fallback: initialiser avec un tableau vide\n            console.error('Format de données de catégories non reconnu:', response.data);\n            setCategories([]);\n          }\n        } else {\n          // Aucune donnée reçue\n          console.error('Aucune donnée de catégories reçue');\n          setCategories([]);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la récupération des catégories:', error);\n        showError('Impossible de charger les catégories. Veuillez réessayer plus tard.');\n        setCategories([]);\n      }\n    };\n\n    fetchCategories();\n  }, [showError]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData({\n        ...formData,\n        image: file\n      });\n\n      // Créer un aperçu de l'image\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!formData.category) {\n      showError('Veuillez sélectionner une catégorie');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les fichiers\n      const formDataObj = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (key === 'image' && formData[key]) {\n          formDataObj.append(key, formData[key]);\n        } else if (formData[key]) {\n          formDataObj.append(key, formData[key]);\n        }\n      });\n\n      await livresAPI.create(formDataObj);\n\n      showSuccess('Livre ajouté avec succès !');\n      navigate('/books');\n    } catch (error) {\n      console.error('Erreur lors de l\\'ajout du livre:', error);\n      showError('Erreur lors de l\\'ajout du livre. Veuillez réessayer plus tard.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"add-book-container\">\n      <h1 className=\"add-book-title\">Ajouter un livre</h1>\n\n      <div className=\"add-book-card\">\n        <form className=\"add-book-form\" onSubmit={handleSubmit}>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"titre\">Titre du livre <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"titre\"\n                name=\"titre\"\n                value={formData.titre}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le titre du livre\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"autheur\">Auteur <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"autheur\"\n                name=\"autheur\"\n                value={formData.autheur}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le nom de l'auteur\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"category\">Catégorie <span className=\"required\">*</span></label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Sélectionnez une catégorie</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"quantitie_Total\">Quantité totale <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"quantitie_Total\"\n                name=\"quantitie_Total\"\n                value={formData.quantitie_Total}\n                onChange={handleInputChange}\n                placeholder=\"Entrez la quantité totale\"\n                min=\"1\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"price\">Prix (MAD) <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleInputChange}\n                placeholder=\"Entrez le prix du livre\"\n                min=\"0\"\n                step=\"0.01\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"isbn\">ISBN <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"isbn\"\n                name=\"isbn\"\n                value={formData.isbn}\n                onChange={handleInputChange}\n                placeholder=\"Entrez l'ISBN du livre\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"date_publication\">Date de publication <span className=\"required\">*</span></label>\n              <input\n                type=\"date\"\n                id=\"date_publication\"\n                name=\"date_publication\"\n                value={formData.date_publication}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"desc\">Description <span className=\"required\">*</span></label>\n            <textarea\n              id=\"desc\"\n              name=\"desc\"\n              value={formData.desc}\n              onChange={handleInputChange}\n              placeholder=\"Description du livre\"\n              required\n            ></textarea>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"image\">Image du livre <span className=\"required\">*</span></label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              name=\"image\"\n              onChange={handleImageChange}\n              accept=\"image/*\"\n              required\n            />\n            {imagePreview && (\n              <div className=\"image-preview\">\n                <img src={imagePreview} alt=\"Aperçu\" />\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-actions\">\n            <button\n              type=\"button\"\n              className=\"cancel-button\"\n              onClick={() => navigate('/books')}\n              disabled={loading}\n            >\n              Annuler\n            </button>\n            <button\n              type=\"submit\"\n              className=\"submit-button\"\n              disabled={loading}\n            >\n              {loading ? 'Ajout en cours...' : 'Ajouter le livre'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AddBook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,EAAEC,SAAS,QAAQ,iBAAiB;AAC1D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,WAAW;IAAEC;EAAU,CAAC,GAAGP,QAAQ,CAAC,CAAC;EAC7C,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAM6B,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,MAAM,CAAC,CAAC;QAC7CC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAAC;;QAEhD;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACI,IAAI,EAAE;UAC7B;UACA,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAAC,EAAE;YAChCnB,aAAa,CAACe,QAAQ,CAACI,IAAI,CAAC;UAC9B;UACA;UAAA,KACK,IAAIJ,QAAQ,CAACI,IAAI,CAACG,OAAO,IAAIF,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC,EAAE;YACtEtB,aAAa,CAACe,QAAQ,CAACI,IAAI,CAACG,OAAO,CAAC;UACtC;UACA;UAAA,KACK,IAAI,OAAOP,QAAQ,CAACI,IAAI,KAAK,QAAQ,EAAE;YAC1C;YACA,MAAMI,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACV,QAAQ,CAACI,IAAI,CAAC,CAACO,MAAM,CAACC,GAAG,IAAIP,KAAK,CAACC,OAAO,CAACM,GAAG,CAAC,CAAC;YACrF,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;cAC7B5B,aAAa,CAACuB,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,MAAM;cACL;cACA,MAAMM,eAAe,GAAGL,MAAM,CAACM,OAAO,CAACf,QAAQ,CAACI,IAAI,CAAC,CAACY,GAAG,CAAC,CAAC,CAACC,EAAE,EAAE3B,QAAQ,CAAC,KAAK;gBAC5E,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;kBAChC,OAAO;oBAAE2B,EAAE,EAAEA,EAAE;oBAAE,GAAG3B;kBAAS,CAAC;gBAChC,CAAC,MAAM;kBACL,OAAO;oBAAE2B,EAAE,EAAEA,EAAE;oBAAEC,IAAI,EAAE5B;kBAAS,CAAC;gBACnC;cACF,CAAC,CAAC;cACFL,aAAa,CAAC6B,eAAe,CAAC;YAChC;UACF,CAAC,MAAM;YACL;YACAZ,OAAO,CAACiB,KAAK,CAAC,8CAA8C,EAAEnB,QAAQ,CAACI,IAAI,CAAC;YAC5EnB,aAAa,CAAC,EAAE,CAAC;UACnB;QACF,CAAC,MAAM;UACL;UACAiB,OAAO,CAACiB,KAAK,CAAC,mCAAmC,CAAC;UAClDlC,aAAa,CAAC,EAAE,CAAC;QACnB;MACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtEtC,SAAS,CAAC,qEAAqE,CAAC;QAChFI,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC;IAEDc,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClB,SAAS,CAAC,CAAC;EAEf,MAAMuC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEH,IAAI;MAAEI;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCpC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgC,IAAI,GAAGI;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,iBAAiB,GAAIH,CAAC,IAAK;IAC/B,MAAMI,IAAI,GAAGJ,CAAC,CAACE,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRtC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXU,KAAK,EAAE6B;MACT,CAAC,CAAC;;MAEF;MACA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvB/B,eAAe,CAAC6B,MAAM,CAACG,MAAM,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC/C,QAAQ,CAACI,QAAQ,EAAE;MACtBT,SAAS,CAAC,qCAAqC,CAAC;MAChD;IACF;IAEA,IAAI;MACFE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMmD,WAAW,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAClC1B,MAAM,CAAC2B,IAAI,CAAClD,QAAQ,CAAC,CAACmD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,KAAK,OAAO,IAAIpD,QAAQ,CAACoD,GAAG,CAAC,EAAE;UACpCJ,WAAW,CAACK,MAAM,CAACD,GAAG,EAAEpD,QAAQ,CAACoD,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAIpD,QAAQ,CAACoD,GAAG,CAAC,EAAE;UACxBJ,WAAW,CAACK,MAAM,CAACD,GAAG,EAAEpD,QAAQ,CAACoD,GAAG,CAAC,CAAC;QACxC;MACF,CAAC,CAAC;MAEF,MAAMjE,SAAS,CAACmE,MAAM,CAACN,WAAW,CAAC;MAEnCtD,WAAW,CAAC,4BAA4B,CAAC;MACzCD,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDtC,SAAS,CAAC,iEAAiE,CAAC;IAC9E,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKiE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjClE,OAAA;MAAIiE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEpDtE,OAAA;MAAKiE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BlE,OAAA;QAAMiE,SAAS,EAAC,eAAe;QAACM,QAAQ,EAAEf,YAAa;QAAAU,QAAA,gBACrDlE,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,iBAAe,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjFtE,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXhC,EAAE,EAAC,OAAO;cACVC,IAAI,EAAC,OAAO;cACZI,KAAK,EAAEpC,QAAQ,CAACE,KAAM;cACtB8D,QAAQ,EAAE9B,iBAAkB;cAC5B+B,WAAW,EAAC,0BAA0B;cACtCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,SAAS;cAAAN,QAAA,GAAC,SAAO,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3EtE,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXhC,EAAE,EAAC,SAAS;cACZC,IAAI,EAAC,SAAS;cACdI,KAAK,EAAEpC,QAAQ,CAACG,OAAQ;cACxB6D,QAAQ,EAAE9B,iBAAkB;cAC5B+B,WAAW,EAAC,2BAA2B;cACvCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,UAAU;cAAAN,QAAA,GAAC,eAAU,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/EtE,OAAA;cACEyC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfI,KAAK,EAAEpC,QAAQ,CAACI,QAAS;cACzB4D,QAAQ,EAAE9B,iBAAkB;cAC5BgC,QAAQ;cAAAV,QAAA,gBAERlE,OAAA;gBAAQ8C,KAAK,EAAC,EAAE;gBAAAoB,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnD9D,UAAU,CAACgC,GAAG,CAAC1B,QAAQ,iBACtBd,OAAA;gBAA0B8C,KAAK,EAAEhC,QAAQ,CAAC2B,EAAG;gBAAAyB,QAAA,EAC1CpD,QAAQ,CAAC4B;cAAI,GADH5B,QAAQ,CAAC2B,EAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,iBAAiB;cAAAN,QAAA,GAAC,qBAAgB,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5FtE,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbhC,EAAE,EAAC,iBAAiB;cACpBC,IAAI,EAAC,iBAAiB;cACtBI,KAAK,EAAEpC,QAAQ,CAACM,eAAgB;cAChC0D,QAAQ,EAAE9B,iBAAkB;cAC5B+B,WAAW,EAAC,8BAA2B;cACvCE,GAAG,EAAC,GAAG;cACPD,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,aAAW,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7EtE,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbhC,EAAE,EAAC,OAAO;cACVC,IAAI,EAAC,OAAO;cACZI,KAAK,EAAEpC,QAAQ,CAACK,KAAM;cACtB2D,QAAQ,EAAE9B,iBAAkB;cAC5B+B,WAAW,EAAC,yBAAyB;cACrCE,GAAG,EAAC,GAAG;cACPC,IAAI,EAAC,MAAM;cACXF,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtEtE,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXhC,EAAE,EAAC,MAAM;cACTC,IAAI,EAAC,MAAM;cACXI,KAAK,EAAEpC,QAAQ,CAACS,IAAK;cACrBuD,QAAQ,EAAE9B,iBAAkB;cAC5B+B,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBlE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAOwE,OAAO,EAAC,kBAAkB;cAAAN,QAAA,GAAC,sBAAoB,eAAAlE,OAAA;gBAAMiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjGtE,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXhC,EAAE,EAAC,kBAAkB;cACrBC,IAAI,EAAC,kBAAkB;cACvBI,KAAK,EAAEpC,QAAQ,CAACQ,gBAAiB;cACjCwD,QAAQ,EAAE9B,iBAAkB;cAC5BgC,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlE,OAAA;YAAOwE,OAAO,EAAC,MAAM;YAAAN,QAAA,GAAC,cAAY,eAAAlE,OAAA;cAAMiE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7EtE,OAAA;YACEyC,EAAE,EAAC,MAAM;YACTC,IAAI,EAAC,MAAM;YACXI,KAAK,EAAEpC,QAAQ,CAACO,IAAK;YACrByD,QAAQ,EAAE9B,iBAAkB;YAC5B+B,WAAW,EAAC,sBAAsB;YAClCC,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlE,OAAA;YAAOwE,OAAO,EAAC,OAAO;YAAAN,QAAA,GAAC,iBAAe,eAAAlE,OAAA;cAAMiE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjFtE,OAAA;YACEyE,IAAI,EAAC,MAAM;YACXhC,EAAE,EAAC,OAAO;YACVC,IAAI,EAAC,OAAO;YACZgC,QAAQ,EAAE1B,iBAAkB;YAC5B+B,MAAM,EAAC,SAAS;YAChBH,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDjD,YAAY,iBACXrB,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlE,OAAA;cAAKgF,GAAG,EAAE3D,YAAa;cAAC4D,GAAG,EAAC;YAAQ;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlE,OAAA;YACEyE,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,eAAe;YACzBiB,OAAO,EAAEA,CAAA,KAAM/E,QAAQ,CAAC,QAAQ,CAAE;YAClCgF,QAAQ,EAAE7E,OAAQ;YAAA4D,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtE,OAAA;YACEyE,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,eAAe;YACzBkB,QAAQ,EAAE7E,OAAQ;YAAA4D,QAAA,EAEjB5D,OAAO,GAAG,mBAAmB,GAAG;UAAkB;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CAnSID,OAAO;EAAA,QACMN,WAAW,EACOG,QAAQ;AAAA;AAAAsF,EAAA,GAFvCnF,OAAO;AAqSb,eAAeA,OAAO;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}