{"ast": null, "code": "import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Token ${token}`;\n  }\n\n  // Si la requête contient un FormData, ne pas définir le Content-Type\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Créer un objet d'erreur plus détaillé\n  const enhancedError = {\n    message: 'Une erreur est survenue',\n    originalError: error,\n    timestamp: new Date().toISOString()\n  };\n\n  // Gérer les erreurs réseau (pas de réponse du serveur)\n  if (!error.response) {\n    enhancedError.type = 'network';\n    enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n    console.error('Erreur réseau:', error);\n  }\n  // Gérer les erreurs avec réponse du serveur\n  else {\n    enhancedError.status = error.response.status;\n    enhancedError.data = error.response.data;\n\n    // Gérer les erreurs d'authentification (401)\n    if (error.response.status === 401) {\n      enhancedError.type = 'auth';\n      enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n      // Supprimer le token et rediriger vers la page de connexion\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    // Gérer les erreurs de permission (403)\n    else if (error.response.status === 403) {\n      enhancedError.type = 'permission';\n      enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n      console.error('Erreur de permission:', error.response.data);\n    }\n    // Gérer les erreurs de validation (400)\n    else if (error.response.status === 400) {\n      enhancedError.type = 'validation';\n      enhancedError.message = 'Les données fournies sont invalides.';\n      console.error('Erreur de validation:', error.response.data);\n    }\n    // Gérer les erreurs serveur (500)\n    else if (error.response.status >= 500) {\n      enhancedError.type = 'server';\n      enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n      console.error('Erreur serveur:', error.response.data);\n    }\n  }\n\n  // Enregistrer l'erreur dans la console avec plus de détails\n  console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n  return Promise.reject(enhancedError);\n});\n\n// API des livres\nexport const livresAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.LIVRES, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: data => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000\n    });\n  },\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: id => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: params => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, {\n    params\n  }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, {\n    params\n  })\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: params => api.get(API_CONFIG.ENDPOINTS.EBOOKS, {\n    params\n  }),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`)\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: data => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: id => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`)\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: id => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`)\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: id => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: id => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`)\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: data => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: id => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`)\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_CONFIG", "api", "create", "baseURL", "BASE_URL", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "data", "FormData", "error", "Promise", "reject", "response", "enhancedError", "message", "originalError", "timestamp", "Date", "toISOString", "type", "console", "status", "removeItem", "window", "location", "href", "livresAPI", "getAll", "params", "get", "ENDPOINTS", "LIVRES", "getById", "id", "log", "post", "timeout", "update", "put", "delete", "emprunter", "reserver", "getRecommendations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ebooksAPI", "EBOOKS", "categoriesAPI", "CATEGORIES", "empruntsAPI", "EMPRUNTS", "retourner", "reservationsAPI", "RESERVATIONS", "annuler", "utilisateursAPI", "getProfile", "UTILISATEURS", "updateProfile", "getNotifications", "markNotificationRead", "statisticsAPI", "STATISTICS"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport API_CONFIG from '../apiConfig';\n\n// Configuration de base d'axios\nconst api = axios.create({\n  baseURL: API_CONFIG.BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true, // Pour envoyer les cookies avec les requêtes\n});\n\n// Intercepteur pour ajouter le token d'authentification\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Token ${token}`;\n    }\n\n    // Si la requête contient un FormData, ne pas définir le Content-Type\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Créer un objet d'erreur plus détaillé\n    const enhancedError = {\n      message: 'Une erreur est survenue',\n      originalError: error,\n      timestamp: new Date().toISOString(),\n    };\n\n    // Gérer les erreurs réseau (pas de réponse du serveur)\n    if (!error.response) {\n      enhancedError.type = 'network';\n      enhancedError.message = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.';\n      console.error('Erreur réseau:', error);\n    }\n    // Gérer les erreurs avec réponse du serveur\n    else {\n      enhancedError.status = error.response.status;\n      enhancedError.data = error.response.data;\n\n      // Gérer les erreurs d'authentification (401)\n      if (error.response.status === 401) {\n        enhancedError.type = 'auth';\n        enhancedError.message = 'Session expirée. Veuillez vous reconnecter.';\n        // Supprimer le token et rediriger vers la page de connexion\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n      // Gérer les erreurs de permission (403)\n      else if (error.response.status === 403) {\n        enhancedError.type = 'permission';\n        enhancedError.message = 'Vous n\\'avez pas les permissions nécessaires pour cette action.';\n        console.error('Erreur de permission:', error.response.data);\n      }\n      // Gérer les erreurs de validation (400)\n      else if (error.response.status === 400) {\n        enhancedError.type = 'validation';\n        enhancedError.message = 'Les données fournies sont invalides.';\n        console.error('Erreur de validation:', error.response.data);\n      }\n      // Gérer les erreurs serveur (500)\n      else if (error.response.status >= 500) {\n        enhancedError.type = 'server';\n        enhancedError.message = 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.';\n        console.error('Erreur serveur:', error.response.data);\n      }\n    }\n\n    // Enregistrer l'erreur dans la console avec plus de détails\n    console.error(`[API Error] ${enhancedError.message}`, enhancedError);\n\n    return Promise.reject(enhancedError);\n  }\n);\n\n// API des livres\nexport const livresAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.LIVRES, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  create: (data) => {\n    console.log('Envoi de données au serveur:', data);\n    // Utiliser des options spécifiques pour les requêtes multipart/form-data\n    return api.post(API_CONFIG.ENDPOINTS.LIVRES, data, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      // Ajouter un timeout plus long pour l'upload des fichiers\n      timeout: 30000,\n    });\n  },\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/`),\n  emprunter: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/emprunter/`),\n  reserver: (id) => api.post(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/reserver/`),\n  getRecommendations: (params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}recommendations/`, { params }),\n  getSimilar: (id, params) => api.get(`${API_CONFIG.ENDPOINTS.LIVRES}${id}/similar/`, { params }),\n};\n\n// API des ebooks\nexport const ebooksAPI = {\n  getAll: (params) => api.get(API_CONFIG.ENDPOINTS.EBOOKS, { params }),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.EBOOKS, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.EBOOKS}${id}/`),\n};\n\n// API des catégories\nexport const categoriesAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.CATEGORIES),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n  create: (data) => api.post(API_CONFIG.ENDPOINTS.CATEGORIES, data),\n  update: (id, data) => api.put(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`, data),\n  delete: (id) => api.delete(`${API_CONFIG.ENDPOINTS.CATEGORIES}${id}/`),\n};\n\n// API des emprunts\nexport const empruntsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.EMPRUNTS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/`),\n  retourner: (id) => api.post(`${API_CONFIG.ENDPOINTS.EMPRUNTS}${id}/retourner/`),\n};\n\n// API des réservations\nexport const reservationsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.RESERVATIONS),\n  getById: (id) => api.get(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n  annuler: (id) => api.delete(`${API_CONFIG.ENDPOINTS.RESERVATIONS}${id}/`),\n};\n\n// API des utilisateurs\nexport const utilisateursAPI = {\n  getProfile: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`),\n  updateProfile: (data) => api.put(`${API_CONFIG.ENDPOINTS.UTILISATEURS}profiles/me/`, data),\n  getNotifications: () => api.get(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/`),\n  markNotificationRead: (id) => api.post(`${API_CONFIG.ENDPOINTS.UTILISATEURS}notifications/${id}/read/`),\n};\n\n// API des statistiques\nexport const statisticsAPI = {\n  getAll: () => api.get(API_CONFIG.ENDPOINTS.STATISTICS),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,UAAU,CAACI,QAAQ;EAC5BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,SAASM,KAAK,EAAE;EACpD;;EAEA;EACA,IAAID,MAAM,CAACI,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC;EACvC;EAEA,OAAOK,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACM,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,MAAMI,aAAa,GAAG;IACpBC,OAAO,EAAE,yBAAyB;IAClCC,aAAa,EAAEN,KAAK;IACpBO,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,IAAI,CAACT,KAAK,CAACG,QAAQ,EAAE;IACnBC,aAAa,CAACM,IAAI,GAAG,SAAS;IAC9BN,aAAa,CAACC,OAAO,GAAG,oEAAoE;IAC5FM,OAAO,CAACX,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACxC;EACA;EAAA,KACK;IACHI,aAAa,CAACQ,MAAM,GAAGZ,KAAK,CAACG,QAAQ,CAACS,MAAM;IAC5CR,aAAa,CAACN,IAAI,GAAGE,KAAK,CAACG,QAAQ,CAACL,IAAI;;IAExC;IACA,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACjCR,aAAa,CAACM,IAAI,GAAG,MAAM;MAC3BN,aAAa,CAACC,OAAO,GAAG,6CAA6C;MACrE;MACAT,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IACA;IAAA,KACK,IAAIhB,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACtCR,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,iEAAiE;MACzFM,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IAC7D;IACA;IAAA,KACK,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;MACtCR,aAAa,CAACM,IAAI,GAAG,YAAY;MACjCN,aAAa,CAACC,OAAO,GAAG,sCAAsC;MAC9DM,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IAC7D;IACA;IAAA,KACK,IAAIE,KAAK,CAACG,QAAQ,CAACS,MAAM,IAAI,GAAG,EAAE;MACrCR,aAAa,CAACM,IAAI,GAAG,QAAQ;MAC7BN,aAAa,CAACC,OAAO,GAAG,uEAAuE;MAC/FM,OAAO,CAACX,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACG,QAAQ,CAACL,IAAI,CAAC;IACvD;EACF;;EAEA;EACAa,OAAO,CAACX,KAAK,CAAC,eAAeI,aAAa,CAACC,OAAO,EAAE,EAAED,aAAa,CAAC;EAEpE,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;AACtC,CACF,CAAC;;AAED;AACA,OAAO,MAAMa,SAAS,GAAG;EACvBC,MAAM,EAAGC,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACC,MAAM,EAAE;IAAEH;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAChEtC,MAAM,EAAGY,IAAI,IAAK;IAChBa,OAAO,CAACc,GAAG,CAAC,8BAA8B,EAAE3B,IAAI,CAAC;IACjD;IACA,OAAOb,GAAG,CAACyC,IAAI,CAAC1C,UAAU,CAACqC,SAAS,CAACC,MAAM,EAAExB,IAAI,EAAE;MACjDT,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACD;MACAsC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,EAAEA,CAACJ,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC4C,GAAG,CAAC,GAAG7C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC3EgC,MAAM,EAAGN,EAAE,IAAKvC,GAAG,CAAC6C,MAAM,CAAC,GAAG9C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,GAAG,CAAC;EAClEO,SAAS,EAAGP,EAAE,IAAKvC,GAAG,CAACyC,IAAI,CAAC,GAAG1C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,aAAa,CAAC;EAC7EQ,QAAQ,EAAGR,EAAE,IAAKvC,GAAG,CAACyC,IAAI,CAAC,GAAG1C,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,YAAY,CAAC;EAC3ES,kBAAkB,EAAGd,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,kBAAkB,EAAE;IAAEH;EAAO,CAAC,CAAC;EACrGe,UAAU,EAAEA,CAACV,EAAE,EAAEL,MAAM,KAAKlC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACC,MAAM,GAAGE,EAAE,WAAW,EAAE;IAAEL;EAAO,CAAC;AAChG,CAAC;;AAED;AACA,OAAO,MAAMgB,SAAS,GAAG;EACvBjB,MAAM,EAAGC,MAAM,IAAKlC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACe,MAAM,EAAE;IAAEjB;EAAO,CAAC,CAAC;EACpEI,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACe,MAAM,GAAGZ,EAAE,GAAG,CAAC;EAChEtC,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACyC,IAAI,CAAC1C,UAAU,CAACqC,SAAS,CAACe,MAAM,EAAEtC,IAAI,CAAC;EAC7D8B,MAAM,EAAEA,CAACJ,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC4C,GAAG,CAAC,GAAG7C,UAAU,CAACqC,SAAS,CAACe,MAAM,GAAGZ,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC3EgC,MAAM,EAAGN,EAAE,IAAKvC,GAAG,CAAC6C,MAAM,CAAC,GAAG9C,UAAU,CAACqC,SAAS,CAACe,MAAM,GAAGZ,EAAE,GAAG;AACnE,CAAC;;AAED;AACA,OAAO,MAAMa,aAAa,GAAG;EAC3BnB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACiB,UAAU,CAAC;EACtDf,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACiB,UAAU,GAAGd,EAAE,GAAG,CAAC;EACpEtC,MAAM,EAAGY,IAAI,IAAKb,GAAG,CAACyC,IAAI,CAAC1C,UAAU,CAACqC,SAAS,CAACiB,UAAU,EAAExC,IAAI,CAAC;EACjE8B,MAAM,EAAEA,CAACJ,EAAE,EAAE1B,IAAI,KAAKb,GAAG,CAAC4C,GAAG,CAAC,GAAG7C,UAAU,CAACqC,SAAS,CAACiB,UAAU,GAAGd,EAAE,GAAG,EAAE1B,IAAI,CAAC;EAC/EgC,MAAM,EAAGN,EAAE,IAAKvC,GAAG,CAAC6C,MAAM,CAAC,GAAG9C,UAAU,CAACqC,SAAS,CAACiB,UAAU,GAAGd,EAAE,GAAG;AACvE,CAAC;;AAED;AACA,OAAO,MAAMe,WAAW,GAAG;EACzBrB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACmB,QAAQ,CAAC;EACpDjB,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACmB,QAAQ,GAAGhB,EAAE,GAAG,CAAC;EAClEiB,SAAS,EAAGjB,EAAE,IAAKvC,GAAG,CAACyC,IAAI,CAAC,GAAG1C,UAAU,CAACqC,SAAS,CAACmB,QAAQ,GAAGhB,EAAE,aAAa;AAChF,CAAC;;AAED;AACA,OAAO,MAAMkB,eAAe,GAAG;EAC7BxB,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAACsB,YAAY,CAAC;EACxDpB,OAAO,EAAGC,EAAE,IAAKvC,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAACsB,YAAY,GAAGnB,EAAE,GAAG,CAAC;EACtEoB,OAAO,EAAGpB,EAAE,IAAKvC,GAAG,CAAC6C,MAAM,CAAC,GAAG9C,UAAU,CAACqC,SAAS,CAACsB,YAAY,GAAGnB,EAAE,GAAG;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMqB,eAAe,GAAG;EAC7BC,UAAU,EAAEA,CAAA,KAAM7D,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAAC0B,YAAY,cAAc,CAAC;EAC7EC,aAAa,EAAGlD,IAAI,IAAKb,GAAG,CAAC4C,GAAG,CAAC,GAAG7C,UAAU,CAACqC,SAAS,CAAC0B,YAAY,cAAc,EAAEjD,IAAI,CAAC;EAC1FmD,gBAAgB,EAAEA,CAAA,KAAMhE,GAAG,CAACmC,GAAG,CAAC,GAAGpC,UAAU,CAACqC,SAAS,CAAC0B,YAAY,gBAAgB,CAAC;EACrFG,oBAAoB,EAAG1B,EAAE,IAAKvC,GAAG,CAACyC,IAAI,CAAC,GAAG1C,UAAU,CAACqC,SAAS,CAAC0B,YAAY,iBAAiBvB,EAAE,QAAQ;AACxG,CAAC;;AAED;AACA,OAAO,MAAM2B,aAAa,GAAG;EAC3BjC,MAAM,EAAEA,CAAA,KAAMjC,GAAG,CAACmC,GAAG,CAACpC,UAAU,CAACqC,SAAS,CAAC+B,UAAU;AACvD,CAAC;AAED,eAAenE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}