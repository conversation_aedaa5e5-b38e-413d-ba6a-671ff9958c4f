{"ast": null, "code": "// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias en mode développement Django\n  DJANGO_MEDIA_URL: 'http://localhost:8000/media/',\n  // Image par défaut pour les livres et ebooks\n  DEFAULT_BOOK_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png',\n  // Image par défaut pour les profils\n  DEFAULT_PROFILE_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png',\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: imagePath => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      console.warn(\"getImageUrl: imagePath est vide ou null\");\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      console.log(`getImageUrl: URL complète détectée: ${imagePath}`);\n      return imagePath;\n    }\n\n    // Simplification du traitement des chemins\n    let cleanPath = imagePath;\n\n    // Supprimer tout préfixe /media/ existant pour éviter les doublons\n    cleanPath = cleanPath.replace(/^\\/?(media\\/)+/, '');\n\n    // Récupérer le timestamp pour éviter la mise en cache\n    const timestamp = new Date().getTime();\n    localStorage.setItem('imageTimestamp', timestamp);\n\n    // Construire l'URL complète avec le timestamp\n    const fullUrl = `${config.DJANGO_MEDIA_URL}${cleanPath}?t=${timestamp}`;\n\n    // Log pour le débogage\n    console.log(`getImageUrl: URL générée: ${fullUrl} (original: ${imagePath})`);\n    return fullUrl;\n  },\n  // Fonction spécifique pour les images de profil\n  getProfileImageUrl: imagePath => {\n    if (!imagePath) {\n      return config.DEFAULT_PROFILE_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  },\n  // Fonction spécifique pour les images de livres\n  getBookImageUrl: imagePath => {\n    if (!imagePath) {\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_BASE_URL", "MEDIA_BASE_URL", "DJANGO_MEDIA_URL", "DEFAULT_BOOK_IMAGE", "DEFAULT_PROFILE_IMAGE", "getImageUrl", "imagePath", "console", "warn", "startsWith", "log", "cleanPath", "replace", "timestamp", "Date", "getTime", "localStorage", "setItem", "fullUrl", "getProfileImageUrl", "getBookImageUrl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/config.js"], "sourcesContent": ["// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias en mode développement Django\n  DJANGO_MEDIA_URL: 'http://localhost:8000/media/',\n\n  // Image par défaut pour les livres et ebooks\n  DEFAULT_BOOK_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png',\n\n  // Image par défaut pour les profils\n  DEFAULT_PROFILE_IMAGE: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Man_Silhouette.png/220px-Man_Silhouette.png',\n\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: (imagePath) => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      console.warn(\"getImageUrl: imagePath est vide ou null\");\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      console.log(`getImageUrl: URL complète détectée: ${imagePath}`);\n      return imagePath;\n    }\n\n    // Simplification du traitement des chemins\n    let cleanPath = imagePath;\n\n    // Supprimer tout préfixe /media/ existant pour éviter les doublons\n    cleanPath = cleanPath.replace(/^\\/?(media\\/)+/, '');\n\n    // Récupérer le timestamp pour éviter la mise en cache\n    const timestamp = new Date().getTime();\n    localStorage.setItem('imageTimestamp', timestamp);\n\n    // Construire l'URL complète avec le timestamp\n    const fullUrl = `${config.DJANGO_MEDIA_URL}${cleanPath}?t=${timestamp}`;\n\n    // Log pour le débogage\n    console.log(`getImageUrl: URL générée: ${fullUrl} (original: ${imagePath})`);\n\n    return fullUrl;\n  },\n\n  // Fonction spécifique pour les images de profil\n  getProfileImageUrl: (imagePath) => {\n    if (!imagePath) {\n      return config.DEFAULT_PROFILE_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  },\n\n  // Fonction spécifique pour les images de livres\n  getBookImageUrl: (imagePath) => {\n    if (!imagePath) {\n      return config.DEFAULT_BOOK_IMAGE;\n    }\n    return config.getImageUrl(imagePath);\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,YAAY,EAAE,uBAAuB;EAErC;EACAC,cAAc,EAAE,uBAAuB;EAEvC;EACAC,gBAAgB,EAAE,8BAA8B;EAEhD;EACAC,kBAAkB,EAAE,wHAAwH;EAE5I;EACAC,qBAAqB,EAAE,uGAAuG;EAE9H;EACAC,WAAW,EAAGC,SAAS,IAAK;IAC1B;IACA,IAAI,CAACA,SAAS,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAC;MACvD,OAAOT,MAAM,CAACI,kBAAkB;IAClC;;IAEA;IACA,IAAIG,SAAS,CAACG,UAAU,CAAC,MAAM,CAAC,EAAE;MAChCF,OAAO,CAACG,GAAG,CAAC,uCAAuCJ,SAAS,EAAE,CAAC;MAC/D,OAAOA,SAAS;IAClB;;IAEA;IACA,IAAIK,SAAS,GAAGL,SAAS;;IAEzB;IACAK,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;;IAEnD;IACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtCC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,SAAS,CAAC;;IAEjD;IACA,MAAMK,OAAO,GAAG,GAAGnB,MAAM,CAACG,gBAAgB,GAAGS,SAAS,MAAME,SAAS,EAAE;;IAEvE;IACAN,OAAO,CAACG,GAAG,CAAC,6BAA6BQ,OAAO,eAAeZ,SAAS,GAAG,CAAC;IAE5E,OAAOY,OAAO;EAChB,CAAC;EAED;EACAC,kBAAkB,EAAGb,SAAS,IAAK;IACjC,IAAI,CAACA,SAAS,EAAE;MACd,OAAOP,MAAM,CAACK,qBAAqB;IACrC;IACA,OAAOL,MAAM,CAACM,WAAW,CAACC,SAAS,CAAC;EACtC,CAAC;EAED;EACAc,eAAe,EAAGd,SAAS,IAAK;IAC9B,IAAI,CAACA,SAAS,EAAE;MACd,OAAOP,MAAM,CAACI,kBAAkB;IAClC;IACA,OAAOJ,MAAM,CAACM,WAAW,CAACC,SAAS,CAAC;EACtC;AACF,CAAC;AAED,eAAeP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}