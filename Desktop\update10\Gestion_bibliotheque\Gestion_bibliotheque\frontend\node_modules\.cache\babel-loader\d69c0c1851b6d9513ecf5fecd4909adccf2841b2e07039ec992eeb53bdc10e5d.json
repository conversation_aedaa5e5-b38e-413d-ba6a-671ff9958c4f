{"ast": null, "code": "// Configuration API\nconst API_CONFIG = {\n  BASE_URL: 'http://localhost:8000',\n  // Endpoints\n  ENDPOINTS: {\n    LIVRES: '/api/livres/livres/',\n    EBOOKS: '/api/livres/ebooks/',\n    CATEGORIES: '/api/livres/categories/',\n    EMPRUNTS: '/api/livres/emprunts/',\n    RESERVATIONS: '/api/livres/reservations/',\n    UTILISATEURS: '/api/utilisateurs/',\n    LOGIN: '/api/utilisateurs/login/',\n    REGISTER: '/api/utilisateurs/register/',\n    LOGOUT: '/api/utilisateurs/logout/'\n  },\n  // Fonction utilitaire pour construire des URL complètes\n  getUrl: function (endpoint) {\n    return this.BASE_URL + endpoint;\n  }\n};\nexport default API_CONFIG;", "map": {"version": 3, "names": ["API_CONFIG", "BASE_URL", "ENDPOINTS", "LIVRES", "EBOOKS", "CATEGORIES", "EMPRUNTS", "RESERVATIONS", "UTILISATEURS", "LOGIN", "REGISTER", "LOGOUT", "getUrl", "endpoint"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/apiConfig.js"], "sourcesContent": ["// Configuration API\nconst API_CONFIG = {\n  BASE_URL: 'http://localhost:8000',\n  \n  // Endpoints\n  ENDPOINTS: {\n    LIVRES: '/api/livres/livres/',\n    EBOOKS: '/api/livres/ebooks/',\n    CATEGORIES: '/api/livres/categories/',\n    EMPRUNTS: '/api/livres/emprunts/',\n    RESERVATIONS: '/api/livres/reservations/',\n    UTILISATEURS: '/api/utilisateurs/',\n    LOGIN: '/api/utilisateurs/login/',\n    REGISTER: '/api/utilisateurs/register/',\n    LOGOUT: '/api/utilisateurs/logout/',\n  },\n  \n  // Fonction utilitaire pour construire des URL complètes\n  getUrl: function(endpoint) {\n    return this.BASE_URL + endpoint;\n  }\n};\n\nexport default API_CONFIG;\n"], "mappings": "AAAA;AACA,MAAMA,UAAU,GAAG;EACjBC,QAAQ,EAAE,uBAAuB;EAEjC;EACAC,SAAS,EAAE;IACTC,MAAM,EAAE,qBAAqB;IAC7BC,MAAM,EAAE,qBAAqB;IAC7BC,UAAU,EAAE,yBAAyB;IACrCC,QAAQ,EAAE,uBAAuB;IACjCC,YAAY,EAAE,2BAA2B;IACzCC,YAAY,EAAE,oBAAoB;IAClCC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,6BAA6B;IACvCC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,MAAM,EAAE,SAAAA,CAASC,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACZ,QAAQ,GAAGY,QAAQ;EACjC;AACF,CAAC;AAED,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}