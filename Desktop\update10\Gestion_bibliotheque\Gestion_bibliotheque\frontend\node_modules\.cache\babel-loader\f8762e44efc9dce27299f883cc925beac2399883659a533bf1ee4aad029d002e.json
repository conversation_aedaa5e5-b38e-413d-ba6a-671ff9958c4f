{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projetMAI\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async token => {\n    try {\n      const response = await axios.get('/api/utilisateurs/users/me/', {\n        headers: {\n          'Authorization': `Token ${token}`\n        }\n      });\n      setCurrentUser(response.data);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n      setAuthError('Impossible de récupérer les informations utilisateur');\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (token) {\n          const success = await fetchUserInfo(token);\n          if (!success) {\n            localStorage.removeItem('token');\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, [fetchUserInfo]);\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n      const {\n        user,\n        profile,\n        token\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n      }\n      setCurrentUser({\n        ...user,\n        profile\n      });\n      setIsAuthenticated(true);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Erreur de connexion:', error);\n      setAuthError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Erreur de connexion');\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Erreur de connexion'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n      return {\n        success: true,\n        message: response.data.detail\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response5, _error$response5$data;\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && _error$response4.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n      return {\n        success: false,\n        message: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || 'Erreur d\\'inscription'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async userData => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data, _error$response7, _error$response7$data;\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"MmdmBtoNyEXLJ8ErwOEOz7sUNs4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useCallback", "axios", "api", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "authError", "setAuthError", "fetchUserInfo", "token", "response", "get", "headers", "data", "error", "console", "checkAuth", "localStorage", "getItem", "success", "removeItem", "login", "username", "password", "post", "user", "profile", "setItem", "defaults", "common", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "message", "register", "userData", "_error$response3", "_error$response3$data", "_error$response4", "_error$response5", "_error$response5$data", "errorMessages", "field", "Array", "isArray", "push", "join", "length", "logout", "updateProfile", "put", "_error$response6", "_error$response6$data", "_error$response7", "_error$response7$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projetMAI/Gestion_bibliotheque/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport api from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [authError, setAuthError] = useState(null);\n\n  // Fonction pour récupérer les informations de l'utilisateur\n  const fetchUserInfo = useCallback(async (token) => {\n    try {\n      const response = await axios.get('/api/utilisateurs/users/me/', {\n        headers: {\n          'Authorization': `Token ${token}`\n        }\n      });\n\n      setCurrentUser(response.data);\n      setIsAuthenticated(true);\n      setAuthError(null);\n      return true;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des informations utilisateur:', error);\n      setAuthError('Impossible de récupérer les informations utilisateur');\n      return false;\n    }\n  }, []);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (token) {\n          const success = await fetchUserInfo(token);\n\n          if (!success) {\n            localStorage.removeItem('token');\n            setIsAuthenticated(false);\n            setCurrentUser(null);\n          }\n        }\n      } catch (error) {\n        console.error('Erreur d\\'authentification:', error);\n        localStorage.removeItem('token');\n        setIsAuthenticated(false);\n        setCurrentUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [fetchUserInfo]);\n\n  const login = async (username, password) => {\n    try {\n      setAuthError(null);\n\n      const response = await api.post('/api/utilisateurs/login/', {\n        username,\n        password\n      });\n\n      const { user, profile, token } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Token ${token}`;\n        api.defaults.headers.common['Authorization'] = `Token ${token}`;\n      }\n\n      setCurrentUser({ ...user, profile });\n      setIsAuthenticated(true);\n\n      return { success: true };\n    } catch (error) {\n      console.error('Erreur de connexion:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur de connexion');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur de connexion'\n      };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.post('/api/utilisateurs/register/', userData);\n      return { success: true, message: response.data.detail };\n    } catch (error) {\n      console.error('Erreur d\\'inscription:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur d\\'inscription');\n\n      // Formater les erreurs de validation\n      if (error.response?.data && typeof error.response.data === 'object') {\n        const errorMessages = [];\n        for (const field in error.response.data) {\n          if (Array.isArray(error.response.data[field])) {\n            errorMessages.push(`${field}: ${error.response.data[field].join(', ')}`);\n          }\n        }\n\n        if (errorMessages.length > 0) {\n          return {\n            success: false,\n            message: errorMessages.join('\\n')\n          };\n        }\n      }\n\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur d\\'inscription'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await api.post('/api/utilisateurs/logout/');\n    } catch (error) {\n      console.error('Erreur de déconnexion:', error);\n    } finally {\n      setCurrentUser(null);\n      setIsAuthenticated(false);\n      setAuthError(null);\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Fonction pour mettre à jour le profil utilisateur\n  const updateProfile = async (userData) => {\n    try {\n      setAuthError(null);\n      const response = await api.put('/utilisateurs/profiles/me/', userData);\n\n      // Mettre à jour les informations de l'utilisateur\n      await fetchUserInfo(localStorage.getItem('token'));\n\n      return { success: true, data: response.data };\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour du profil:', error);\n      setAuthError(error.response?.data?.detail || 'Erreur lors de la mise à jour du profil');\n      return {\n        success: false,\n        message: error.response?.data?.detail || 'Erreur lors de la mise à jour du profil'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    isAuthenticated,\n    loading,\n    authError,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMU,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMT,UAAU,CAACO,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMsB,aAAa,GAAGnB,WAAW,CAAC,MAAOoB,KAAK,IAAK;IACjD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,6BAA6B,EAAE;QAC9DC,OAAO,EAAE;UACP,eAAe,EAAE,SAASH,KAAK;QACjC;MACF,CAAC,CAAC;MAEFR,cAAc,CAACS,QAAQ,CAACG,IAAI,CAAC;MAC7BV,kBAAkB,CAAC,IAAI,CAAC;MACxBI,YAAY,CAAC,IAAI,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpFP,YAAY,CAAC,sDAAsD,CAAC;MACpE,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM4B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFX,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMI,KAAK,GAAGQ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAIT,KAAK,EAAE;UACT,MAAMU,OAAO,GAAG,MAAMX,aAAa,CAACC,KAAK,CAAC;UAE1C,IAAI,CAACU,OAAO,EAAE;YACZF,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;YAChCjB,kBAAkB,CAAC,KAAK,CAAC;YACzBF,cAAc,CAAC,IAAI,CAAC;UACtB;QACF;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDG,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;QAChCjB,kBAAkB,CAAC,KAAK,CAAC;QACzBF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDW,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACR,aAAa,CAAC,CAAC;EAEnB,MAAMa,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACFhB,YAAY,CAAC,IAAI,CAAC;MAElB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAACiC,IAAI,CAAC,0BAA0B,EAAE;QAC1DF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEE,IAAI;QAAEC,OAAO;QAAEjB;MAAM,CAAC,GAAGC,QAAQ,CAACG,IAAI;MAE9C,IAAIJ,KAAK,EAAE;QACTQ,YAAY,CAACU,OAAO,CAAC,OAAO,EAAElB,KAAK,CAAC;QACpCnB,KAAK,CAACsC,QAAQ,CAAChB,OAAO,CAACiB,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;QACjElB,GAAG,CAACqC,QAAQ,CAAChB,OAAO,CAACiB,MAAM,CAAC,eAAe,CAAC,GAAG,SAASpB,KAAK,EAAE;MACjE;MAEAR,cAAc,CAAC;QAAE,GAAGwB,IAAI;QAAEC;MAAQ,CAAC,CAAC;MACpCvB,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEgB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdlB,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CP,YAAY,CAAC,EAAAuB,eAAA,GAAAhB,KAAK,CAACJ,QAAQ,cAAAoB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjB,IAAI,cAAAkB,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,qBAAqB,CAAC;MACnE,OAAO;QACLf,OAAO,EAAE,KAAK;QACdgB,OAAO,EAAE,EAAAH,gBAAA,GAAAlB,KAAK,CAACJ,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBC,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAME,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAACiC,IAAI,CAAC,6BAA6B,EAAEa,QAAQ,CAAC;MACxE,OAAO;QAAElB,OAAO,EAAE,IAAI;QAAEgB,OAAO,EAAEzB,QAAQ,CAACG,IAAI,CAACqB;MAAO,CAAC;IACzD,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd3B,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CP,YAAY,CAAC,EAAA+B,gBAAA,GAAAxB,KAAK,CAACJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,uBAAuB,CAAC;;MAErE;MACA,IAAI,CAAAM,gBAAA,GAAA1B,KAAK,CAACJ,QAAQ,cAAA8B,gBAAA,eAAdA,gBAAA,CAAgB3B,IAAI,IAAI,OAAOC,KAAK,CAACJ,QAAQ,CAACG,IAAI,KAAK,QAAQ,EAAE;QACnE,MAAM8B,aAAa,GAAG,EAAE;QACxB,KAAK,MAAMC,KAAK,IAAI9B,KAAK,CAACJ,QAAQ,CAACG,IAAI,EAAE;UACvC,IAAIgC,KAAK,CAACC,OAAO,CAAChC,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAAC+B,KAAK,CAAC,CAAC,EAAE;YAC7CD,aAAa,CAACI,IAAI,CAAC,GAAGH,KAAK,KAAK9B,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAAC+B,KAAK,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UAC1E;QACF;QAEA,IAAIL,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;UAC5B,OAAO;YACL9B,OAAO,EAAE,KAAK;YACdgB,OAAO,EAAEQ,aAAa,CAACK,IAAI,CAAC,IAAI;UAClC,CAAC;QACH;MACF;MAEA,OAAO;QACL7B,OAAO,EAAE,KAAK;QACdgB,OAAO,EAAE,EAAAM,gBAAA,GAAA3B,KAAK,CAACJ,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBR,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMgB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM3D,GAAG,CAACiC,IAAI,CAAC,2BAA2B,CAAC;IAC7C,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRb,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,KAAK,CAAC;MACzBI,YAAY,CAAC,IAAI,CAAC;MAClBU,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO9B,KAAK,CAACsC,QAAQ,CAAChB,OAAO,CAACiB,MAAM,CAAC,eAAe,CAAC;MACrD,OAAOtC,GAAG,CAACqC,QAAQ,CAAChB,OAAO,CAACiB,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMsB,aAAa,GAAG,MAAOd,QAAQ,IAAK;IACxC,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMG,QAAQ,GAAG,MAAMnB,GAAG,CAAC6D,GAAG,CAAC,4BAA4B,EAAEf,QAAQ,CAAC;;MAEtE;MACA,MAAM7B,aAAa,CAACS,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;MAElD,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEN,IAAI,EAAEH,QAAQ,CAACG;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAuC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdzC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEP,YAAY,CAAC,EAAA8C,gBAAA,GAAAvC,KAAK,CAACJ,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBpB,MAAM,KAAI,yCAAyC,CAAC;MACvF,OAAO;QACLf,OAAO,EAAE,KAAK;QACdgB,OAAO,EAAE,EAAAoB,gBAAA,GAAAzC,KAAK,CAACJ,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBtB,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMuB,KAAK,GAAG;IACZzD,WAAW;IACXE,eAAe;IACfE,OAAO;IACPE,SAAS;IACTe,KAAK;IACLe,QAAQ;IACRc,MAAM;IACNC;EACF,CAAC;EAED,oBACE1D,OAAA,CAACC,WAAW,CAACgE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3D,QAAA,EAChCA;EAAQ;IAAA6D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/D,GAAA,CA1KWF,YAAY;AAAAkE,EAAA,GAAZlE,YAAY;AAAA,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}