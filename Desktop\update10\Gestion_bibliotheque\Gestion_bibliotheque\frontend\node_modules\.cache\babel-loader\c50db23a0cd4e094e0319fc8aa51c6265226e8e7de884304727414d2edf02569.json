{"ast": null, "code": "'use strict';\n\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};", "map": {"version": 3, "names": ["call", "require", "isCallable", "isObject", "$TypeError", "TypeError", "module", "exports", "input", "pref", "fn", "val", "toString", "valueOf"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/node_modules/core-js-pure/internals/ordinary-to-primitive.js"], "sourcesContent": ["'use strict';\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,QAAQ,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIG,UAAU,GAAGC,SAAS;;AAE1B;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;EACtC,IAAIC,EAAE,EAAEC,GAAG;EACX,IAAIF,IAAI,KAAK,QAAQ,IAAIP,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACI,QAAQ,CAAC,IAAI,CAACT,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EACxG,IAAIT,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACK,OAAO,CAAC,IAAI,CAACV,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EAClF,IAAIF,IAAI,KAAK,QAAQ,IAAIP,UAAU,CAACQ,EAAE,GAAGF,KAAK,CAACI,QAAQ,CAAC,IAAI,CAACT,QAAQ,CAACQ,GAAG,GAAGX,IAAI,CAACU,EAAE,EAAEF,KAAK,CAAC,CAAC,EAAE,OAAOG,GAAG;EACxG,MAAM,IAAIP,UAAU,CAAC,yCAAyC,CAAC;AACjE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}