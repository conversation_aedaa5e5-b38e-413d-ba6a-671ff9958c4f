{"ast": null, "code": "// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: imagePath => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      return imagePath;\n    }\n\n    // Convertir l'objet ImageFieldFile en chaîne si nécessaire\n    let pathStr = String(imagePath);\n\n    // Extraire le nom du fichier si c'est un objet ImageFieldFile\n    if (pathStr.includes('<ImageFieldFile:')) {\n      const match = pathStr.match(/media\\/(.+?)>/);\n      if (match && match[1]) {\n        pathStr = match[1];\n      } else {\n        // Si on ne peut pas extraire le chemin, utiliser l'image par défaut\n        return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n      }\n    }\n\n    // Nettoyer le chemin de l'image\n    let cleanPath = pathStr;\n\n    // Supprimer tous les préfixes /media/ ou media/ pour éviter les doublons\n    while (cleanPath.includes('/media/media/') || cleanPath.includes('media/media/')) {\n      cleanPath = cleanPath.replace('/media/media/', '/media/');\n      cleanPath = cleanPath.replace('media/media/', 'media/');\n    }\n\n    // Assurer que le chemin commence par /media/\n    if (!cleanPath.startsWith('/media/') && !cleanPath.startsWith('media/')) {\n      cleanPath = `/media/${cleanPath}`;\n    }\n\n    // Assurer que le chemin commence par /\n    if (!cleanPath.startsWith('/')) {\n      cleanPath = `/${cleanPath}`;\n    }\n\n    // Construire l'URL complète\n    return `${config.MEDIA_BASE_URL}${cleanPath}`;\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_BASE_URL", "MEDIA_BASE_URL", "getImageUrl", "imagePath", "startsWith", "pathStr", "String", "includes", "match", "cleanPath", "replace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/config.js"], "sourcesContent": ["// Configuration globale de l'application\nconst config = {\n  // URL de base de l'API\n  API_BASE_URL: 'http://localhost:8000',\n\n  // URL de base pour les médias (images, etc.)\n  MEDIA_BASE_URL: 'http://localhost:8000',\n\n  // Fonction améliorée pour construire une URL d'image complète\n  getImageUrl: (imagePath) => {\n    // Si l'image est null, undefined ou vide\n    if (!imagePath) {\n      return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n    }\n\n    // Si l'image est déjà une URL complète\n    if (imagePath.startsWith('http')) {\n      return imagePath;\n    }\n\n    // Convertir l'objet ImageFieldFile en chaîne si nécessaire\n    let pathStr = String(imagePath);\n\n    // Extraire le nom du fichier si c'est un objet ImageFieldFile\n    if (pathStr.includes('<ImageFieldFile:')) {\n      const match = pathStr.match(/media\\/(.+?)>/);\n      if (match && match[1]) {\n        pathStr = match[1];\n      } else {\n        // Si on ne peut pas extraire le chemin, utiliser l'image par défaut\n        return 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/No-Image-Placeholder.svg/1665px-No-Image-Placeholder.svg.png';\n      }\n    }\n\n    // Nettoyer le chemin de l'image\n    let cleanPath = pathStr;\n\n    // Supprimer tous les préfixes /media/ ou media/ pour éviter les doublons\n    while (cleanPath.includes('/media/media/') || cleanPath.includes('media/media/')) {\n      cleanPath = cleanPath.replace('/media/media/', '/media/');\n      cleanPath = cleanPath.replace('media/media/', 'media/');\n    }\n\n    // Assurer que le chemin commence par /media/\n    if (!cleanPath.startsWith('/media/') && !cleanPath.startsWith('media/')) {\n      cleanPath = `/media/${cleanPath}`;\n    }\n\n    // Assurer que le chemin commence par /\n    if (!cleanPath.startsWith('/')) {\n      cleanPath = `/${cleanPath}`;\n    }\n\n    // Construire l'URL complète\n    return `${config.MEDIA_BASE_URL}${cleanPath}`;\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,YAAY,EAAE,uBAAuB;EAErC;EACAC,cAAc,EAAE,uBAAuB;EAEvC;EACAC,WAAW,EAAGC,SAAS,IAAK;IAC1B;IACA,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,wHAAwH;IACjI;;IAEA;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAChC,OAAOD,SAAS;IAClB;;IAEA;IACA,IAAIE,OAAO,GAAGC,MAAM,CAACH,SAAS,CAAC;;IAE/B;IACA,IAAIE,OAAO,CAACE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MACxC,MAAMC,KAAK,GAAGH,OAAO,CAACG,KAAK,CAAC,eAAe,CAAC;MAC5C,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrBH,OAAO,GAAGG,KAAK,CAAC,CAAC,CAAC;MACpB,CAAC,MAAM;QACL;QACA,OAAO,wHAAwH;MACjI;IACF;;IAEA;IACA,IAAIC,SAAS,GAAGJ,OAAO;;IAEvB;IACA,OAAOI,SAAS,CAACF,QAAQ,CAAC,eAAe,CAAC,IAAIE,SAAS,CAACF,QAAQ,CAAC,cAAc,CAAC,EAAE;MAChFE,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC;MACzDD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC;IACzD;;IAEA;IACA,IAAI,CAACD,SAAS,CAACL,UAAU,CAAC,SAAS,CAAC,IAAI,CAACK,SAAS,CAACL,UAAU,CAAC,QAAQ,CAAC,EAAE;MACvEK,SAAS,GAAG,UAAUA,SAAS,EAAE;IACnC;;IAEA;IACA,IAAI,CAACA,SAAS,CAACL,UAAU,CAAC,GAAG,CAAC,EAAE;MAC9BK,SAAS,GAAG,IAAIA,SAAS,EAAE;IAC7B;;IAEA;IACA,OAAO,GAAGV,MAAM,CAACE,cAAc,GAAGQ,SAAS,EAAE;EAC/C;AACF,CAAC;AAED,eAAeV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}