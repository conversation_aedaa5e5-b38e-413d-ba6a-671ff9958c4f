{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\Loading.js\";\nimport React from 'react';\nimport './Loading.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = ({\n  fullScreen = false,\n  message = 'Chargement...'\n}) => {\n  if (fullScreen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-fullscreen\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"loading-message\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"loading-message\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = Loading;\nexport default Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Loading", "fullScreen", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/components/Loading.js"], "sourcesContent": ["import React from 'react';\nimport './Loading.css';\n\nconst Loading = ({ fullScreen = false, message = 'Chargement...' }) => {\n  if (fullScreen) {\n    return (\n      <div className=\"loading-fullscreen\">\n        <div className=\"loading-spinner\"></div>\n        <p className=\"loading-message\">{message}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"loading-container\">\n      <div className=\"loading-spinner\"></div>\n      <p className=\"loading-message\">{message}</p>\n    </div>\n  );\n};\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,UAAU,GAAG,KAAK;EAAEC,OAAO,GAAG;AAAgB,CAAC,KAAK;EACrE,IAAID,UAAU,EAAE;IACd,oBACEF,OAAA;MAAKI,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCL,OAAA;QAAKI,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCT,OAAA;QAAGI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEF;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;EAEA,oBACET,OAAA;IAAKI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCL,OAAA;MAAKI,SAAS,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvCT,OAAA;MAAGI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC;AAEV,CAAC;AAACC,EAAA,GAhBIT,OAAO;AAkBb,eAAeA,OAAO;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}