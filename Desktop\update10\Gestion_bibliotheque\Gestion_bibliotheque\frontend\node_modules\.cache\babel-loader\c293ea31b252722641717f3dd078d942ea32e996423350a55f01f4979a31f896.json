{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\EbookDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { ebooksAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './BookDetail.css'; // Réutilisation du même style que BookDetail.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EbookDetail = () => {\n  _s();\n  var _currentUser$profile;\n  const {\n    id\n  } = useParams();\n  const [ebook, setEbook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    const fetchEbook = async () => {\n      try {\n        setLoading(true);\n        const response = await ebooksAPI.getById(id);\n        setEbook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement de l\\'e-book:', err);\n        setError('Erreur lors du chargement de l\\'e-book. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchEbook();\n  }, [id]);\n  const handleReadEbook = () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    // Ouvrir l'e-book dans un nouvel onglet\n    if (ebook && ebook.url) {\n      window.open(ebook.url, '_blank');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Chargement...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 12\n    }, this);\n  }\n  if (!ebook) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: \"E-book non trouv\\xE9\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-detail-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/ebooks\",\n        className: \"back-button\",\n        children: \"\\u2190 Retour aux e-books\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-actions\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/ebooks/${id}/edit`,\n          className: \"edit-button\",\n          children: \"Modifier\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-image\",\n        children: ebook.image ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: ebook.image,\n          alt: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-no-image\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pas d'image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"book-detail-title\",\n          children: ebook.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Auteur:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 16\n            }, this), \" \", ebook.autheur]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Cat\\xE9gorie:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 16\n            }, this), \" \", ebook.category_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"ISBN:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 16\n            }, this), \" \", ebook.isbn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date de publication:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 16\n            }, this), \" \", new Date(ebook.date_publication).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Format:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 16\n            }, this), \" \", ebook.format.toUpperCase()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), ebook.prix > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Prix:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 18\n            }, this), \" \", ebook.prix, \" \\u20AC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ebook.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button borrow\",\n            onClick: handleReadEbook,\n            children: \"Lire l'e-book\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-button preview\",\n            onClick: () => navigator.clipboard.writeText(ebook.url),\n            children: \"Copier le lien\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(EbookDetail, \"HE4laQQ99eyfLF6995qWyMEy8wQ=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = EbookDetail;\nexport default EbookDetail;\nvar _c;\n$RefreshReg$(_c, \"EbookDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "ebooksAPI", "useAuth", "jsxDEV", "_jsxDEV", "EbookDetail", "_s", "_currentUser$profile", "id", "ebook", "setEbook", "loading", "setLoading", "error", "setError", "isAuthenticated", "currentUser", "navigate", "isAdmin", "profile", "user_type", "is_superuser", "fetchEbook", "response", "getById", "data", "err", "console", "handleReadEbook", "url", "window", "open", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "image", "src", "alt", "titre", "autheur", "category_name", "isbn", "Date", "date_publication", "toLocaleDateString", "format", "toUpperCase", "prix", "description", "onClick", "navigator", "clipboard", "writeText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/EbookDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';\nimport { ebooksAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport './BookDetail.css'; // Réutilisation du même style que BookDetail.js\n\nconst EbookDetail = () => {\n  const { id } = useParams();\n  const [ebook, setEbook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  \n  const { isAuthenticated, currentUser } = useAuth();\n  const navigate = useNavigate();\n  \n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    const fetchEbook = async () => {\n      try {\n        setLoading(true);\n        const response = await ebooksAPI.getById(id);\n        setEbook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement de l\\'e-book:', err);\n        setError('Erreur lors du chargement de l\\'e-book. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchEbook();\n  }, [id]);\n\n  const handleReadEbook = () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    \n    // Ouvrir l'e-book dans un nouvel onglet\n    if (ebook && ebook.url) {\n      window.open(ebook.url, '_blank');\n    }\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Chargement...</div>;\n  }\n\n  if (error) {\n    return <div className=\"error\">{error}</div>;\n  }\n\n  if (!ebook) {\n    return <div className=\"error\">E-book non trouvé</div>;\n  }\n\n  return (\n    <div className=\"book-detail-container\">\n      <div className=\"book-detail-header\">\n        <Link to=\"/ebooks\" className=\"back-button\">\n          &larr; Retour aux e-books\n        </Link>\n        \n        {isAdmin && (\n          <div className=\"admin-actions\">\n            <Link to={`/ebooks/${id}/edit`} className=\"edit-button\">\n              Modifier\n            </Link>\n          </div>\n        )}\n      </div>\n      \n      <div className=\"book-detail-content\">\n        <div className=\"book-detail-image\">\n          {ebook.image ? (\n            <img src={ebook.image} alt={ebook.titre} />\n          ) : (\n            <div className=\"book-detail-no-image\">\n              <span>Pas d'image</span>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"book-detail-info\">\n          <h1 className=\"book-detail-title\">{ebook.titre}</h1>\n          \n          <div className=\"book-detail-meta\">\n            <p><strong>Auteur:</strong> {ebook.autheur}</p>\n            <p><strong>Catégorie:</strong> {ebook.category_name}</p>\n            <p><strong>ISBN:</strong> {ebook.isbn}</p>\n            <p><strong>Date de publication:</strong> {new Date(ebook.date_publication).toLocaleDateString()}</p>\n            <p><strong>Format:</strong> {ebook.format.toUpperCase()}</p>\n            {ebook.prix > 0 && (\n              <p><strong>Prix:</strong> {ebook.prix} €</p>\n            )}\n          </div>\n          \n          <div className=\"book-detail-description\">\n            <h3>Description</h3>\n            <p>{ebook.description}</p>\n          </div>\n          \n          <div className=\"book-detail-actions\">\n            <button \n              className=\"action-button borrow\"\n              onClick={handleReadEbook}\n            >\n              Lire l'e-book\n            </button>\n            \n            {isAuthenticated && (\n              <button \n                className=\"action-button preview\"\n                onClick={() => navigator.clipboard.writeText(ebook.url)}\n              >\n                Copier le lien\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EbookDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,kBAAkB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGV,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM;IAAEmB,eAAe;IAAEC;EAAY,CAAC,GAAGd,OAAO,CAAC,CAAC;EAClD,MAAMe,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,OAAO,GAAG,CAAAF,WAAW,aAAXA,WAAW,wBAAAT,oBAAA,GAAXS,WAAW,CAAEG,OAAO,cAAAZ,oBAAA,uBAApBA,oBAAA,CAAsBa,SAAS,MAAK,OAAO,KAAIJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,YAAY;EAExFxB,SAAS,CAAC,MAAM;IACd,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFV,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMW,QAAQ,GAAG,MAAMtB,SAAS,CAACuB,OAAO,CAAChB,EAAE,CAAC;QAC5CE,QAAQ,CAACa,QAAQ,CAACE,IAAI,CAAC;QACvBb,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOc,GAAG,EAAE;QACZC,OAAO,CAACd,KAAK,CAAC,yCAAyC,EAAEa,GAAG,CAAC;QAC7DZ,QAAQ,CAAC,uEAAuE,CAAC;QACjFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDU,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACd,EAAE,CAAC,CAAC;EAER,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACb,eAAe,EAAE;MACpBE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;;IAEA;IACA,IAAIR,KAAK,IAAIA,KAAK,CAACoB,GAAG,EAAE;MACtBC,MAAM,CAACC,IAAI,CAACtB,KAAK,CAACoB,GAAG,EAAE,QAAQ,CAAC;IAClC;EACF,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBAAOP,OAAA;MAAK4B,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrD;EAEA,IAAIxB,KAAK,EAAE;IACT,oBAAOT,OAAA;MAAK4B,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEpB;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7C;EAEA,IAAI,CAAC5B,KAAK,EAAE;IACV,oBAAOL,OAAA;MAAK4B,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvD;EAEA,oBACEjC,OAAA;IAAK4B,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC7B,OAAA;MAAK4B,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC7B,OAAA,CAACL,IAAI;QAACuC,EAAE,EAAC,SAAS;QAACN,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAENnB,OAAO,iBACNd,OAAA;QAAK4B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B7B,OAAA,CAACL,IAAI;UAACuC,EAAE,EAAE,WAAW9B,EAAE,OAAQ;UAACwB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjC,OAAA;MAAK4B,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC7B,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/BxB,KAAK,CAAC8B,KAAK,gBACVnC,OAAA;UAAKoC,GAAG,EAAE/B,KAAK,CAAC8B,KAAM;UAACE,GAAG,EAAEhC,KAAK,CAACiC;QAAM;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3CjC,OAAA;UAAK4B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnC7B,OAAA;YAAA6B,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7B,OAAA;UAAI4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAExB,KAAK,CAACiC;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEpDjC,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B7B,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,KAAK,CAACkC,OAAO;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,KAAK,CAACmC,aAAa;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,KAAK,CAACoC,IAAI;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIS,IAAI,CAACrC,KAAK,CAACsC,gBAAgB,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpGjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,KAAK,CAACwC,MAAM,CAACC,WAAW,CAAC,CAAC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC3D5B,KAAK,CAAC0C,IAAI,GAAG,CAAC,iBACb/C,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,KAAK,CAAC0C,IAAI,EAAC,SAAE;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAC5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7B,OAAA;YAAA6B,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBjC,OAAA;YAAA6B,QAAA,EAAIxB,KAAK,CAAC2C;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC7B,OAAA;YACE4B,SAAS,EAAC,sBAAsB;YAChCqB,OAAO,EAAEzB,eAAgB;YAAAK,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERtB,eAAe,iBACdX,OAAA;YACE4B,SAAS,EAAC,uBAAuB;YACjCqB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/C,KAAK,CAACoB,GAAG,CAAE;YAAAI,QAAA,EACzD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvHID,WAAW;EAAA,QACAP,SAAS,EAKiBI,OAAO,EAC/BF,WAAW;AAAA;AAAAyD,EAAA,GAPxBpD,WAAW;AAyHjB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}