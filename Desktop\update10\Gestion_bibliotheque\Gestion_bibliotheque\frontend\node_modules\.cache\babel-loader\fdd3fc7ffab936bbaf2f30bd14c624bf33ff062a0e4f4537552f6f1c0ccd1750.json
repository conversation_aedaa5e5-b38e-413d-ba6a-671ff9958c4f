{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\BookDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport SimilarBooks from '../components/SimilarBooks';\nimport config from '../config';\nimport './BookDetail.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BookDetail = ({\n  showAlert\n}) => {\n  _s();\n  var _currentUser$profile, _currentUser$profile2;\n  const {\n    id\n  } = useParams();\n  const [book, setBook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [actionMessage, setActionMessage] = useState({\n    text: '',\n    type: ''\n  });\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    const fetchBook = async () => {\n      try {\n        setLoading(true);\n        const response = await livresAPI.getById(id);\n\n        // Récupération des données du livre\n\n        setBook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement du livre:', err);\n        setError('Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        if (showAlert) {\n          showAlert('error', 'Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        }\n        setLoading(false);\n      }\n    };\n    fetchBook();\n  }, [id, showAlert]);\n  const handleEmprunt = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.emprunter(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n\n      // Mettre à jour les données du livre\n      const updatedBook = await livresAPI.getById(id);\n      setBook(updatedBook.data);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Erreur lors de l\\'emprunt:', err);\n      setActionMessage({\n        text: ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || 'Erreur lors de l\\'emprunt. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({\n          text: '',\n          type: ''\n        });\n      }, 5000);\n    }\n  };\n  const handleReservation = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.reserver(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Erreur lors de la réservation:', err);\n      setActionMessage({\n        text: ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || 'Erreur lors de la réservation. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({\n          text: '',\n          type: ''\n        });\n      }, 5000);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      message: \"Chargement du livre...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  if (!book) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: \"Livre non trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/books\",\n        className: \"back-link\",\n        children: \"Retour \\xE0 la liste des livres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"book-detail-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-header\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/books\",\n        className: \"back-button\",\n        children: \"\\u2190 Retour aux livres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-actions\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/books/${id}/edit`,\n          className: \"edit-button\",\n          children: \"Modifier\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), actionMessage.text && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `action-message ${actionMessage.type}`,\n      children: actionMessage.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"book-detail-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: `http://localhost:8000/static/images/livres/book_${id}.jpg`,\n          alt: book.titre,\n          onError: e => {\n            console.error(`Erreur de chargement d'image: ${e.target.src}`);\n            e.target.onerror = null;\n            e.target.src = config.DEFAULT_BOOK_IMAGE;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-detail-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"book-detail-title\",\n          children: book.titre\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Auteur:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 16\n            }, this), \" \", book.autheur]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Cat\\xE9gorie:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 16\n            }, this), \" \", book.category_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"ISBN:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 16\n            }, this), \" \", book.isbn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date de publication:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 16\n            }, this), \" \", new Date(book.date_publication).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), isAdmin && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Prix:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 20\n              }, this), \" \", book.price, \" \\u20AC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Disponibilit\\xE9:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), \" \", book.quantitie_Dispo, \" / \", book.quantitie_Total, \" exemplaires disponibles\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !isAdmin && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Disponibilit\\xE9:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), \" \", book.quantitie_Dispo > 0 ? 'Disponible' : 'Non disponible']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: book.desc\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"book-detail-actions\",\n          children: [(currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile2 = currentUser.profile) === null || _currentUser$profile2 === void 0 ? void 0 : _currentUser$profile2.user_type) === 'admin' || currentUser !== null && currentUser !== void 0 && currentUser.is_superuser ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-actions\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: `/books/${id}/edit`,\n              className: \"action-button edit\",\n              children: \"Modifier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button delete\",\n              onClick: () => {\n                if (window.confirm('Êtes-vous sûr de vouloir supprimer ce livre ?')) {\n                  livresAPI.delete(id).then(() => {\n                    navigate('/books');\n                  }).catch(err => {\n                    console.error('Erreur lors de la suppression:', err);\n                    showAlert('Erreur lors de la suppression du livre', 'error');\n                  });\n                }\n              },\n              children: \"Supprimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: book.quantitie_Dispo > 0 ? /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button borrow\",\n              onClick: handleEmprunt,\n              disabled: actionLoading,\n              children: actionLoading ? 'En cours...' : 'Emprunter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button reserve\",\n              onClick: handleReservation,\n              disabled: actionLoading,\n              children: actionLoading ? 'En cours...' : 'Réserver'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this)\n          }, void 0, false), book.url && book.url !== 'https://www.google.com' && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: book.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"action-button preview\",\n            children: \"Aper\\xE7u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SimilarBooks, {\n      bookId: id,\n      showAlert: showAlert\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(BookDetail, \"iT1jEivz54VDAj0mtViD4zJZuBk=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = BookDetail;\nexport default BookDetail;\nvar _c;\n$RefreshReg$(_c, \"BookDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "livresAPI", "useAuth", "Loading", "SimilarBooks", "config", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BookDetail", "show<PERSON><PERSON><PERSON>", "_s", "_currentUser$profile", "_currentUser$profile2", "id", "book", "setBook", "loading", "setLoading", "error", "setError", "actionLoading", "setActionLoading", "actionMessage", "setActionMessage", "text", "type", "isAuthenticated", "currentUser", "navigate", "isAdmin", "profile", "user_type", "is_superuser", "fetchBook", "response", "getById", "data", "err", "console", "handleEmprunt", "emprunter", "detail", "updatedBook", "_err$response", "_err$response$data", "setTimeout", "handleReservation", "reserver", "_err$response2", "_err$response2$data", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "to", "src", "alt", "titre", "onError", "e", "target", "onerror", "DEFAULT_BOOK_IMAGE", "autheur", "category_name", "isbn", "Date", "date_publication", "toLocaleDateString", "price", "quantitie_Dispo", "quantitie_Total", "desc", "confirm", "delete", "then", "catch", "disabled", "url", "href", "rel", "bookId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/BookDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';\nimport { livresAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport SimilarBooks from '../components/SimilarBooks';\nimport config from '../config';\nimport './BookDetail.css';\n\nconst BookDetail = ({ showAlert }) => {\n  const { id } = useParams();\n  const [book, setBook] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [actionMessage, setActionMessage] = useState({ text: '', type: '' });\n\n  const { isAuthenticated, currentUser } = useAuth();\n  const navigate = useNavigate();\n\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    const fetchBook = async () => {\n      try {\n        setLoading(true);\n        const response = await livresAPI.getById(id);\n\n        // Récupération des données du livre\n\n        setBook(response.data);\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement du livre:', err);\n        setError('Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        if (showAlert) {\n          showAlert('error', 'Erreur lors du chargement du livre. Veuillez réessayer plus tard.');\n        }\n        setLoading(false);\n      }\n    };\n\n    fetchBook();\n  }, [id, showAlert]);\n\n  const handleEmprunt = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.emprunter(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n\n      // Mettre à jour les données du livre\n      const updatedBook = await livresAPI.getById(id);\n      setBook(updatedBook.data);\n    } catch (err) {\n      console.error('Erreur lors de l\\'emprunt:', err);\n      setActionMessage({\n        text: err.response?.data?.detail || 'Erreur lors de l\\'emprunt. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({ text: '', type: '' });\n      }, 5000);\n    }\n  };\n\n  const handleReservation = async () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    try {\n      setActionLoading(true);\n      const response = await livresAPI.reserver(id);\n      setActionMessage({\n        text: response.data.detail,\n        type: 'success'\n      });\n    } catch (err) {\n      console.error('Erreur lors de la réservation:', err);\n      setActionMessage({\n        text: err.response?.data?.detail || 'Erreur lors de la réservation. Veuillez réessayer.',\n        type: 'error'\n      });\n    } finally {\n      setActionLoading(false);\n\n      // Effacer le message après 5 secondes\n      setTimeout(() => {\n        setActionMessage({ text: '', type: '' });\n      }, 5000);\n    }\n  };\n\n  if (loading) {\n    return <Loading message=\"Chargement du livre...\" />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">{error}</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  if (!book) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">Livre non trouvé</p>\n        <Link to=\"/books\" className=\"back-link\">\n          Retour à la liste des livres\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"book-detail-container\">\n      <div className=\"book-detail-header\">\n        <Link to=\"/books\" className=\"back-button\">\n          &larr; Retour aux livres\n        </Link>\n\n        {isAdmin && (\n          <div className=\"admin-actions\">\n            <Link to={`/books/${id}/edit`} className=\"edit-button\">\n              Modifier\n            </Link>\n          </div>\n        )}\n      </div>\n\n      {actionMessage.text && (\n        <div className={`action-message ${actionMessage.type}`}>\n          {actionMessage.text}\n        </div>\n      )}\n\n      <div className=\"book-detail-content\">\n        <div className=\"book-detail-image\">\n          <img\n            src={`http://localhost:8000/static/images/livres/book_${id}.jpg`}\n            alt={book.titre}\n            onError={(e) => {\n              console.error(`Erreur de chargement d'image: ${e.target.src}`);\n              e.target.onerror = null;\n              e.target.src = config.DEFAULT_BOOK_IMAGE;\n            }}\n          />\n        </div>\n\n        <div className=\"book-detail-info\">\n          <h1 className=\"book-detail-title\">{book.titre}</h1>\n\n          <div className=\"book-detail-meta\">\n            <p><strong>Auteur:</strong> {book.autheur}</p>\n            <p><strong>Catégorie:</strong> {book.category_name}</p>\n            <p><strong>ISBN:</strong> {book.isbn}</p>\n            <p><strong>Date de publication:</strong> {new Date(book.date_publication).toLocaleDateString()}</p>\n            {isAdmin && (\n              <>\n                <p><strong>Prix:</strong> {book.price} €</p>\n                <p className={`book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`}>\n                  <strong>Disponibilité:</strong> {book.quantitie_Dispo} / {book.quantitie_Total} exemplaires disponibles\n                </p>\n              </>\n            )}\n            {!isAdmin && (\n              <p className={`book-detail-availability ${book.quantitie_Dispo > 0 ? 'available' : 'unavailable'}`}>\n                <strong>Disponibilité:</strong> {book.quantitie_Dispo > 0 ? 'Disponible' : 'Non disponible'}\n              </p>\n            )}\n          </div>\n\n          <div className=\"book-detail-description\">\n            <h3>Description</h3>\n            <p>{book.desc}</p>\n          </div>\n\n          <div className=\"book-detail-actions\">\n            {currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser ? (\n              <div className=\"admin-actions\">\n                <Link\n                  to={`/books/${id}/edit`}\n                  className=\"action-button edit\"\n                >\n                  Modifier\n                </Link>\n                <button\n                  className=\"action-button delete\"\n                  onClick={() => {\n                    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce livre ?')) {\n                      livresAPI.delete(id)\n                        .then(() => {\n                          navigate('/books');\n                        })\n                        .catch(err => {\n                          console.error('Erreur lors de la suppression:', err);\n                          showAlert('Erreur lors de la suppression du livre', 'error');\n                        });\n                    }\n                  }}\n                >\n                  Supprimer\n                </button>\n              </div>\n            ) : (\n              <>\n                {book.quantitie_Dispo > 0 ? (\n                  <button\n                    className=\"action-button borrow\"\n                    onClick={handleEmprunt}\n                    disabled={actionLoading}\n                  >\n                    {actionLoading ? 'En cours...' : 'Emprunter'}\n                  </button>\n                ) : (\n                  <button\n                    className=\"action-button reserve\"\n                    onClick={handleReservation}\n                    disabled={actionLoading}\n                  >\n                    {actionLoading ? 'En cours...' : 'Réserver'}\n                  </button>\n                )}\n              </>\n            )}\n\n            {book.url && book.url !== 'https://www.google.com' && (\n              <a\n                href={book.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"action-button preview\"\n              >\n                Aperçu\n              </a>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Livres similaires */}\n      <SimilarBooks bookId={id} showAlert={showAlert} />\n    </div>\n  );\n};\n\nexport default BookDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC;IAAE8B,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE1E,MAAM;IAAEC,eAAe;IAAEC;EAAY,CAAC,GAAG3B,OAAO,CAAC,CAAC;EAClD,MAAM4B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B,MAAM+B,OAAO,GAAG,CAAAF,WAAW,aAAXA,WAAW,wBAAAhB,oBAAA,GAAXgB,WAAW,CAAEG,OAAO,cAAAnB,oBAAA,uBAApBA,oBAAA,CAAsBoB,SAAS,MAAK,OAAO,KAAIJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,YAAY;EAExFrC,SAAS,CAAC,MAAM;IACd,MAAMsC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFhB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMiB,QAAQ,GAAG,MAAMnC,SAAS,CAACoC,OAAO,CAACtB,EAAE,CAAC;;QAE5C;;QAEAE,OAAO,CAACmB,QAAQ,CAACE,IAAI,CAAC;QACtBnB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZC,OAAO,CAACpB,KAAK,CAAC,qCAAqC,EAAEmB,GAAG,CAAC;QACzDlB,QAAQ,CAAC,mEAAmE,CAAC;QAC7E,IAAIV,SAAS,EAAE;UACbA,SAAS,CAAC,OAAO,EAAE,mEAAmE,CAAC;QACzF;QACAQ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACpB,EAAE,EAAEJ,SAAS,CAAC,CAAC;EAEnB,MAAM8B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACb,eAAe,EAAE;MACpBE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI;MACFP,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMa,QAAQ,GAAG,MAAMnC,SAAS,CAACyC,SAAS,CAAC3B,EAAE,CAAC;MAC9CU,gBAAgB,CAAC;QACfC,IAAI,EAAEU,QAAQ,CAACE,IAAI,CAACK,MAAM;QAC1BhB,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,MAAMiB,WAAW,GAAG,MAAM3C,SAAS,CAACoC,OAAO,CAACtB,EAAE,CAAC;MAC/CE,OAAO,CAAC2B,WAAW,CAACN,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAM,aAAA,EAAAC,kBAAA;MACZN,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEmB,GAAG,CAAC;MAChDd,gBAAgB,CAAC;QACfC,IAAI,EAAE,EAAAmB,aAAA,GAAAN,GAAG,CAACH,QAAQ,cAAAS,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcP,IAAI,cAAAQ,kBAAA,uBAAlBA,kBAAA,CAAoBH,MAAM,KAAI,gDAAgD;QACpFhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,SAAS;MACRJ,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAwB,UAAU,CAAC,MAAM;QACftB,gBAAgB,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;MAC1C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpB,eAAe,EAAE;MACpBE,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI;MACFP,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMa,QAAQ,GAAG,MAAMnC,SAAS,CAACgD,QAAQ,CAAClC,EAAE,CAAC;MAC7CU,gBAAgB,CAAC;QACfC,IAAI,EAAEU,QAAQ,CAACE,IAAI,CAACK,MAAM;QAC1BhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,GAAG,EAAE;MAAA,IAAAW,cAAA,EAAAC,mBAAA;MACZX,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEmB,GAAG,CAAC;MACpDd,gBAAgB,CAAC;QACfC,IAAI,EAAE,EAAAwB,cAAA,GAAAX,GAAG,CAACH,QAAQ,cAAAc,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBR,MAAM,KAAI,oDAAoD;QACxFhB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,SAAS;MACRJ,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAwB,UAAU,CAAC,MAAM;QACftB,gBAAgB,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;MAC1C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,oBAAOX,OAAA,CAACJ,OAAO;MAACiD,OAAO,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,IAAIpC,KAAK,EAAE;IACT,oBACEb,OAAA;MAAKkD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnD,OAAA;QAAGkD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEtC;MAAK;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxCjD,OAAA;QACEkD,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACxC,IAAI,EAAE;IACT,oBACET,OAAA;MAAKkD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnD,OAAA;QAAGkD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACjDjD,OAAA,CAACR,IAAI;QAACgE,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAExC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEjD,OAAA;IAAKkD,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCnD,OAAA;MAAKkD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCnD,OAAA,CAACR,IAAI;QAACgE,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE1C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAENzB,OAAO,iBACNxB,OAAA;QAAKkD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BnD,OAAA,CAACR,IAAI;UAACgE,EAAE,EAAE,UAAUhD,EAAE,OAAQ;UAAC0C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEvD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELhC,aAAa,CAACE,IAAI,iBACjBnB,OAAA;MAAKkD,SAAS,EAAE,kBAAkBjC,aAAa,CAACG,IAAI,EAAG;MAAA+B,QAAA,EACpDlC,aAAa,CAACE;IAAI;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN,eAEDjD,OAAA;MAAKkD,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCnD,OAAA;QAAKkD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCnD,OAAA;UACEyD,GAAG,EAAE,mDAAmDjD,EAAE,MAAO;UACjEkD,GAAG,EAAEjD,IAAI,CAACkD,KAAM;UAChBC,OAAO,EAAGC,CAAC,IAAK;YACd5B,OAAO,CAACpB,KAAK,CAAC,iCAAiCgD,CAAC,CAACC,MAAM,CAACL,GAAG,EAAE,CAAC;YAC9DI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;YACvBF,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG3D,MAAM,CAACkE,kBAAkB;UAC1C;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BnD,OAAA;UAAIkD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAE1C,IAAI,CAACkD;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAEnDjD,OAAA;UAAKkD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BnD,OAAA;YAAAmD,QAAA,gBAAGnD,OAAA;cAAAmD,QAAA,EAAQ;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAACwD,OAAO;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CjD,OAAA;YAAAmD,QAAA,gBAAGnD,OAAA;cAAAmD,QAAA,EAAQ;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAACyD,aAAa;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDjD,OAAA;YAAAmD,QAAA,gBAAGnD,OAAA;cAAAmD,QAAA,EAAQ;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC0D,IAAI;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCjD,OAAA;YAAAmD,QAAA,gBAAGnD,OAAA;cAAAmD,QAAA,EAAQ;YAAoB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAImB,IAAI,CAAC3D,IAAI,CAAC4D,gBAAgB,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClGzB,OAAO,iBACNxB,OAAA,CAAAE,SAAA;YAAAiD,QAAA,gBACEnD,OAAA;cAAAmD,QAAA,gBAAGnD,OAAA;gBAAAmD,QAAA,EAAQ;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC8D,KAAK,EAAC,SAAE;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5CjD,OAAA;cAAGkD,SAAS,EAAE,4BAA4BzC,IAAI,CAAC+D,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,EAAG;cAAArB,QAAA,gBACjGnD,OAAA;gBAAAmD,QAAA,EAAQ;cAAc;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC+D,eAAe,EAAC,KAAG,EAAC/D,IAAI,CAACgE,eAAe,EAAC,0BACjF;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CACH,EACA,CAACzB,OAAO,iBACPxB,OAAA;YAAGkD,SAAS,EAAE,4BAA4BzC,IAAI,CAAC+D,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,EAAG;YAAArB,QAAA,gBACjGnD,OAAA;cAAAmD,QAAA,EAAQ;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxC,IAAI,CAAC+D,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG,gBAAgB;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjD,OAAA;UAAKkD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCnD,OAAA;YAAAmD,QAAA,EAAI;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBjD,OAAA;YAAAmD,QAAA,EAAI1C,IAAI,CAACiE;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAENjD,OAAA;UAAKkD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjC,CAAA7B,WAAW,aAAXA,WAAW,wBAAAf,qBAAA,GAAXe,WAAW,CAAEG,OAAO,cAAAlB,qBAAA,uBAApBA,qBAAA,CAAsBmB,SAAS,MAAK,OAAO,IAAIJ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEK,YAAY,gBACvE3B,OAAA;YAAKkD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnD,OAAA,CAACR,IAAI;cACHgE,EAAE,EAAE,UAAUhD,EAAE,OAAQ;cACxB0C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC/B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjD,OAAA;cACEkD,SAAS,EAAC,sBAAsB;cAChCE,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIC,MAAM,CAACsB,OAAO,CAAC,+CAA+C,CAAC,EAAE;kBACnEjF,SAAS,CAACkF,MAAM,CAACpE,EAAE,CAAC,CACjBqE,IAAI,CAAC,MAAM;oBACVtD,QAAQ,CAAC,QAAQ,CAAC;kBACpB,CAAC,CAAC,CACDuD,KAAK,CAAC9C,GAAG,IAAI;oBACZC,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEmB,GAAG,CAAC;oBACpD5B,SAAS,CAAC,wCAAwC,EAAE,OAAO,CAAC;kBAC9D,CAAC,CAAC;gBACN;cACF,CAAE;cAAA+C,QAAA,EACH;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENjD,OAAA,CAAAE,SAAA;YAAAiD,QAAA,EACG1C,IAAI,CAAC+D,eAAe,GAAG,CAAC,gBACvBxE,OAAA;cACEkD,SAAS,EAAC,sBAAsB;cAChCE,OAAO,EAAElB,aAAc;cACvB6C,QAAQ,EAAEhE,aAAc;cAAAoC,QAAA,EAEvBpC,aAAa,GAAG,aAAa,GAAG;YAAW;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,gBAETjD,OAAA;cACEkD,SAAS,EAAC,uBAAuB;cACjCE,OAAO,EAAEX,iBAAkB;cAC3BsC,QAAQ,EAAEhE,aAAc;cAAAoC,QAAA,EAEvBpC,aAAa,GAAG,aAAa,GAAG;YAAU;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UACT,gBACD,CACH,EAEAxC,IAAI,CAACuE,GAAG,IAAIvE,IAAI,CAACuE,GAAG,KAAK,wBAAwB,iBAChDhF,OAAA;YACEiF,IAAI,EAAExE,IAAI,CAACuE,GAAI;YACflB,MAAM,EAAC,QAAQ;YACfoB,GAAG,EAAC,qBAAqB;YACzBhC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClC;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjD,OAAA,CAACH,YAAY;MAACsF,MAAM,EAAE3E,EAAG;MAACJ,SAAS,EAAEA;IAAU;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAjQIF,UAAU;EAAA,QACCZ,SAAS,EAOiBI,OAAO,EAC/BF,WAAW;AAAA;AAAA2F,EAAA,GATxBjF,UAAU;AAmQhB,eAAeA,UAAU;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}