{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Gestion_bibliotheque\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\pages\\\\EditEbook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { ebooksAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditEbook = () => {\n  _s();\n  var _currentUser$profile;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    currentUser\n  } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    description: '',\n    date_publication: '',\n    isbn: '',\n    format: 'pdf',\n    url: '',\n    prix: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$profile = currentUser.profile) === null || _currentUser$profile === void 0 ? void 0 : _currentUser$profile.user_type) === 'admin' || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.is_superuser);\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    if (!isAdmin) {\n      navigate('/ebooks');\n      return;\n    }\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les catégories\n        const categoriesResponse = await ebooksAPI.getCategories();\n        // S'assurer que categories est bien un tableau\n        if (Array.isArray(categoriesResponse.data)) {\n          setCategories(categoriesResponse.data);\n        } else if (categoriesResponse.data && typeof categoriesResponse.data === 'object') {\n          // Si c'est un objet, essayer de le convertir en tableau\n          setCategories(Object.values(categoriesResponse.data));\n        } else {\n          console.error('Format de catégories inattendu:', categoriesResponse.data);\n          setCategories([]);\n        }\n\n        // Récupérer les données de l'ebook\n        const ebookResponse = await ebooksAPI.getById(id);\n        const ebook = ebookResponse.data;\n\n        // Formater la date pour l'input date\n        const datePublication = ebook.date_publication ? new Date(ebook.date_publication).toISOString().split('T')[0] : '';\n        setFormData({\n          titre: ebook.titre || '',\n          autheur: ebook.autheur || '',\n          category: ebook.category || '',\n          description: ebook.description || '',\n          date_publication: datePublication,\n          isbn: ebook.isbn || '',\n          format: ebook.format || 'pdf',\n          url: ebook.url || '',\n          prix: ebook.prix || '',\n          image: null\n        });\n\n        // Définir l'aperçu de l'image si elle existe\n        if (ebook.image_url) {\n          setImagePreview(ebook.image_url);\n        } else if (ebook.image) {\n          setImagePreview(`http://localhost:8000/media/ebook_${id}.jpg`);\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      files\n    } = e.target;\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n\n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n\n      // Envoyer les données au serveur\n      await ebooksAPI.update(id, data);\n\n      // Rediriger vers la page de détail de l'ebook\n      navigate(`/ebooks/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification de l\\'ebook:', err);\n      setError('Erreur lors de la modification de l\\'ebook. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loading, {\n      message: \"Chargement des donn\\xE9es...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"retry-button\",\n        onClick: () => window.location.reload(),\n        children: \"R\\xE9essayer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-book-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"add-book-title\",\n      children: \"Modifier un e-book\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-book-card\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"add-book-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"titre\",\n              children: [\"Titre \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"titre\",\n              name: \"titre\",\n              value: formData.titre,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"autheur\",\n              children: [\"Auteur \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"autheur\",\n              name: \"autheur\",\n              value: formData.autheur,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: [\"Cat\\xE9gorie \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 51\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"S\\xE9lectionner une cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), Array.isArray(categories) && categories.filter(category => category && category.id && category.name) // Filtrer les éléments null ou undefined\n              .map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"isbn\",\n              children: [\"ISBN \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"isbn\",\n              name: \"isbn\",\n              value: formData.isbn,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"format\",\n              children: [\"Format \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"format\",\n              name: \"format\",\n              value: formData.format,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"pdf\",\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"epub\",\n                children: \"EPUB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"mobi\",\n                children: \"MOBI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"date_publication\",\n              children: [\"Date de publication \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 69\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"date_publication\",\n              name: \"date_publication\",\n              value: formData.date_publication,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"prix\",\n              children: [\"Prix \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 42\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"prix\",\n              name: \"prix\",\n              value: formData.prix,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"url\",\n              children: [\"URL de l'e-book \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"required\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 52\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              id: \"url\",\n              name: \"url\",\n              value: formData.url,\n              onChange: handleChange,\n              placeholder: \"https://example.com/ebook.pdf\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: [\"Description \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"required\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 54\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: \"Image (optionnel)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            name: \"image\",\n            onChange: handleChange,\n            accept: \"image/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Aper\\xE7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: `/ebooks/${id}`,\n            className: \"cancel-button\",\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            children: \"Enregistrer les modifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(EditEbook, \"yw9M1oaQZEH6X3abVYMP/80etLY=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = EditEbook;\nexport default EditEbook;\nvar _c;\n$RefreshReg$(_c, \"EditEbook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "ebooksAPI", "useAuth", "Loading", "jsxDEV", "_jsxDEV", "EditEbook", "_s", "_currentUser$profile", "id", "navigate", "isAuthenticated", "currentUser", "loading", "setLoading", "error", "setError", "categories", "setCategories", "imagePreview", "setImagePreview", "formData", "setFormData", "titre", "autheur", "category", "description", "date_publication", "isbn", "format", "url", "prix", "image", "isAdmin", "profile", "user_type", "is_superuser", "fetchData", "categoriesResponse", "getCategories", "Array", "isArray", "data", "Object", "values", "console", "ebookResponse", "getById", "ebook", "datePublication", "Date", "toISOString", "split", "image_url", "err", "handleChange", "e", "name", "value", "type", "files", "target", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "FormData", "keys", "for<PERSON>ach", "key", "append", "update", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "window", "location", "reload", "onSubmit", "htmlFor", "onChange", "required", "filter", "map", "placeholder", "accept", "src", "alt", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Gestion_bibliotheque/Gestion_bibliotheque/frontend/src/pages/EditEbook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { ebooksAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport Loading from '../components/Loading';\nimport './AddBook.css';\n\nconst EditEbook = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { isAuthenticated, currentUser } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [categories, setCategories] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    titre: '',\n    autheur: '',\n    category: '',\n    description: '',\n    date_publication: '',\n    isbn: '',\n    format: 'pdf',\n    url: '',\n    prix: '',\n    image: null\n  });\n\n  // Vérifier si l'utilisateur est admin\n  const isAdmin = currentUser?.profile?.user_type === 'admin' || currentUser?.is_superuser;\n\n  useEffect(() => {\n    // Rediriger si l'utilisateur n'est pas authentifié ou n'est pas admin\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n\n    if (!isAdmin) {\n      navigate('/ebooks');\n      return;\n    }\n\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les catégories\n        const categoriesResponse = await ebooksAPI.getCategories();\n        // S'assurer que categories est bien un tableau\n        if (Array.isArray(categoriesResponse.data)) {\n          setCategories(categoriesResponse.data);\n        } else if (categoriesResponse.data && typeof categoriesResponse.data === 'object') {\n          // Si c'est un objet, essayer de le convertir en tableau\n          setCategories(Object.values(categoriesResponse.data));\n        } else {\n          console.error('Format de catégories inattendu:', categoriesResponse.data);\n          setCategories([]);\n        }\n\n        // Récupérer les données de l'ebook\n        const ebookResponse = await ebooksAPI.getById(id);\n        const ebook = ebookResponse.data;\n\n        // Formater la date pour l'input date\n        const datePublication = ebook.date_publication\n          ? new Date(ebook.date_publication).toISOString().split('T')[0]\n          : '';\n\n        setFormData({\n          titre: ebook.titre || '',\n          autheur: ebook.autheur || '',\n          category: ebook.category || '',\n          description: ebook.description || '',\n          date_publication: datePublication,\n          isbn: ebook.isbn || '',\n          format: ebook.format || 'pdf',\n          url: ebook.url || '',\n          prix: ebook.prix || '',\n          image: null\n        });\n\n        // Définir l'aperçu de l'image si elle existe\n        if (ebook.image_url) {\n          setImagePreview(ebook.image_url);\n        } else if (ebook.image) {\n          setImagePreview(`http://localhost:8000/media/ebook_${id}.jpg`);\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Erreur lors du chargement des données:', err);\n        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [id, isAuthenticated, isAdmin, navigate]);\n\n  const handleChange = (e) => {\n    const { name, value, type, files } = e.target;\n\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n\n      // Créer un aperçu de l'image\n      if (files[0]) {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreview(reader.result);\n        };\n        reader.readAsDataURL(files[0]);\n      }\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    try {\n      setLoading(true);\n\n      // Créer un objet FormData pour envoyer les données, y compris l'image\n      const data = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== null) {\n          data.append(key, formData[key]);\n        }\n      });\n\n      // Envoyer les données au serveur\n      await ebooksAPI.update(id, data);\n\n      // Rediriger vers la page de détail de l'ebook\n      navigate(`/ebooks/${id}`);\n    } catch (err) {\n      console.error('Erreur lors de la modification de l\\'ebook:', err);\n      setError('Erreur lors de la modification de l\\'ebook. Veuillez réessayer.');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <Loading message=\"Chargement des données...\" />;\n  }\n\n  if (error) {\n    return (\n      <div className=\"error-container\">\n        <p className=\"error-message\">{error}</p>\n        <button\n          className=\"retry-button\"\n          onClick={() => window.location.reload()}\n        >\n          Réessayer\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"add-book-container\">\n      <h1 className=\"add-book-title\">Modifier un e-book</h1>\n\n      <div className=\"add-book-card\">\n        <form className=\"add-book-form\" onSubmit={handleSubmit}>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"titre\">Titre <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"titre\"\n                name=\"titre\"\n                value={formData.titre}\n                onChange={handleChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"autheur\">Auteur <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"autheur\"\n                name=\"autheur\"\n                value={formData.autheur}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"category\">Catégorie <span className=\"required\">*</span></label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"\">Sélectionner une catégorie</option>\n                {Array.isArray(categories) && categories\n                  .filter(category => category && category.id && category.name) // Filtrer les éléments null ou undefined\n                  .map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"isbn\">ISBN <span className=\"required\">*</span></label>\n              <input\n                type=\"text\"\n                id=\"isbn\"\n                name=\"isbn\"\n                value={formData.isbn}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"format\">Format <span className=\"required\">*</span></label>\n              <select\n                id=\"format\"\n                name=\"format\"\n                value={formData.format}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"pdf\">PDF</option>\n                <option value=\"epub\">EPUB</option>\n                <option value=\"mobi\">MOBI</option>\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"date_publication\">Date de publication <span className=\"required\">*</span></label>\n              <input\n                type=\"date\"\n                id=\"date_publication\"\n                name=\"date_publication\"\n                value={formData.date_publication}\n                onChange={handleChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"prix\">Prix <span className=\"required\">*</span></label>\n              <input\n                type=\"number\"\n                id=\"prix\"\n                name=\"prix\"\n                value={formData.prix}\n                onChange={handleChange}\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"url\">URL de l'e-book <span className=\"required\">*</span></label>\n              <input\n                type=\"url\"\n                id=\"url\"\n                name=\"url\"\n                value={formData.url}\n                onChange={handleChange}\n                placeholder=\"https://example.com/ebook.pdf\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\">Description <span className=\"required\">*</span></label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              required\n            ></textarea>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"image\">Image (optionnel)</label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              name=\"image\"\n              onChange={handleChange}\n              accept=\"image/*\"\n            />\n            {imagePreview && (\n              <div className=\"image-preview\">\n                <img src={imagePreview} alt=\"Aperçu\" />\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-actions\">\n            <Link to={`/ebooks/${id}`} className=\"cancel-button\">Annuler</Link>\n            <button type=\"submit\" className=\"submit-button\">Enregistrer les modifications</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default EditEbook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACtB,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,eAAe;IAAEC;EAAY,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,KAAK;IACbC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAArB,WAAW,aAAXA,WAAW,wBAAAJ,oBAAA,GAAXI,WAAW,CAAEsB,OAAO,cAAA1B,oBAAA,uBAApBA,oBAAA,CAAsB2B,SAAS,MAAK,OAAO,KAAIvB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,YAAY;EAExFvC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACc,eAAe,EAAE;MACpBD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI,CAACuB,OAAO,EAAE;MACZvB,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEA,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFvB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMwB,kBAAkB,GAAG,MAAMrC,SAAS,CAACsC,aAAa,CAAC,CAAC;QAC1D;QACA,IAAIC,KAAK,CAACC,OAAO,CAACH,kBAAkB,CAACI,IAAI,CAAC,EAAE;UAC1CxB,aAAa,CAACoB,kBAAkB,CAACI,IAAI,CAAC;QACxC,CAAC,MAAM,IAAIJ,kBAAkB,CAACI,IAAI,IAAI,OAAOJ,kBAAkB,CAACI,IAAI,KAAK,QAAQ,EAAE;UACjF;UACAxB,aAAa,CAACyB,MAAM,CAACC,MAAM,CAACN,kBAAkB,CAACI,IAAI,CAAC,CAAC;QACvD,CAAC,MAAM;UACLG,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEuB,kBAAkB,CAACI,IAAI,CAAC;UACzExB,aAAa,CAAC,EAAE,CAAC;QACnB;;QAEA;QACA,MAAM4B,aAAa,GAAG,MAAM7C,SAAS,CAAC8C,OAAO,CAACtC,EAAE,CAAC;QACjD,MAAMuC,KAAK,GAAGF,aAAa,CAACJ,IAAI;;QAEhC;QACA,MAAMO,eAAe,GAAGD,KAAK,CAACrB,gBAAgB,GAC1C,IAAIuB,IAAI,CAACF,KAAK,CAACrB,gBAAgB,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC5D,EAAE;QAEN9B,WAAW,CAAC;UACVC,KAAK,EAAEyB,KAAK,CAACzB,KAAK,IAAI,EAAE;UACxBC,OAAO,EAAEwB,KAAK,CAACxB,OAAO,IAAI,EAAE;UAC5BC,QAAQ,EAAEuB,KAAK,CAACvB,QAAQ,IAAI,EAAE;UAC9BC,WAAW,EAAEsB,KAAK,CAACtB,WAAW,IAAI,EAAE;UACpCC,gBAAgB,EAAEsB,eAAe;UACjCrB,IAAI,EAAEoB,KAAK,CAACpB,IAAI,IAAI,EAAE;UACtBC,MAAM,EAAEmB,KAAK,CAACnB,MAAM,IAAI,KAAK;UAC7BC,GAAG,EAAEkB,KAAK,CAAClB,GAAG,IAAI,EAAE;UACpBC,IAAI,EAAEiB,KAAK,CAACjB,IAAI,IAAI,EAAE;UACtBC,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACA,IAAIgB,KAAK,CAACK,SAAS,EAAE;UACnBjC,eAAe,CAAC4B,KAAK,CAACK,SAAS,CAAC;QAClC,CAAC,MAAM,IAAIL,KAAK,CAAChB,KAAK,EAAE;UACtBZ,eAAe,CAAC,qCAAqCX,EAAE,MAAM,CAAC;QAChE;QAEAK,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOwC,GAAG,EAAE;QACZT,OAAO,CAAC9B,KAAK,CAAC,wCAAwC,EAAEuC,GAAG,CAAC;QAC5DtC,QAAQ,CAAC,sEAAsE,CAAC;QAChFF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC5B,EAAE,EAAEE,eAAe,EAAEsB,OAAO,EAAEvB,QAAQ,CAAC,CAAC;EAE5C,MAAM6C,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAE7C,IAAIF,IAAI,KAAK,MAAM,EAAE;MACnBrC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACoC,IAAI,GAAGG,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACA,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACZ,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvB5C,eAAe,CAAC0C,MAAM,CAACG,MAAM,CAAC;QAChC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACLtC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACoC,IAAI,GAAGC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI;MACFtD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM4B,IAAI,GAAG,IAAI2B,QAAQ,CAAC,CAAC;MAC3B1B,MAAM,CAAC2B,IAAI,CAACjD,QAAQ,CAAC,CAACkD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAInD,QAAQ,CAACmD,GAAG,CAAC,KAAK,IAAI,EAAE;UAC1B9B,IAAI,CAAC+B,MAAM,CAACD,GAAG,EAAEnD,QAAQ,CAACmD,GAAG,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMvE,SAAS,CAACyE,MAAM,CAACjE,EAAE,EAAEiC,IAAI,CAAC;;MAEhC;MACAhC,QAAQ,CAAC,WAAWD,EAAE,EAAE,CAAC;IAC3B,CAAC,CAAC,OAAO6C,GAAG,EAAE;MACZT,OAAO,CAAC9B,KAAK,CAAC,6CAA6C,EAAEuC,GAAG,CAAC;MACjEtC,QAAQ,CAAC,iEAAiE,CAAC;MAC3EF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACF,OAAO;MAACwE,OAAO,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxD;EAEA,IAAIhE,KAAK,EAAE;IACT,oBACEV,OAAA;MAAK2E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5E,OAAA;QAAG2E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAElE;MAAK;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC1E,OAAA;QACE2E,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAJ,QAAA,EACzC;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1E,OAAA;IAAK2E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC5E,OAAA;MAAI2E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAkB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEtD1E,OAAA;MAAK2E,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B5E,OAAA;QAAM2E,SAAS,EAAC,eAAe;QAACM,QAAQ,EAAEnB,YAAa;QAAAc,QAAA,gBACrD5E,OAAA;UAAK2E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,QAAM,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxE1E,OAAA;cACEsD,IAAI,EAAC,MAAM;cACXlD,EAAE,EAAC,OAAO;cACVgD,IAAI,EAAC,OAAO;cACZC,KAAK,EAAErC,QAAQ,CAACE,KAAM;cACtBiE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,SAAS;cAAAN,QAAA,GAAC,SAAO,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E1E,OAAA;cACEsD,IAAI,EAAC,MAAM;cACXlD,EAAE,EAAC,SAAS;cACZgD,IAAI,EAAC,SAAS;cACdC,KAAK,EAAErC,QAAQ,CAACG,OAAQ;cACxBgE,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,UAAU;cAAAN,QAAA,GAAC,eAAU,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E1E,OAAA;cACEI,EAAE,EAAC,UAAU;cACbgD,IAAI,EAAC,UAAU;cACfC,KAAK,EAAErC,QAAQ,CAACI,QAAS;cACzB+D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;cAAAR,QAAA,gBAER5E,OAAA;gBAAQqD,KAAK,EAAC,EAAE;gBAAAuB,QAAA,EAAC;cAA0B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDvC,KAAK,CAACC,OAAO,CAACxB,UAAU,CAAC,IAAIA,UAAU,CACrCyE,MAAM,CAACjE,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAChB,EAAE,IAAIgB,QAAQ,CAACgC,IAAI,CAAC,CAAC;cAAA,CAC7DkC,GAAG,CAAClE,QAAQ,iBACXpB,OAAA;gBAA0BqD,KAAK,EAAEjC,QAAQ,CAAChB,EAAG;gBAAAwE,QAAA,EAC1CxD,QAAQ,CAACgC;cAAI,GADHhC,QAAQ,CAAChB,EAAE;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1E,OAAA;cACEsD,IAAI,EAAC,MAAM;cACXlD,EAAE,EAAC,MAAM;cACTgD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErC,QAAQ,CAACO,IAAK;cACrB4D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,QAAQ;cAAAN,QAAA,GAAC,SAAO,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1E1E,OAAA;cACEI,EAAE,EAAC,QAAQ;cACXgD,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAErC,QAAQ,CAACQ,MAAO;cACvB2D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;cAAAR,QAAA,gBAER5E,OAAA;gBAAQqD,KAAK,EAAC,KAAK;gBAAAuB,QAAA,EAAC;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC1E,OAAA;gBAAQqD,KAAK,EAAC,MAAM;gBAAAuB,QAAA,EAAC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC1E,OAAA;gBAAQqD,KAAK,EAAC,MAAM;gBAAAuB,QAAA,EAAC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,kBAAkB;cAAAN,QAAA,GAAC,sBAAoB,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjG1E,OAAA;cACEsD,IAAI,EAAC,MAAM;cACXlD,EAAE,EAAC,kBAAkB;cACrBgD,IAAI,EAAC,kBAAkB;cACvBC,KAAK,EAAErC,QAAQ,CAACM,gBAAiB;cACjC6D,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,MAAM;cAAAN,QAAA,GAAC,OAAK,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1E,OAAA;cACEsD,IAAI,EAAC,QAAQ;cACblD,EAAE,EAAC,MAAM;cACTgD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErC,QAAQ,CAACU,IAAK;cACrByD,QAAQ,EAAEjC,YAAa;cACvBkC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5E,OAAA;cAAOkF,OAAO,EAAC,KAAK;cAAAN,QAAA,GAAC,kBAAgB,eAAA5E,OAAA;gBAAM2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF1E,OAAA;cACEsD,IAAI,EAAC,KAAK;cACVlD,EAAE,EAAC,KAAK;cACRgD,IAAI,EAAC,KAAK;cACVC,KAAK,EAAErC,QAAQ,CAACS,GAAI;cACpB0D,QAAQ,EAAEjC,YAAa;cACvBqC,WAAW,EAAC,+BAA+B;cAC3CH,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5E,OAAA;YAAOkF,OAAO,EAAC,aAAa;YAAAN,QAAA,GAAC,cAAY,eAAA5E,OAAA;cAAM2E,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpF1E,OAAA;YACEI,EAAE,EAAC,aAAa;YAChBgD,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAErC,QAAQ,CAACK,WAAY;YAC5B8D,QAAQ,EAAEjC,YAAa;YACvBkC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5E,OAAA;YAAOkF,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1E,OAAA;YACEsD,IAAI,EAAC,MAAM;YACXlD,EAAE,EAAC,OAAO;YACVgD,IAAI,EAAC,OAAO;YACZ+B,QAAQ,EAAEjC,YAAa;YACvBsC,MAAM,EAAC;UAAS;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EACD5D,YAAY,iBACXd,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5E,OAAA;cAAKyF,GAAG,EAAE3E,YAAa;cAAC4E,GAAG,EAAC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1E,OAAA;UAAK2E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B5E,OAAA,CAACL,IAAI;YAACgG,EAAE,EAAE,WAAWvF,EAAE,EAAG;YAACuE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnE1E,OAAA;YAAQsD,IAAI,EAAC,QAAQ;YAACqB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA6B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CA/TID,SAAS;EAAA,QACER,SAAS,EACPC,WAAW,EACaG,OAAO;AAAA;AAAA+F,EAAA,GAH5C3F,SAAS;AAiUf,eAAeA,SAAS;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}