{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projetMAI\\\\Gestion_bibliotheque\\\\frontend\\\\src\\\\components\\\\Alert.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Alert.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Alert = ({\n  type = 'info',\n  message,\n  onClose,\n  autoClose = true,\n  autoCloseTime = 5000\n}) => {\n  _s();\n  const [visible, setVisible] = useState(true);\n  useEffect(() => {\n    if (autoClose && message) {\n      const timer = setTimeout(() => {\n        setVisible(false);\n        if (onClose) onClose();\n      }, autoCloseTime);\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, autoCloseTime, message, onClose]);\n  if (!message || !visible) return null;\n  const handleClose = () => {\n    setVisible(false);\n    if (onClose) onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `alert alert-${type}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert-content\",\n      children: [type === 'success' && /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-check-circle alert-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 32\n      }, this), type === 'error' && /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-circle alert-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 30\n      }, this), type === 'warning' && /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-triangle alert-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 32\n      }, this), type === 'info' && /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-info-circle alert-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"alert-message\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"alert-close\",\n      onClick: handleClose,\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-times\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Alert, \"+l2LUTlfMHnsWfc+O34BMiR0NHk=\");\n_c = Alert;\nexport default Alert;\nvar _c;\n$RefreshReg$(_c, \"Alert\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "type", "message", "onClose", "autoClose", "autoCloseTime", "_s", "visible", "setVisible", "timer", "setTimeout", "clearTimeout", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projetMAI/Gestion_bibliotheque/frontend/src/components/Alert.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Alert.css';\n\nconst Alert = ({ type = 'info', message, onClose, autoClose = true, autoCloseTime = 5000 }) => {\n  const [visible, setVisible] = useState(true);\n\n  useEffect(() => {\n    if (autoClose && message) {\n      const timer = setTimeout(() => {\n        setVisible(false);\n        if (onClose) onClose();\n      }, autoCloseTime);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, autoCloseTime, message, onClose]);\n\n  if (!message || !visible) return null;\n\n  const handleClose = () => {\n    setVisible(false);\n    if (onClose) onClose();\n  };\n\n  return (\n    <div className={`alert alert-${type}`}>\n      <div className=\"alert-content\">\n        {type === 'success' && <i className=\"fas fa-check-circle alert-icon\"></i>}\n        {type === 'error' && <i className=\"fas fa-exclamation-circle alert-icon\"></i>}\n        {type === 'warning' && <i className=\"fas fa-exclamation-triangle alert-icon\"></i>}\n        {type === 'info' && <i className=\"fas fa-info-circle alert-icon\"></i>}\n        <p className=\"alert-message\">{message}</p>\n      </div>\n      <button className=\"alert-close\" onClick={handleClose}>\n        <i className=\"fas fa-times\"></i>\n      </button>\n    </div>\n  );\n};\n\nexport default Alert;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI,GAAG,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS,GAAG,IAAI;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAIO,SAAS,IAAIF,OAAO,EAAE;MACxB,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BF,UAAU,CAAC,KAAK,CAAC;QACjB,IAAIL,OAAO,EAAEA,OAAO,CAAC,CAAC;MACxB,CAAC,EAAEE,aAAa,CAAC;MAEjB,OAAO,MAAMM,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACL,SAAS,EAAEC,aAAa,EAAEH,OAAO,EAAEC,OAAO,CAAC,CAAC;EAEhD,IAAI,CAACD,OAAO,IAAI,CAACK,OAAO,EAAE,OAAO,IAAI;EAErC,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBJ,UAAU,CAAC,KAAK,CAAC;IACjB,IAAIL,OAAO,EAAEA,OAAO,CAAC,CAAC;EACxB,CAAC;EAED,oBACEJ,OAAA;IAAKc,SAAS,EAAE,eAAeZ,IAAI,EAAG;IAAAa,QAAA,gBACpCf,OAAA;MAAKc,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3Bb,IAAI,KAAK,SAAS,iBAAIF,OAAA;QAAGc,SAAS,EAAC;MAAgC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACxEjB,IAAI,KAAK,OAAO,iBAAIF,OAAA;QAAGc,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC5EjB,IAAI,KAAK,SAAS,iBAAIF,OAAA;QAAGc,SAAS,EAAC;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAChFjB,IAAI,KAAK,MAAM,iBAAIF,OAAA;QAAGc,SAAS,EAAC;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEnB,OAAA;QAAGc,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEZ;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACNnB,OAAA;MAAQc,SAAS,EAAC,aAAa;MAACM,OAAO,EAAEP,WAAY;MAAAE,QAAA,eACnDf,OAAA;QAAGc,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACZ,EAAA,CAnCIN,KAAK;AAAAoB,EAAA,GAALpB,KAAK;AAqCX,eAAeA,KAAK;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}